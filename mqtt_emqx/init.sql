-- Create mqtt_users table
CREATE TABLE mqtt_users (
                            id SERIAL PRIMARY KEY,
                            username VARCHAR(100) NOT NULL UNIQUE,
                            password_hash VARCHAR(100) NOT NULL,
                            is_superuser BOOLEAN DEFAULT FALSE,
                            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                            last_login TIMESTAMP WITH TIME ZONE,
                            status BOOLEAN DEFAULT TRUE -- active/inactive flag
);

-- Create mqtt_acl table for access control
CREATE TABLE mqtt_acl (
                          id SERIAL PRIMARY KEY,
                          allow INTEGER DEFAULT 1, -- 1: allow, 0: deny
                          ipaddr VARCHAR(60), -- NULL means all
                          username VARCHAR(100), -- NULL means all
                          clientid VARCHAR(100), -- NULL means all
                          access INTEGER DEFAULT 1, -- 1: subscribe, 2: publish, 3: both
                          topic VARCHAR(100), -- MQTT topic
                          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON>reate indexes for better performance
CREATE INDEX idx_mqtt_users_username ON mqtt_users(username);
CREATE INDEX idx_mqtt_acl_username ON mqtt_acl(username);
CREATE INDEX idx_mqtt_acl_clientid ON mqtt_acl(clientid);

-- Insert a default superuser (password: public_admin - bcrypt hashed)
INSERT INTO mqtt_users (username, password_hash, is_superuser)
VALUES ('admin', '$2a$12$8Kf1vNW5yXjB4eHIF0tdu.qO2sG9j7.h8pBjXWkP8d7Ap/qXTCE12', true);

-- Insert some example ACL rules
INSERT INTO mqtt_acl (allow, username, topic, access) VALUES
                                                          (1, 'admin', '#', 3),  -- admin can publish and subscribe to all topics
                                                          (1, NULL, 'public/#', 1),  -- everyone can subscribe to public topics
                                                          (1, NULL, '$SYS/#', 1);    -- everyone can subscribe to system topics

-- Function to update last_login timestamp
CREATE OR REPLACE FUNCTION update_last_login()
RETURNS TRIGGER AS $$
BEGIN
UPDATE mqtt_users
SET last_login = CURRENT_TIMESTAMP
WHERE username = NEW.username;
RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update last_login on successful authentication
CREATE TRIGGER auth_timestamp
    AFTER INSERT ON mqtt_users
    FOR EACH ROW
    EXECUTE FUNCTION update_last_login();