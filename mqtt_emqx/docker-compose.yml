version: '3.8'

services:
  # Main MQTT Broker
  emqx:
    image: emqx/emqx:5.8.4
    container_name: emqx
    ports:
      - "1884:1883"      # MQTT port
      - "8073:8083"      # MQTT/WebSocket port
      - "8074:8084"      # MQTT/WSS port
      - "8873:8883"      # MQTT/SSL port
      - "18073:18083"    # Dashboard port with built-in WebSocket client
    environment:
      - EMQX_NAME=emqx
      - EMQX_HOST=node1.emqx.io
      # Dashboard Configuration
      - EMQX_DASHBOARD__DEFAULT_USERNAME=admin
      - EMQX_DASHBOARD__DEFAULT_PASSWORD=public
      # Logging Configuration
      - EMQX_LOG__CONSOLE__LEVEL=debug
      - EMQX_LOG__FILE__LEVEL=debug
#    volumes:
#      - emqx-data:/opt/emqx/data
#      - emqx-log:/opt/emqx/log
    networks:
      - mqtt_network
    restart: always
#    labels:
#      com.datadoghq.ad.check_names: '["emqx"]'
#      com.datadoghq.ad.init_configs: '[{}]'
#      com.datadoghq.ad.instances: '[{"url": "http://%%host%%:18083/api/v5/prometheus/stats", "username": "admin", "password": "public"}]'

  # Monitoring agent
#  datadog:
#    image: gcr.io/datadoghq/agent:latest
#    container_name: datadog-agent
#    environment:
#      - DD_API_KEY=${DD_API_KEY}
#      - DD_SITE=${DD_SITE:-datadoghq.com}
#      - DD_LOGS_ENABLED=true
#      - DD_LOGS_CONFIG_CONTAINER_COLLECT_ALL=true
#      - DD_CONTAINER_EXCLUDE="name:datadog-agent"
#    volumes:
#      - /var/run/docker.sock:/var/run/docker.sock:ro
#      - /proc/:/host/proc/:ro
#      - /sys/fs/cgroup/:/host/sys/fs/cgroup:ro
#      - /var/lib/docker/containers:/var/lib/docker/containers:ro
#    networks:
#      - mqtt-net
#    depends_on:
#      - emqx
#    restart: always

  redis:
    image: "redis:alpine"
    container_name: robot_redis
    restart: always
    command: redis-server --requirepass @Khiem28121993
    ports:
      - "6373:6379"
    environment:
      - REDIS_REPLICATION_MODE=master
    depends_on:
      - emqx
    networks:
      - mqtt_network

networks:
  mqtt_network:
    driver: bridge