# Use the base image
FROM iegomez/mosquitto-go-auth:2.1.0-mosquitto_1.6.14

# Copy your configuration file into the container
COPY mosquitto/config/mosquitto.conf /etc/mosquitto/mosquitto.conf

# Make sure config file has correct permissions
RUN chown mosquitto:mosquitto /etc/mosquitto/mosquitto.conf && \
    chmod 644 /etc/mosquitto/mosquitto.conf

# Expose the necessary ports
EXPOSE 1883 9001

# Command to run the Mosquitto server
CMD ["/usr/sbin/mosquitto", "-c", "/etc/mosquitto/mosquitto.conf"]


# Build the custom docker image
#docker build -t ngocquan/mosquitto-custom:1.0 .
# Push to docker hub
# docker push ngocquan/mosquitto-custom:1.0


