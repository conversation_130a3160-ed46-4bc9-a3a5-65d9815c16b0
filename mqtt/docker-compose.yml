version: '3.8'

services:
  mosquitto:
    image: ngocquan/mosquitto-custom:1.0
    container_name: mosquitto
    ports:
      - "1884:1883"    # MQTT
      - "9003:9001"    # WebSocket
#    volumes:
#      - ./mosquitto/config/mosquitto.conf:/etc/mosquitto/mosquitto.conf
    networks:
      - mqtt_network

  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9093:9090"
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
    networks:
      - mqtt_network
    depends_on:
      - mosquitto

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3003:3000"
    networks:
      - mqtt_network
    depends_on:
      - prometheus

  redis:
    image: "redis:alpine"
    container_name: robot_redis
    restart: always
    command: redis-server --requirepass @Khiem28121993
    ports:
      - "6373:6379"
    environment:
      - REDIS_REPLICATION_MODE=master
    depends_on:
      - mosquitto
    networks:
      - mqtt_network

networks:
  mqtt_network:
    driver: bridge
