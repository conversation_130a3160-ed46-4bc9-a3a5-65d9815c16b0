# Plain MQTT protocol
listener 1883
protocol mqtt

# Plain WebSockets configuration
listener 8000
protocol websockets

auth_plugin /mosquitto/go-auth.so
auth_opt_log_level debug

auth_opt_backends redis,postgres
auth_opt_check_prefix false
allow_anonymous false

auth_opt_pg_host *************
auth_opt_pg_port 8089
auth_opt_pg_dbname postgres
auth_opt_pg_user postgres
auth_opt_pg_password 123

auth_opt_pg_connect_tries 5
auth_opt_pg_sslmode disable

# -- 1: Read -  4: Subscribe - 2: Write - 999: all permissions
auth_opt_pg_userquery SELECT "password" FROM "mqtt_users" WHERE "username" = $1 and "is_active" = true limit 1
auth_opt_pg_aclquery SELECT "topic" FROM "mqtt_acls" acl JOIN "mqtt_users" mu on acl."mqtt_user_id" = mu.id WHERE mu."username" = $1 and (acl.role = $2 or acl.role = 999)
auth_opt_pg_superquery SELECT count(*) FROM "mqtt_users" WHERE "username" = $1 and "is_admin" = true


# Redis connection parameters
auth_opt_redis_host *************
auth_opt_redis_port 6373
auth_opt_redis_password @Khiem28121993
auth_opt_redis_db 2
auth_opt_redis_disable_superuser false
auth_opt_redis_connect_timeout 3
# Redis mode 'single' means it will only use Redis as a backend, not as cache.
auth_opt_redis_mode single
# The %s will be replaced by the username in auth queries, and the username in ACL queries.
auth_opt_redis_userquery GET %s:auth

# Hasher
auth_opt_hasher bcrypt
auth_opt_hasher_cost 10

# Enable syslog logging
log_dest syslog

# Optionally, you can specify log_type to get more specific logs:
log_type all    # Logs all types, including connection logs, message flow, errors, etc.
