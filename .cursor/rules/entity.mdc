---
description: Use when creating or editing entity classes which annotated with @Entity
globs:
alwaysApply: false
---
# Spring Boot Entity Best Practices

## Entity Structure
1. Each entity class must:
   - Be annotated with `@Entity`
   - Have a proper `@Table` annotation specifying the table name
   - Extend `ModifierTrackingEntity` if it needs creation/update timestamps
   - Use appropriate Lombok annotations:
     - `@Data`
     - `@NoArgsConstructor`
     - `@AllArgsConstructor`
     - `@Builder`
     - `@EqualsAndHashCode(callSuper = false)` when extending other classes

## ID Field Requirements
1. Every entity must have an ID field annotated with `@Id`
2. Prefer using UUID instead of auto-incremented IDs with:
   ```java
   @Id
   @Column(name = "id", columnDefinition = "VARCHAR(255)")
   @GeneratedValue(generator = "UUID")
   @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
   private String id;
   ```

## Entity Relationship Best Practices
1. **IMPORTANT**: Use Foreign Keys Instead of JPA Relationships
   - Favor scalar fields (e.g. userId, topicId) to represent relationships instead of using JPA annotations like @ManyToOne, @OneToMany, etc.
   - Do not declare relationships directly in entity classes unless there's a strong need.
   - Fetch related entities manually in the service or repository layer via separate queries or joins.

2. When relationships must be defined in entities:
   - Use `@OneToMany`, `@ManyToOne`, `@OneToOne`, or `@ManyToMany` appropriately
   - Always specify `fetch = FetchType.LAZY` for collection relationships to avoid N+1 problems
   - Define cascade types explicitly, avoid `CascadeType.ALL` unless necessary
   - Use `@JoinColumn` with explicit column names

## Property Annotations
1. All columns must be properly annotated:
   - Use `@Column` with explicit `name` attribute matching database column name
   - Specify `nullable = false` for required fields
   - Use appropriate `columnDefinition` for special types (e.g., `"TEXT"` for long strings)

2. **IMPORTANT**: Use appropriate validation annotations:
   - `@NotNull`: For non-nullable primitive wrappers
   - `@NotBlank` or `@NotEmpty`: For string fields that can't be empty
   - `@Size(min = x, max = y)`: For strings with length constraints
   - `@Email`: For email fields
   - `@Pattern`: For strings requiring specific patterns
   - `@Min`/`@Max`: For numeric constraints

3. Use `@Enumerated(EnumType.STRING)` for enum fields to store the name instead of ordinal

## Example Entity
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "users")
public class User extends ModifierTrackingEntity {
    @Id
    @Column(name = "id", columnDefinition = "VARCHAR(255)")
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;

    @NotBlank(message = "Username cannot be blank")
    @Size(min = 3, max = 50, message = "Username must be between 3 and 50 characters")
    @Column(name = "username", nullable = false, unique = true)
    private String username;

    @Email(message = "Invalid email format")
    @NotBlank(message = "Email cannot be blank")
    @Column(name = "email", nullable = false, unique = true)
    private String email;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private UserStatus status;

    // Avoid direct relationship declarations when possible
    // Instead, create a separate UserProfile entity and reference by ID
    @Column(name = "profile_id")
    private String profileId;
}