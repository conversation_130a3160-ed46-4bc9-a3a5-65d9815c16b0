---
description: Use when creating or editing service classes
globs:
alwaysApply: false
---
# Spring Boot Service Class Best Practices

## Dependency Injection
- **Always use constructor injection** instead of field injection
- Avoid `@Autowired` on fields

## Data Transfer Object (DTO) Usage
- Service methods must return DTOs, not entity classes
- All parameters from controllers must be DTOs, not entities
- Use Java record types for DTOs where appropriate for immutability
- Only return entities when absolutely necessary (with documented reason)

## Error Handling
- Use `.orElseThrow()` with appropriate exception for checking record existence
- Create and use custom exceptions for different error scenarios
- Always provide meaningful error messages

## Transaction Management
- Use `@Transactional` for methods performing multiple database operations
- For more complex transaction scenarios, use `TransactionTemplate`
- Specify read-only attribute when appropriate: `@Transactional(readOnly = true)`

## Repository Interaction
- Services must interact with database exclusively through Repository interfaces
- Do not inject or use EntityManager directly unless absolutely necessary
- If EntityManager is needed, document the specific reason

## DTO-Entity Mapping
- Mapping logic between Entities and DTOs belongs in the service layer
- Use a consistent mapping approach throughout the service
- Preferred mapping approaches:
  - MapStruct for complex mapping needs
  - ObjectMapper for JSON/Object conversions
  - Manual mapping for simple cases or performance-critical code

## Method Naming
- Use clear, descriptive method names that reflect their purpose
- Follow verb-noun pattern (e.g., `createUser`, `findUserById`, `updateUserProfile`)
- Avoid generic names like `process`, `handle`, or `manage`

## Redisson Usage
- Use `RMapCache`, `RBucket`, or other Redisson data structures for caching service-layer data, with clear and consistent Redis key naming (e.g., constants).
- Always check the cache before querying the database.
- Use try-catch blocks for all cache (de)serialization operations. Log errors at the appropriate level, but never log sensitive data.
- If cache (de)serialization fails, remove the corrupted cache entry asynchronously using `removeAsync`.
- On cache miss, fetch the data from the repository, then repopulate the cache using `putAsync` with a sensible expiration (e.g., 30 minutes).
- Prefer async Redisson methods (`putAsync`, `removeAsync`) for non-blocking cache operations.
- Set explicit cache expiration times to avoid stale data.
- Document any deviation from these practices, especially if using direct Redis commands or synchronous methods.