---
description: Use when creating or editing controller classes
globs:
alwaysApply: false
---
# Spring Boot Controller Conventions

## Controller Structure

1. Controllers should extend `BaseController` to inherit common response handling methods.
2. Every controller class must be annotated with:

   - `@RestController` - Indicates this is a REST controller
   - `@CrossOrigin("*")` - Enables cross-origin requests
   - `@RequestMapping("**/api/v1/{resource}")` - Defines the base path using consistent prefix and versioning

3. Controllers should follow a resource-based naming convention: `{Resource}Controller.java`

## Endpoint Guidelines

1. Path parameters should use RESTful conventions:

   - Collection endpoints: `/resources`
   - Specific resource: `/resources/{id}`
   - Sub-resources: `/resources/{id}/sub-resources`

2. Request validation:

   - Use `@Valid` annotation with `@RequestBody` for DTO validation
   - DTOs should follow the naming convention: `{Resource}{Action}ReqDTO` or `{Resource}{Action}RespDTO`

3. Method documentation:
   - Include a comment above each endpoint describing its purpose
   - Document expected request/response behavior

## Dependencies

1. Controllers should use constructor injection for required services
2. Keep business logic in services, controllers should only handle:
   - Request validation
   - Calling appropriate services
   - Returning appropriate responses