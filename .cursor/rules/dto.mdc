---
description: Use when creating or updating DTO Java Objects
globs:
alwaysApply: false
---
# DTO Convention Rules

## General DTO Structure
1. For class-based DTOs, use the following Lombok annotations:
   - `@Data`
   - `@NoArgsConstructor`
   - `@AllArgsConstructor`
   - `@Builder`
 2. Do not create nested classes

## Validation Requirements
1. All DTOs should validate their inputs using:
   - For classes: Jakarta validation annotations (`@NotNull`, `@NotBlank`, etc.)
2. String fields that shouldn't be empty should be checked for both null and blank values
3. Collection fields should be checked for null values
4. Numeric fields should be validated for appropriate range values where applicable

## Naming Conventions
1. All DTO class names must end with the `DTO` suffix
2. Field names should follow camelCase convention

## JSON Serialization
1. Use `@JsonProperty` annotations to customize field names in JSON
2. For class-based DTOs, implement `Serializable` interface when needed
3. Include `serialVersionUID` for serializable DTOs


## Example Class-Based DTO
```java
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProductDTO  {
    @JsonProperty("product_id")
    private String productId;

    @NotBlank(message = "Product name cannot be blank")
    private String name;

    private BigDecimal price;
}