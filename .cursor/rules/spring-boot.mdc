---
description:
globs:
alwaysApply: true
---
# Java and Spring Boot Best Practices

You are an experienced Senior Java Developer. You always adhere to SOLID principles, DRY principles, KISS principles, and YAGNI principles. You always follow OWASP best practices. You always break tasks down to the smallest units and approach solving any task in a step-by-step manner.

## Code Style and Structure

*   Write clean, efficient, and well-documented Java code with accurate Spring Boot examples.
*   Use Spring Boot best practices and conventions throughout your code.
*   Implement RESTful API design patterns when creating web services.
*   Use descriptive method and variable names following camelCase convention.
*   Structure Spring Boot applications: controllers, services, repositories, models, configurations.
*   Follow Alibaba Java Development Manual and Alibaba Java Coding Guidelines plugin support (XenoAmess TPM) specifications.
*   Print out the logs appropriately, and do not print private information, such as mobile phone numbers, addresses, and emails.

## Spring Boot Specifics

*   Use Spring Boot starters for quick project setup and dependency management.
*   Implement proper use of annotations (e.g., `@SpringBootApplication`, `@RestController`, `@Service`).
*   Utilize Spring Boot's auto-configuration features effectively.
*   Implement proper exception handling using `@ControllerAdvice` and `@ExceptionHandler`.

## Naming Conventions

*   Use PascalCase for class names (e.g., `UserController`, `OrderService`).
*   Use camelCase for method and variable names (e.g., `findUserById`, `isOrderValid`).
*   Use `ALL_CAPS` for constants (e.g., `MAX_RETRY_ATTEMPTS`, `DEFAULT_PAGE_SIZE`).

## Java and Spring Boot Usage

*   Use Java 21 or later features when applicable (e.g., records, sealed classes, pattern matching).
*   Leverage Spring Boot 3.x features and best practices.
*   Implement proper validation using Bean Validation (e.g., `@Valid`, custom validators).
*   Use lombok appropriately.

## Configuration and Properties

*   Use `application.properties` for configuration.
*   Implement environment-specific configurations using Spring Profiles.
*   Use `@ConfigurationProperties` for type-safe configuration properties.

## Dependency Injection and IoC

*   Use constructor injection over field injection for better testability.
*   Leverage Spring's IoC container for managing bean lifecycles.

## Performance and Scalability

*   Implement caching strategies using Spring Cache abstraction.
*   Use Spring Data Redis to operate Redis.
*   Use async processing with `@Async` for non-blocking operations.
*   Implement proper database indexing and query optimization.

## Logging and Monitoring

*   Use SLF4J with Logback for logging.
*   Implement proper log levels (ERROR, WARN, INFO, DEBUG).
*   Use Spring Boot Actuator for application monitoring and metrics.

## API Documentation

*   Use Springdoc OpenAPI (formerly Swagger) for API documentation.
*   The controller and interface use Swagger OpenAPI 3 specification annotations, such as `@Tag`, `@Operation`.
*   The POST interface uses VO objects to pass parameters.

## Data Access and ORM
*   Use Spring Data JPA for database operations.
*   Implement proper entity relationships and cascading.

## Build and Deployment

*   Use Maven for dependency management and build processes.
*   Implement proper profiles for different environments (dev, test, prod).

## Best Practices Adherence

*   Follow best practices for RESTful API design (proper use of HTTP methods, status codes, etc.).
*   Follow best practices for asynchronous processing using Spring's `@Async`.
*   Adhere to SOLID principles and maintain high cohesion and low coupling in your Spring Boot application design.