package com.stepup.springrobot.util;

import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * Simple standalone class to generate and test AES keys
 * Run this class directly to generate a key for your application
 */
public class KeyGeneratorSimpleTest {

    private static final String KEY_ALGORITHM = "AES";
    private static final int KEY_SIZE = 256;

    public static void main(String[] args) {
        try {
            // Generate a new key
            SecretKey key = generateKey();
            System.out.println("Key algorithm: " + key.getAlgorithm());
            System.out.println("Key size: " + (key.getEncoded().length * 8) + " bits");
            
            // Encode the key to Base64 for storage
            String encodedKey = Base64.getEncoder().encodeToString(key.getEncoded());
            System.out.println("\nGenerated AES Key (Base64):");
            System.out.println(encodedKey);
            
            // Demonstrate how to restore the key from Base64
            SecretKey restoredKey = restoreKeyFromBase64(encodedKey);
            System.out.println("\nRestored key matches original: " + key.equals(restoredKey));
            
            // Usage instructions
            System.out.println("\n----- How to use this key in your code -----");
            System.out.println("// To use this key, add this code to your application:");
            System.out.println("String encodedKeyString = \"" + encodedKey + "\";");
            System.out.println("byte[] decodedKey = Base64.getDecoder().decode(encodedKeyString);");
            System.out.println("SecretKey key = new SecretKeySpec(decodedKey, 0, decodedKey.length, \"AES\");");
        } catch (Exception e) {
            System.err.println("Error generating key: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Generate a new AES key
     */
    private static SecretKey generateKey() throws NoSuchAlgorithmException {
        KeyGenerator keyGenerator = KeyGenerator.getInstance(KEY_ALGORITHM);
        SecureRandom secureRandom = new SecureRandom();
        keyGenerator.init(KEY_SIZE, secureRandom);
        return keyGenerator.generateKey();
    }
    
    /**
     * Restore a SecretKey from a Base64 encoded string
     */
    private static SecretKey restoreKeyFromBase64(String encodedKey) {
        byte[] decodedKey = Base64.getDecoder().decode(encodedKey);
        return new SecretKeySpec(decodedKey, 0, decodedKey.length, KEY_ALGORITHM);
    }
} 