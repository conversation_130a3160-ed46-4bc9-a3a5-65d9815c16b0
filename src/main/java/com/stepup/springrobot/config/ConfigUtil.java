package com.stepup.springrobot.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

@Data
@Service
public class ConfigUtil {
    public static ConfigUtil INSTANCE;

    @Value("${hostname_wp_alert_system}")
    private String HOSTNAME_WP_ALERT_SYSTEM;

    @Value("${path_wp_alert_system_critical}")
    private String PATH_WP_ALERT_SYSTEM_CRITICAL;

    @Value("${cdn_domain}")
    private String cdnDomain;

    @Value("${s3.aws.bucket}")
    private String s3Bucket;

    @PostConstruct
    public void init() {
        INSTANCE = this;
    }
}
