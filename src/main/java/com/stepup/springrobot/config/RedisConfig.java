package com.stepup.springrobot.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisPassword;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.StringRedisTemplate;

@Configuration
public class RedisConfig {
    @Value("${spring.redis.host}")
    private String redisHostName;

    @Value("${spring.redis.port}")
    private Integer redisPort;

    @Value("${spring.redis.password}")
    private String redisPassword;

    @Bean
    public LettuceConnectionFactory redisConnectionFactoryDB2() {
        RedisStandaloneConfiguration redisConfig = new RedisStandaloneConfiguration(redisHostName, redisPort);
        redisConfig.setPassword(RedisPassword.of(redisPassword));
        redisConfig.setDatabase(2);

        return new LettuceConnectionFactory(redisConfig);
    }

    @Bean(name = "redisTemplateDB2")
    public StringRedisTemplate redisTemplateDB2(@Qualifier("redisConnectionFactoryDB2") LettuceConnectionFactory redisConnectionFactoryDB2) {
        StringRedisTemplate template = new StringRedisTemplate();
        template.setConnectionFactory(redisConnectionFactoryDB2);
        return template;
    }
}
