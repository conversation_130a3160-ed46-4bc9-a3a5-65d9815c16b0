package com.stepup.springrobot.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.*;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Configuration
public class SwaggerOpenAPIConfig {
    @Value("${swagger.openapi.prod-url}")
    private String productionUrl;

    @Value("${swagger.openapi.staging-url}")
    private String stagingUrl;

    @Value("${swagger.openapi.dev-url}")
    private String developmentUrl;

    @Value("${swagger.openapi.local-url}")
    private String localUrl;

    private ApiKey apiKey() {
        return new ApiKey("Authentication", "Authorization", "header");
    }

    private springfox.documentation.spi.service.contexts.SecurityContext securityContext() {
        return springfox.documentation.spi.service.contexts.SecurityContext.builder()
                .securityReferences(defaultAuth())
                .forPaths(PathSelectors.regex("^(?!/(robot-user/api/v1/auth/login|robot-user/api/v1/auth/refresh-token)).*$"))
                .build();
    }

    private List<SecurityReference> defaultAuth() {
        AuthorizationScope authorizationScope = new AuthorizationScope("global", "accessEverything");
        AuthorizationScope[] authorizationScopes = new AuthorizationScope[1];
        authorizationScopes[0] = authorizationScope;
        return Collections.singletonList(new SecurityReference("JWT", authorizationScopes));
    }

    @Bean
    public Docket api() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .securityContexts(Arrays.asList(securityContext()))
                .securitySchemes(Arrays.asList(apiKey()))
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.stepup.springrobot.controller"))
                .paths(PathSelectors.any())
                .build();
    }

    private ApiInfo apiInfo() {
        Contact contact = new Contact("ThanhDT", "", "<EMAIL>");

        return new ApiInfoBuilder()
                .title("Robot Management API")
                .description("This API exposes endpoints to manage Robot Project. Để sử dụng JWT token, vui lòng nhập token với tiền tố 'Bearer ' vào ô Authorization.")
                .version("1.0")
                .contact(contact)
                .build();
    }
}