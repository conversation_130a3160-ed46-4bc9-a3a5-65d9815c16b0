package com.stepup.springrobot.config;

import org.apache.http.HttpHost;
import org.apache.http.client.AuthCache;
import org.apache.http.client.HttpClient;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.impl.auth.DigestScheme;
import org.apache.http.impl.client.BasicAuthCache;
import org.apache.http.protocol.BasicHttpContext;
import org.apache.http.protocol.HttpContext;
import org.springframework.http.HttpMethod;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;

import java.net.URI;
import java.util.Random;

public class HttpComponentsClientHttpRequestFactoryDigestAuth extends HttpComponentsClientHttpRequestFactory {
    HttpHost host;

    public HttpComponentsClientHttpRequestFactoryDigestAuth(final HttpHost host, final HttpClient httpClient) {
        super(httpClient);
        this.host = host;
    }

    @Override
    protected HttpContext createHttpContext(final HttpMethod httpMethod, final URI uri) {
        return createHttpContext();
    }

    private HttpContext createHttpContext() {
        // Create AuthCache instance
        final AuthCache authCache = new BasicAuthCache();
        // Generate DIGEST scheme object, initialize it and add it to the local auth cache
        final DigestScheme digestAuth = new DigestScheme();

        digestAuth.overrideParamter("algorithm", "MD5");
        digestAuth.overrideParamter("realm", "serverRealm");
        digestAuth.overrideParamter("nonce", Long.toString(new Random().nextLong(), 36));
        digestAuth.overrideParamter("qop", "auth");//   not effective
        digestAuth.overrideParamter("nc",""+1);//nt effective
        digestAuth.overrideParamter("nonce", DigestScheme.createCnonce());
        digestAuth.overrideParamter("opaque","ba897c2f0f3de9c6f52d");
        String err;
        try {
            digestAuth.processChallenge(null);
            //force  qop in use  chalange  on return header ????!!!!
        } catch (Exception e) {
            err=e.getLocalizedMessage();
        }

        authCache.put(host, digestAuth);

        // Add AuthCache to the execution context
        final BasicHttpContext localcontext = new BasicHttpContext();
        localcontext.setAttribute(HttpClientContext.AUTH_CACHE, authCache);
        return localcontext;
    }

}