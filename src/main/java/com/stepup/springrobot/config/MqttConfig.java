package com.stepup.springrobot.config;

import lombok.extern.log4j.Log4j2;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.annotation.IntegrationComponentScan;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.integration.mqtt.core.DefaultMqttPahoClientFactory;
import org.springframework.integration.mqtt.core.MqttPahoClientFactory;
import org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter;
import org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.support.MessageBuilder;

@Log4j2
@Configuration
@IntegrationComponentScan
public class MqttConfig {
    @Value("${mqtt_broker_url}")
    private String brokerUrl ;

    @Value("${mqtt_client_id}")
    private String clientId;

    @Value("${mqtt_username}")
    private String username;

    @Value("${mqtt_password}")
    private String password;

    private MqttPahoMessageDrivenChannelAdapter adapter;

    // Configure the MQTT Client Factory
    // Define the MQTT Client Factory
    @Bean
    public MqttPahoClientFactory mqttClientFactory() {
        DefaultMqttPahoClientFactory factory = new DefaultMqttPahoClientFactory();
        MqttConnectOptions options = new MqttConnectOptions();
        options.setServerURIs(new String[]{brokerUrl});  // Mosquitto broker URL
        options.setUserName(username);  // Replace with your MQTT user
        options.setPassword(password.toCharArray());  // Replace with your MQTT password
        options.setConnectionTimeout(30);
        options.setKeepAliveInterval(60);
        options.setAutomaticReconnect(true);

        factory.setConnectionOptions(options);
        return factory;
    }

    // Define the MQTT Outbound Handler (for publishing messages)
    @Bean
    public MqttPahoMessageHandler messageHandler() {
        MqttPahoMessageHandler messageHandler = new MqttPahoMessageHandler(clientId + "-outbound", mqttClientFactory());
        messageHandler.setDefaultTopic("my/topic");  // Default topic to publish to
        return messageHandler;
    }

    // Define the channel for publishing messages
    @Bean
    public MessageChannel mqttOutboundChannel() {
        return new DirectChannel();
    }

    // Define an Activator to send messages to dynamic topics
    @ServiceActivator(inputChannel = "mqttOutboundChannel")
    public void sendMessage(@Payload String message, @Header(MqttHeaders.TOPIC) String topic) {
        try {
            Message<String> mqttMessage = MessageBuilder
                    .withPayload(message)
                    .setHeader(MqttHeaders.TOPIC, topic)
                    .setHeader(MqttHeaders.QOS, 1)  // Set QoS level
                    .build();

            messageHandler().handleMessage(mqttMessage);
            log.info("Publish message: {} to topic: {}", message, topic);

        } catch (Exception e) {
            log.error("Failed to publish message: {} to topic: {}. Error: {}",
                    message, topic, e.getMessage(), e);
        }
    }

    // Define the MQTT Inbound Adapter (for subscribing to topics)
    @Bean
    public MqttPahoMessageDrivenChannelAdapter messageDrivenChannelAdapter() {
        String[] topics = new String[]{"my/topic"};
        adapter = new MqttPahoMessageDrivenChannelAdapter(clientId + "-inbound", mqttClientFactory(), topics);
        adapter.setOutputChannel(mqttInboundChannel());
        return adapter;
    }

    // Define the channel for receiving messages
    @Bean
    public MessageChannel mqttInboundChannel() {
        return new DirectChannel();
    }

    @ServiceActivator(inputChannel = "mqttInboundChannel")
    public void handleMessage(@Payload String message, @Header(MqttHeaders.RECEIVED_TOPIC) String topic) {
        log.info("Received message: " + message + " from topic: " + topic);
    }

    // New method to add subscription to a topic at runtime
    public void addNewSubscription(String newTopic) {
        try {
            adapter.addTopic(newTopic);
            log.info("Successfully subscribed to new topic: " + newTopic);
        } catch (Exception e) {
            log.error("Error subscribing to topic: {}, error: {}", newTopic, e.getMessage(), e);
        }
    }

    // New method to remove subscription from a topic at runtime
    public void removeSubscription(String topic) {
        try {
            adapter.removeTopic(topic);
            log.info("Successfully unsubscribed from topic: " + topic);
        } catch (Exception e) {
            log.error("Error unsubscribing from topic: " + topic, e);
        }
    }
}
