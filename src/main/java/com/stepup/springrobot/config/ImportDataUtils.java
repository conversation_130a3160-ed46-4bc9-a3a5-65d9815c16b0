package com.stepup.springrobot.config;

import com.google.api.client.auth.oauth2.Credential;
import com.google.api.client.googleapis.auth.oauth2.GoogleCredential;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.services.sheets.v4.SheetsScopes;

import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

public class ImportDataUtils {
    private static final List<String> SCOPES = Collections.singletonList(SheetsScopes.SPREADSHEETS);
    private static final String CREDENTIALS_FILE_PATH = "/cmsmkt-1601868101732-899297121cb2.json";

    public static Credential getCredentials(final NetHttpTransport HTTP_TRANSPORT, final JsonFactory jsonFactory) throws IOException {
        // Load client secrets.
        return GoogleCredential.fromStream(Objects.requireNonNull(ImportDataUtils.class.getResourceAsStream(CREDENTIALS_FILE_PATH)), HTTP_TRANSPORT, jsonFactory)
                .createScoped(SCOPES);
    }

    public static String normalizeData(String data) {
        if (data == null) return null;

        return data.trim().toLowerCase();
    }

    public static String trimData(String data) {
        if (data == null) return null;

        return data.trim();
    }

    public static String getRequiredField(List<Object> values, int index) throws Exception {
        try {
            return (String) values.get(index);
        } catch (IndexOutOfBoundsException ex) {
            throw new Exception("Dữ liệu này là bắt buộc " + index + ". " + ex.getMessage());
        }
    }

    public static String[] getRequiredFieldSplitToArray(List<Object> values, int index, String charSplit) throws Exception {
        try {
            return values.get(index).toString().split(charSplit);
        } catch (IndexOutOfBoundsException ex) {
            throw new Exception("Dữ liệu này là bắt buộc " + index + ". " + ex.getMessage());
        }
    }

    private static String removeLastSpecialCharacter(String str) {
        if (str.charAt(str.length() - 1) == '.') {
            return str.substring(0, str.length() - 1);
        }

        return str;
    }

    public static String getNullableField(List<Object> values, int index) {
        try {
            var value = values.get(index);
            if (value == null) return null;
            return (String) value;
        } catch (IndexOutOfBoundsException ex) {
            return null;
        }
    }

    public static String getValueOrDefault(List<Object> values, int index, String defaultValue) {
        try {
            var value = values.get(index);
            if (value == null) return defaultValue;
            return (String) value;
        } catch (IndexOutOfBoundsException ex) {
            return null;
        }
    }
}
