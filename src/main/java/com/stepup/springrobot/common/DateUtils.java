package com.stepup.springrobot.common;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Date;

public class DateUtils {
    private DateUtils() {
    }

    public static Date asDate(LocalDate localDate) {
        return Date.from(localDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date getDateFromString(String date) {
        try {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(date);
        } catch (ParseException ignored){
            return DateUtils.asDate(LocalDate.now().minusDays(1));
        }
    }

    // this method will get today in format yyMMdd and then return the integer value of it
    public static Integer getToday() {
        LocalDate date = LocalDate.now();
        return Integer.parseInt(date.format(CodeDefine.DEFAULT_INT_DATE_FORMATTER));
    }

    public static Integer getCurrentYearMonthNumber() {
        LocalDate date = LocalDate.now();
        return Integer.parseInt(date.getYear() + "" + date.getMonthValue());
    }
}
