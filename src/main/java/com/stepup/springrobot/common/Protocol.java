package com.stepup.springrobot.common;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.Serializable;

@Slf4j
public class Protocol implements Serializable {
    private static final long serialVersionUID = 20091803145200L;
    protected String url;
    private String protocol;
    private String ip;
    private String server;
    private int port;

    public Protocol(String url) {
        this.init(url);
    }

    private void init(String url) {
        this.url = url;
        this.protocol = this.parseProtocol();
        this.ip = this.parseIp();
        this.port = this.parsePort();
        this.server = this.parseServer();
    }

    private String parseProtocol() {
        if (this.url != null && this.url.length() != 0) {
            int i = this.url.indexOf("://");
            return i != -1 && i < this.url.length() ? this.url.substring(0, i) : null;
        } else {
            return null;
        }
    }

    private String parseIp() {
        if (this.url != null && this.url.length() != 0) {
            int s = this.url.indexOf("://");
            if (s != -1 && s < this.url.length()) {
                int e = this.url.indexOf(":", s + 1);
                if (e == -1) {
                    e = this.url.indexOf("/", s + 3);
                    if (e == -1) {
                        e = this.url.length();
                    }
                }

                return this.url.substring(s + 3, e);
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    private int parsePort() {
        if (this.url != null && this.url.length() != 0) {
            int s = this.url.indexOf("://");
            if (s != -1 && s < this.url.length()) {
                s = this.url.indexOf(":", s + 1);
                if (s != -1 && s < this.url.length()) {
                    int e = this.url.indexOf("/", s + 1);
                    String parten;
                    if (e == -1) {
                        parten = this.url.substring(s + 1);
                    } else {
                        parten = this.url.substring(s + 1, e);
                    }

                    try {
                        return Integer.parseInt(parten);
                    } catch (NumberFormatException var5) {
                        log.error(var5.getMessage() + "->partern:" + parten);

                        return this.getDefaultPort();
                    }
                }
            }

            return this.getDefaultPort();
        } else {
            return this.getDefaultPort();
        }
    }

    private String parseServer() {
        if (this.url != null && this.url.length() != 0) {
            int s = this.url.indexOf("://");
            if (s != -1 && s < this.url.length()) {
                s = this.url.indexOf("/", s + 3) + 1;
                if (s != 0 && s < this.url.length()) {
                    return this.url.substring(s);
                }
            }

            return null;
        } else {
            return null;
        }
    }

    private int getDefaultPort() {
        if ("ftp".equals(this.protocol)) {
            return 21;
        } else if ("smtp".equals(this.protocol)) {
            return 25;
        } else if ("rmi".equals(this.protocol)) {
            return 1099;
        } else {
            return "eic".equals(this.protocol) ? 8001 : 80;
        }
    }

    public String getProtocol() {
        return this.protocol;
    }

    public String getIp() {
        return this.ip;
    }

    public int getPort() {
        return this.port;
    }

    public String getServer() {
        return this.server;
    }

    public String getUrl() {
        return this.url;
    }

    public void setUrl(String url) {
        this.init(url);
    }

    private void readObject(ObjectInputStream in) throws ClassNotFoundException, IOException {
        in.defaultReadObject();
    }
}
