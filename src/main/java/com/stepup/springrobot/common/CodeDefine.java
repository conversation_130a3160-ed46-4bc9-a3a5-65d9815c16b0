package com.stepup.springrobot.common;

import java.time.format.DateTimeFormatter;

public abstract class CodeDefine {

    public static final DateTimeFormatter DEFAULT_INT_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyMMdd");
    public static final String HEADER_USER_COMMUNICATE_SV = "11b0718a0375f62d2e5e5c1a4f513df9";
    public static final String PRODUCT_TF22 = "TF22";

    /*Thành công*/
    public static final int OK = 200;
    public static final int ERR_COMMON = 600;
    public static final int NON_AUTHORITATIVE = 401;

    // Check phát âm
    public static final String ERROR_MESSAGE_PROCESS = "Cannot process audio!";
    public static final String ERROR_MESSAGE_TOO_SHORT = "Audio is too short!";
    public static final String ERROR_MESSAGE_SOMETHING_WRONG = "Error! Something wrong";
    public static final String MSG_CALL_OK_HTTP_NULL = "Object mono is null";
    public static final String BOT_CHECK_SPEECH_ERROR_RESPONSE = "Bot check speech error response";

    public static final int SERVER_ERROR = 500;
    public static final int NOT_NULL = 100;// sai thông tin user hoặc password
    public static final int TOO_MANY_REQUESTS = 429;
    public static final int FILE_NOT_FOUND = 128;

    public static final String ONION_USER_MEDIA_IN_BUCKET_STORAGE_S3 = "robot/audio/user";
    public static final String UPLOAD_MEDIA_IN_BUCKET_STORAGE_S3 = "robot/upload";
    public static final String UPLOAD_VIDEO_IN_BUCKET_STORAGE_S3 = "robot/video/user";

    public static final String URL_AVATAR_USER_DEFAULT = "https://smedia.stepup.edu.vn/image/avatar/avatar_default1.jpg";

    public static final String CONVERSATION_FINISH_SENTENCE = "Đã hết thời gian bài học, lần sau trò chuyện tiếp nha";
    public static final String CONVERSATION_FINISH_AUDIO = "http://tts-file-mgc-52.hacknao.edu.vn/data/tts_female_linh_v1/1f02774d4a9480d6f4d7d51c6d2209e4_3_1.wav";

    public static final String WEBSOCKET_PARAM_BOT_ID = "bot_id";
    public static final String WEBSOCKET_PARAM_IS_CONVERT = "is_convert";

    public static final String GET_PRE_SIGNUP_INFO_KEY = "GET_PRE_SIGNUP_INFO_KEY";
    public static final String GET_FORGOT_INFO_KEY = "FORGOT_PASS_INFO";
    public static final String UPDATE_PASSWORD_KEY = "UPDATE_PASSWORD_KEY";
    public static final String SIGNIN_LIMIT_KEY = "SIGNIN_LIMIT_KEY";
    public static final String SIGNUP_LIMIT_KEY = "SIGNUP_LIMIT_KEY";
    public static final String SMS_LIMIT_KEY = "_SMS_LIMIT_KEY";
    public static final String SMS_VERIFY_LIMIT_KEY = "_SMS_VERIFY_LIMIT_KEY";
    public static final String STRING_PHONE = "STRING_PHONE";
    public static final int FORGOT_PASS_BLOCK_TIME = 10;
    public static final int SIGNIN_BLOCK_TIME = 10;
    public static final int SIGNUP_BLOCK_TIME = 10;

    public static final String REDIS_KEY_ZALO_AUTHENTICATION_CONFIG = "zalo_authentication_config";
    public static final long TTL_KEY_ZALO_AUTHENTICATION_CONFIG = 365;

    // Error Zalo
    public static final int ERROR_NOT_EXISTED = -118;
    public static final int ERROR_PHONE_NUMBER_INVALID = -108;

    //   sms gateway
    public static final int SMS_GATEWAY_NOT_EXIST_PORT = -1;
    public static final int SMS_GATEWAY_NOT_DETERMINE_NETWORK = -2;

    public static final String APP_NAME_DEFAULT = "ROBOT";

    public static final long TTL_KEY_FIX_CONTENT = 360;
    public static final String REDIS_KEY_RATE_LIMIT = "rate_limit_get_user_info";
    public static final String REDIS_KEY_VERIFY_OTP_REQUEST = "verify_otp_request";
    public static final String REDIS_LIMIT_CHECK_ASR = "limit_check_asr";
    public static final String REDIS_LIMIT_CHECK_SPEECH = "limit_check_speech";
    public static final String REDIS_COUNT_INTERNAL_CHECK_SPEECH = "map_total_check_speech";
    public static final String REDIS_COUNT_INTERNAL_CHECK_ASR = "map_total_check_asr";

    public static final String REDIS_KEY_CONVERSATION_BY_SOCKET_SESSION = "conversation_by_socket_session";
    public static final String REDIS_KEY_SERVO_BY_EMOTION = "servo_by_emotion";
    public static final String REDIS_KEY_LLM_IMAGE = "llm_image";
    public static final String REDIS_KEY_LLM_MOOD = "llm_mood";
    public static final String REDIS_KEY_ALARM_SCHEDULES = "alarm_schedules";
    public static final String REDIS_KEY_ALARM_ACTIVITY = "alarm_activities";
    public static final String REDIS_KEY_USERS_ISSUE_TOKEN = "users_issue_token";

    public static final long TTL_KEY_LIMIT_CHECK_SPEECH = 1;
    public static final long TTL_CHECK_SPEECH_A_DAY = 1;
    public static final long TTL_CHECK_SPEECH = 365;

    public static final String SUCCESS_MESSAGE = "Thành công!";

    public static final String SOCKET_VER_2_ENDPOINT = "/ws/free_talk";

    public static final String ROBOT_MQTT_TOPIC_PREFIX = "robots/device/";
    public static final DateTimeFormatter ALARM_SCHEDULE_PATTERN = DateTimeFormatter.ofPattern("HH:mm-dd/MM/yyyy");

    public static final String DEMO_CUSTOM_EMOTION_DATA = "[{\"text\":\"BEEP BEEP! Đã đến Trái Đất. Nhưng mà tớ không biết cách nói chuyện với con người! Chào cậu, cậu giúp tớ được không?\",\"audio\":\"http://tts-file-mgc-52.hacknao.edu.vn/data/tts_female_linh_v1/03f6d1077dfac55bbd53bf40dd69bae4_None_1.0.mp3\",\"media\":null,\"emotions\":[{\"duration\":2264,\"type\":\"LAZY\",\"emotion_gif\":\"https://smedia.stepup.edu.vn/thecoach/all_files/demo_custome_imotion/surprise_face.gif\",\"text\":null,\"servo_data\":{\"parts\":[{\"steps\":[{\"time\":400,\"angle\":120},{\"time\":200,\"angle\":120},{\"time\":800,\"angle\":60},{\"time\":200,\"angle\":60},{\"time\":200,\"angle\":90}],\"part_type\":\"HEAD\",\"repetition\":1,\"start_time\":200},{\"steps\":[{\"time\":500,\"angle\":90}],\"part_type\":\"BASE\",\"repetition\":1,\"start_time\":300},{\"steps\":[{\"time\":500,\"angle\":60}],\"part_type\":\"LEFT_HAND\",\"repetition\":1,\"start_time\":100},{\"steps\":[{\"time\":500,\"angle\":60}],\"part_type\":\"RIGHT_HAND\",\"repetition\":1,\"start_time\":100}]}},\n" +
            "  {\"duration\":2876,\"type\":\"ANXIOUS\",\"emotion_gif\":\"https://smedia.stepup.edu.vn/thecoach/all_files/demo_custome_imotion/anxious.gif\",\"text\":null,\"servo_data\":{\"parts\":[{\"steps\":[{\"time\":300,\"angle\":90}],\"part_type\":\"HEAD\",\"repetition\":2,\"start_time\":100},{\"steps\":[{\"time\":500,\"angle\":90}],\"part_type\":\"BASE\",\"repetition\":1,\"start_time\":300},{\"steps\":[{\"time\":500,\"angle\":60}],\"part_type\":\"LEFT_HAND\",\"repetition\":1,\"start_time\":100},{\"steps\":[{\"time\":500,\"angle\":60}],\"part_type\":\"RIGHT_HAND\",\"repetition\":1,\"start_time\":100}]}},{\"duration\":2084,\"type\":\"TEASING\",\"emotion_gif\":\"https://smedia.stepup.edu.vn/thecoach/all_files/demo_custome_imotion/motivational.gif\",\"text\":null,\"servo_data\":{\"parts\":[{\"steps\":[{\"time\":300,\"angle\":90}],\"part_type\":\"HEAD\",\"repetition\":2,\"start_time\":100},{\"steps\":[{\"time\":500,\"angle\":90}],\"part_type\":\"BASE\",\"repetition\":1,\"start_time\":300},{\"steps\":[{\"time\":500,\"angle\":60}],\"part_type\":\"LEFT_HAND\",\"repetition\":1,\"start_time\":100},{\"steps\":[{\"time\":700,\"angle\":150},{\"time\":3000,\"angle\":150},{\"time\":500,\"angle\":60}],\"part_type\":\"RIGHT_HAND\",\"repetition\":1,\"start_time\":600}]}}]}]";
}
