package com.stepup.springrobot.common;

import com.stepup.springrobot.exception.business.user.InvalidPhoneNumberException;
import org.springframework.util.StringUtils;

public class ValidationUtils {
    public static String regexPhone = "\\d{10,12}";
    public static String prefixPhone = "0";
    public static String locationPlusVN = "\\+84";
    public static String locationVN = "84";
    public static String space = " ";

    public static String validateAndNormalizePhone(String phone) throws InvalidPhoneNumberException {
        if(!StringUtils.hasText(phone)) {
            throw new InvalidPhoneNumberException("Số điện thoại không được để trống bạn ơi 😐");
        }

        phone = refactorPhone(phone);

        if(!phone.matches(regexPhone) || !phone.startsWith(prefixPhone)) {
            throw new InvalidPhoneNumberException("Sai định dạng số điện thoại");
        }

        return phone;
    }

    public static String normalizePhone(String phone) {
        if(!StringUtils.hasText(phone)) {
            return null;
        }

        phone = refactorPhone(phone);

        if(!phone.matches(regexPhone) || !phone.startsWith(prefixPhone)) {
            return null;
        }

        return phone;
    }

    /**
     * Bỏ space đầu, cuối, giữa.
     * Thay thế +84 hoặc 84 ở đầu sdt thành 0
     *
     * @param phone
     * @return
     */
    public static String refactorPhone(String phone) {
        phone = phone.trim().replaceAll(space, "").replaceAll(locationPlusVN, prefixPhone);
        phone = phone.startsWith(locationVN) ? phone.replaceFirst(locationVN, prefixPhone) : phone;
        return phone;
    }
}
