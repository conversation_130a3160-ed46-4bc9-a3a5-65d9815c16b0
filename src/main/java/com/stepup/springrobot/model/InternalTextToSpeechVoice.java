package com.stepup.springrobot.model;

import java.util.HashMap;
import java.util.Map;

public enum InternalTextToSpeechVoice {
    <PERSON><PERSON>("<PERSON>oy"),
    <PERSON><PERSON>("Jenifer"),
    ha_v3("ha_v3"),
    linh_v1("linh_v1");

    private static final Map<String, InternalTextToSpeechVoice> TEXT_TO_SPEECH_VOICE_HASH_MAP = new HashMap<>();

    private final String feature;

    InternalTextToSpeechVoice(String feature) {
        this.feature = feature;
    }

    static {
        for (InternalTextToSpeechVoice e : values()) {
            TEXT_TO_SPEECH_VOICE_HASH_MAP.put(e.feature, e);
        }
    }

    public static InternalTextToSpeechVoice from(String feature) {
        return TEXT_TO_SPEECH_VOICE_HASH_MAP.get(feature);
    }

    public String getFeature() {
        return this.feature;
    }

    public static InternalTextToSpeechVoice getDefaultVoice() {
        return linh_v1;
    }
}
