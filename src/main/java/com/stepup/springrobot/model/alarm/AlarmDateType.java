package com.stepup.springrobot.model.alarm;

import java.time.DayOfWeek;
import java.util.HashMap;
import java.util.Map;

public enum AlarmDateType {
    ONE_TIME("ONE_TIME", "Chỉ một lần", true, "chỉ một lần"),
    T2("T2", "Thứ 2", false, "T2"),
    T3("T3", "Thứ 3", false, "T3"),
    T4("T4", "Thứ 4", false, "T4"),
    T5("T5", "Thứ 5", false, "T5"),
    T6("T6", "Thứ 6", false, "T6"),
    T7("T7", "Th<PERSON>́ 7", false, "T7"),
    CN("CN", "Chủ nhật", false, "CN"),
    EVERYDAY("EVERYDAY", "Hàng ngày", false, "hàng ngày");

    public static final Map<String, AlarmDateType> ALARM_DATE_TYPE_HASH_MAP = new HashMap<>();

    private final String type;

    private final String label;

    private final Boolean isDefault;

    private final String description;

    AlarmDateType(String type, String label, Boolean isDefault, String description) {
        this.type = type;
        this.label = label;
        this.isDefault = isDefault;
        this.description = description;
    }

    static {
        for (AlarmDateType e : values()) {
            ALARM_DATE_TYPE_HASH_MAP.put(e.type, e);
        }
    }

    public static AlarmDateType from(String type) {
        if (type == null) {
            return null;
        }

        return ALARM_DATE_TYPE_HASH_MAP.get(type);
    }

    public static AlarmDateType convertFromWeekDay(DayOfWeek dayOfWeek) {
        if (dayOfWeek == null) {
            return null;
        }

        switch (dayOfWeek) {
            case MONDAY:
                return T2;
            case TUESDAY:
                return T3;
            case WEDNESDAY:
                return T4;
            case THURSDAY:
                return T5;
            case FRIDAY:
                return T6;
            case SATURDAY:
                return T7;
            case SUNDAY:
                return CN;
            default:
                return null;
        }
    }

    public String getType() {
        return this.type;
    }

    public String getLabel() {
        return this.label;
    }

    public Boolean isDefault() {
        return this.isDefault;
    }

    public String getDescription() {
        return this.description;
    }
}
