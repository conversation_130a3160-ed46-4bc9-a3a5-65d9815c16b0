package com.stepup.springrobot.model.alarm;

import java.util.HashMap;
import java.util.Map;

public enum AlarmTimeType {
    AM("AM"),
    PM("PM");

    public static final Map<String, AlarmTimeType> ALARM_DATE_TYPE_HASH_MAP = new HashMap<>();

    private final String type;

    AlarmTimeType(String type) {
        this.type = type;
    }

    static {
        for (AlarmTimeType e : values()) {
            ALARM_DATE_TYPE_HASH_MAP.put(e.type, e);
        }
    }

    public static AlarmTimeType from(String type) {
        if (type == null) {
            return null;
        }

        return ALARM_DATE_TYPE_HASH_MAP.get(type);
    }

    public String getType() {
        return this.type;
    }
}
