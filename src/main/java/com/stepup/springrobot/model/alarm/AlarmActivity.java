package com.stepup.springrobot.model.alarm;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import com.sun.istack.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "alarm_activities")
public class AlarmActivity extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "name", nullable = false)
    private String name;
    
    @Column(name = "description")
    private String description;

    @NotNull
    @Enumerated(EnumType.STRING)
    private AlarmActivityType type;

    @Column(name = "response_voice")
    private String responseVoice;

    @Column(name = "remind_voice")
    private String remindVoice;
}
