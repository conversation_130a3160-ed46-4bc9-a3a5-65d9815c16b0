package com.stepup.springrobot.model.alarm;

import java.util.HashMap;
import java.util.Map;

public enum AlarmActivityType {
    STUDY("STUDY", "Time to study"),
    HOMEWORK("HOMEWORK", "Time to do homework"),
    SLEEP("SLEEP", "Time to sleep"),
    WAKE_UP("WAKE_UP", "Time to wake up");

    public static final Map<String, AlarmActivityType> ALARM_ACTIVITY_TYPE_HASH_MAP = new HashMap<>();

    private final String type;

    private final String message;

    AlarmActivityType(String type, String message) {
        this.type = type;
        this.message = message;
    }

    static {
        for (AlarmActivityType e : values()) {
            ALARM_ACTIVITY_TYPE_HASH_MAP.put(e.type, e);
        }
    }

    public static AlarmActivityType from(String type) {
        if (type == null) {
            return null;
        }

        return ALARM_ACTIVITY_TYPE_HASH_MAP.get(type);
    }

    public String getType() {
        return this.type;
    }

    public String getMessage() {
        return this.message;
    }
}
