package com.stepup.springrobot.model.alarm;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "alarm_schedules", indexes = {
    @Index(name = "alarm_schedules_idx_user", columnList = "user_id"),
    @Index(name = "alarm_schedules_idx_time", columnList = "time_hour, time_minute")
})
public class AlarmSchedule extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "activity_id", nullable = false)
    private Long activityId;
    
    @Column(name = "user_id", nullable = false)
    private String userId;

    @Column(name = "days_active")
    private String daysActive;
    
    @Column(name = "time_hour", nullable = false)
    private Integer timeHour;
    
    @Column(name = "time_minute", nullable = false)
    private Integer timeMinute;
    
    @Column(name = "is_active", nullable = false)
    private Boolean isActive;
}
