package com.stepup.springrobot.model.alarm;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import com.stepup.springrobot.model.communication.CommunicationRequestStatusType;
import com.sun.istack.NotNull;
import lombok.*;

import javax.persistence.*;


@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "alarm_schedule_executions", indexes = {
    @Index(name = "alarm_schedule_executions_time", columnList = "time_schedule"),
})
public class AlarmScheduleExecution extends ModifierTrackingEntity {
    @Id
    private String id;

    @NotNull
    @Column(name = "user_id")
    private String userId;

    @Column(name = "robot_id")
    private String robotId;

    @Column(name = "profile_id")
    private String profileId;

    @Column(name = "alarm_schedule_id")
    private Long alarmScheduleId;

    @Enumerated(EnumType.STRING)
    @Column(name = "activity_type")
    private AlarmActivityType activityType;

    @Column(name = "request_data", columnDefinition = "TEXT")
    private String requestData;

    @Column(name = "received_data", columnDefinition = "TEXT")
    private String receivedData;

    @Column(name = "response_data", columnDefinition = "TEXT")
    private String responseData;

    @Enumerated(EnumType.STRING)
    private CommunicationRequestStatusType status;

    @Column(name = "time_schedule", nullable = false)
    private String timeSchedule;
}
