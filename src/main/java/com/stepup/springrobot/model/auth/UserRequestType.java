package com.stepup.springrobot.model.auth;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum UserRequestType {
    PRE_SIGN_UP("PRE_SIGN_UP"),
    PRE_FORGOT_PW("PRE_FORGOT_PW"),
    FORGOT_PW_SMS("FORGOT_PW_SMS"),
    FORGOT_PW_SMS_BRANDNAME("FORGOT_PW_SMS_BRANDNAME"),
    FORGOT_PW_SUCCESS("FORGOT_PW_SUCCESS"),
    SIGN_UP_FIREBASE("SIGN_UP_FIREBASE"),
    SIGN_UP_SMS("SIGN_UP_SMS"),
    SIGN_UP_SMS_BRANDNAME("SIGN_UP_SMS_BRANDNAME"),
    SIGN_UP_SUCCESS("SIGN_UP_SUCCESS"),
    PRE_SIGN_UP_USER_EXIST("PRE_SIGN_UP_USER_EXIST"),
    TOO_MANY_REQUESTS("TOO_MANY_REQUESTS");

    private static final Map<String, UserRequestType> USER_REQUEST_TYPE_HASH_MAP = new HashMap<>();

    private final String type;

    UserRequestType(String type) {
        this.type = type;
    }

    static {
        for (UserRequestType e : values()) {
            USER_REQUEST_TYPE_HASH_MAP.put(e.type, e);
        }
    }

    public static UserRequestType from(String type) {
        if (type == null) {
            return null;
        }

        return USER_REQUEST_TYPE_HASH_MAP.get(type);
    }
}
