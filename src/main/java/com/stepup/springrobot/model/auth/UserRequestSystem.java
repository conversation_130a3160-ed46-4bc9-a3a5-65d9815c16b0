package com.stepup.springrobot.model.auth;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "user_request_system")
public class UserRequestSystem extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", columnDefinition = "VARCHAR(50)")
    private String id;

    private String phone;

    @Column(name = "normalize_phone")
    private String normalizePhone;

    private String ip;

    @Enumerated(EnumType.STRING)
    private UserRequestType type;

    @JsonProperty("app_v")
    private String appV;

    @JsonProperty("os")
    private String os;

    @JsonProperty("device_name")
    @Column(name = "device_name")
    private String deviceName;
}
