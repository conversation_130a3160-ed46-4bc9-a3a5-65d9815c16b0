package com.stepup.springrobot.model.auth;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.Date;

@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "refresh_tokens", indexes = {
        @Index(name = "refresh_tokens_user_id", columnList = "user_id", unique = true),
        @Index(name = "refresh_tokens_token", columnList = "token")
})
public class RefreshToken extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", columnDefinition = "VARCHAR(50)")
    private String id;

    private String token;

    @JsonProperty("expiry_date")
    @Column(name = "expiry_date", columnDefinition = "timestamp without time zone")
    private Date expiryDate;

    @JsonProperty("user_id")
    @Column(name = "user_id")
    private String userId;
}
