package com.stepup.springrobot.model.auth;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "user_issue_token", indexes = {
    @Index(name = "user_issue_token_user_id", columnList = "user_id")
})
public class UserIssueToken extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    @Column(columnDefinition = "varchar(255)")
    private String token;

    // khoá tài khoản nếu đăng nhập nhiều hơn 3 thiết bị
    @Column(name = "lock_device", columnDefinition = "boolean default false")
    private Boolean lockDevice = false;

    @Column(name = "ip")
    @JsonProperty("ip")
    private String ip;

    @JsonProperty("device_id")
    @Column(name = "device_id")
    private String deviceId;

    @JsonProperty("os")
    @Column(name = "os")
    private String os;

    @JsonProperty("device_name")
    @Column(name = "device_name")
    private String deviceName;

    @Column(name = "version")
    @JsonProperty("version")
    private String versionApp = "v1.0";

    @Column(name = "user_id")
    private String userId;
}
