package com.stepup.springrobot.model.community;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "community_feature_likes", indexes = {
        @Index(name = "feature_like_feature_id_idx", columnList = "feature_id")
})
public class FeatureLike extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "feature_id", nullable = false)
    private Long featureId;

    @Column(name = "user_id", nullable = false)
    private String userId;
}