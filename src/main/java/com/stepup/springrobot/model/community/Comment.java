package com.stepup.springrobot.model.community;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "community_comments", indexes = {
        @Index(name = "comment_user_id", columnList = "user_id"),
        @Index(name = "comment_feature_id", columnList = "feature_id")
})
public class Comment extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(columnDefinition = "TEXT")
    private String content;

    @Column(name = "likes")
    private int likes;

    @Column(name = "user_id", nullable = false)
    private String userId;

    @Column(name = "feature_id")
    private Long featureId;
}