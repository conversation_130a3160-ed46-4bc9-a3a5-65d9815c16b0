package com.stepup.springrobot.model.community;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "community_comment_likes", indexes = {
        @Index(name = "comment_like_comment_id_index", columnList = "comment_id")
})
public class CommentLike extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "comment_id", nullable = false)
    private Long commentId;

    @Column(name = "user_id", nullable = false)
    private String userId;
}