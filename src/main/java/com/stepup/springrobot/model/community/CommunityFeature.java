package com.stepup.springrobot.model.community;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

@Entity
@Table(name = "community_features")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CommunityFeature extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String title;

    @Column(columnDefinition = "TEXT")
    private String description;

    private int likes;
}