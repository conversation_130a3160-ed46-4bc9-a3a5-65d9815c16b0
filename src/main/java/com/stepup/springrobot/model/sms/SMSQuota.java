package com.stepup.springrobot.model.sms;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@Entity
@Table(name = "sms_quota",
        indexes = {@Index(name = "sms_quota_datetime_service_index",  columnList="datetime, service", unique = true)})
public class SMSQuota {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    //SMSServiceProvider: FIREBASE; BRANDNAME; SMS_COMPANY
    @Column(name = "service")
    private String service;

    @Column(name = "quota")
    private Integer quota;

    @Column(name = "used")
    private Integer used;

    @Column(name = "datetime")
    private Integer datetime;

    @CreationTimestamp
    @Column(name = "created_at", columnDefinition = "timestamp without time zone")
    private Date createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", columnDefinition = "timestamp without time zone")
    private Date updatedAt;
}
