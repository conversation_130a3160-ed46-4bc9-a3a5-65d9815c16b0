package com.stepup.springrobot.model.sms;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum SMSServiceProvider {
    SMS_ZALO("SMS_ZALO"),
    SMS_VOICE("SMS_VOICE"),
    SMS_FIREBASE("SMS_FIREBASE"),
    SMS_BRANDNAME("SMS_BRANDNAME"),
    SMS_COMPANY("SMS_COMPANY");

    private static final Map<String, SMSServiceProvider> SMS_SERVICE_PROVIDER_HASH_MAP = new HashMap<>();

    private final String type;

    SMSServiceProvider(String type) {
        this.type = type;
    }

    static {
        for (SMSServiceProvider e : values()) {
            SMS_SERVICE_PROVIDER_HASH_MAP.put(e.type, e);
        }
    }

    public static SMSServiceProvider from(String type) {
        return SMS_SERVICE_PROVIDER_HASH_MAP.get(type);
    }
}
