package com.stepup.springrobot.model.sms;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum SMSNetworkName {
    VIETTEL("VIETTEL"),
    VINA("VINA"),
    MOBI("MOBI"),
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("VIETNA<PERSON><PERSON><PERSON>"),
    GT<PERSON>("GT<PERSON>"),
    <PERSON><PERSON><PERSON>("OTHER");

    private final String type;
    public static final Map<String, SMSNetworkName> SMS_NETWORK_MAP = new HashMap<>();
    public static final Map<String, List<Integer>> SMS_NETWORK_PORT_MAP = new HashMap<>();

    static {
        for (SMSNetworkName e : values()) {
            SMS_NETWORK_MAP.put(e.type, e);
        }
    }

    static {
        SMS_NETWORK_PORT_MAP.put(SMSNetworkName.VIETTEL.name(), List.of(0,1,2,3,5,6,7,9,10));
        SMS_NETWORK_PORT_MAP.put(SMSNetworkName.VINA.name(), List.of(11,12,13,14,15,16,17,18));
        SMS_NETWORK_PORT_MAP.put(SMSNetworkName.MOBI.name(), List.of(19,20,21,22,23,24,25,26,27,28,29,30,31));
        SMS_NETWORK_PORT_MAP.put(SMSNetworkName.VIETNAMOBILE.name(), List.of(31));
        SMS_NETWORK_PORT_MAP.put(SMSNetworkName.GTEL.name(), List.of(31));
        SMS_NETWORK_PORT_MAP.put(SMSNetworkName.OTHER.name(), List.of(31));
    }

    public static SMSNetworkName from(String type) {
        return SMS_NETWORK_MAP.get(type);
    }

    SMSNetworkName(String type) {
        this.type = type;
    }

    public String getType() {
        return this.type;
    }
}
