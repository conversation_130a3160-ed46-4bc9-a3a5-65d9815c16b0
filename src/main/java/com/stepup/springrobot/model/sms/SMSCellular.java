package com.stepup.springrobot.model.sms;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.vladmihalcea.hibernate.type.array.ListArrayType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data

@Builder
@Entity
@Table(name = "sms_cellular")
@TypeDef(
        name = "list-array",
        typeClass = ListArrayType.class
)
public class SMSCellular {
    @Id
    private Integer id;

    @Column(name = "cellular_name")
    private String cellularName;

    @Type(type = "list-array")
    @Column(name = "list_port", columnDefinition = "int[] default '{}'")
    @JsonProperty("list_port")
    //https://vladmihalcea.com/postgresql-array-java-list/
    private List<Integer> listPort;
}
