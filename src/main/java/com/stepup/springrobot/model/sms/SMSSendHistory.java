package com.stepup.springrobot.model.sms;

import lombok.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.Date;

@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "sms_send_history",
        indexes = {@Index(name = "to_phone_sms_index",  columnList="to_phone")}
)
public class SMSSendHistory {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "to_phone")
    private String toPhone;

    @Column(name = "content")
    private String content;

    @Column(name = "otp")
    private String otp;

    @Column(name = "status", columnDefinition = "boolean default false")
    private Boolean status;

    @Column(name = "network")
    private String network;

    //  SMSAction  action: SIGN_UP SIGN_IN FORGOT_PASSWORD
    @Enumerated(EnumType.STRING)
    @Column(name = "action")
    private SMSAction action;

    @Column(name = "ip")
    private String ip;

    @Enumerated(EnumType.STRING)
    @Column(name = "sms_service_provider")
    private SMSServiceProvider smsServiceProvider;

    @Column(name = "from_app", columnDefinition = "varchar(30) default 'ROBOT'")
    private String fromApp;

    @Column(name = "verified", columnDefinition = "boolean default false")
    private Boolean verified;

    @CreationTimestamp
    @Column(name = "created_at", columnDefinition = "timestamp without time zone")
    private Date createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", columnDefinition = "timestamp without time zone")
    private Date lastUpdated;
}
