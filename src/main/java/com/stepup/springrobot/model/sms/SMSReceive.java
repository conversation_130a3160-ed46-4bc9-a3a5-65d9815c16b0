package com.stepup.springrobot.model.sms;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Entity
@Table(name = "sms_receive",
        indexes = {@Index(name = "sms_receive_number_index",  columnList="number"), @Index(name = "sms_receive_normalize_phone_index",  columnList="normalize_phone")})
public class SMSReceive {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @JsonProperty("incoming_sms_id")
    @Column(name = "incoming_sms_id")
    private int incomingSmsId;

    private String sn;
    private int port;
    private String number;
    private String smsc;
    private String timestamp;

    @Column(columnDefinition = "text")
    private String text;

    private String imsi;

    @Column(columnDefinition = "boolean default false")
    private Boolean hide;

    @Column(name = "normalize_phone")
    @JsonProperty("normalize_phone")
    private String normalizePhone;

    @CreationTimestamp
    @Column(name = "created_at", columnDefinition = "timestamp without time zone")
    private Date createdAt;

    @UpdateTimestamp
    @Column(name = "last_updated", columnDefinition = "timestamp without time zone")
    private Date lastUpdated;
}
