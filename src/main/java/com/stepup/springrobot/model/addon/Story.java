package com.stepup.springrobot.model.addon;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "story")
public class Story extends ModifierTrackingEntity {
    @Id
    private Long id;

    @NotNull
    @Column(name = "orders")
    private Double order;

    @NotNull
    private String title;

    @NotNull
    private String author;

    @NotNull
    private String description;

    @NotNull
    private String thumbnail;

    @NotNull
    private String url;
}
