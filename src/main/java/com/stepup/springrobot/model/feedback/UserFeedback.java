package com.stepup.springrobot.model.feedback;

import com.fasterxml.jackson.databind.JsonNode;
import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.UUID;

@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "user_feedbacks", indexes = {
        @Index(name = "uf_feedback_id", columnList = "feedback_id")
})
public class UserFeedback extends ModifierTrackingEntity {
    @Id
    @Column(name = "id", columnDefinition = "VARCHAR(255)")
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;

    @NotNull
    @Column(name = "description", nullable = false)
    private String description;

    @Type(type = "jsonb")
    @Column(name = "images", columnDefinition = "jsonb")
    private JsonNode images;

    @Column(name = "feedback_id")
    private String feedbackId;

    @Column(name = "rating")
    private Integer rating;

    @Column(name = "account_id")
    private UUID accountId;
}
