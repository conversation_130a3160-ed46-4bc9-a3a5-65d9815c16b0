package com.stepup.springrobot.model.notification;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import javax.validation.constraints.NotNull;


@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "user_device_tokens", indexes = {
    @Index(name = "́user_device_tokens_idx_user", columnList = "user_id"),
    @Index(name = "user_device_tokens_idx_token", columnList = "device_token"),
    @Index(name = "user_device_tokens_idx_device", columnList = "device_id", unique = true)
})
public class UserDeviceToken extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", columnDefinition = "VARCHAR(50)")
    private String id;

    @Column(name = "profile_id")
    private String profileId;

    @NotNull
    @Column(name = "user_id")
    private String userId;

    @NotNull
    @Column(name = "device_token")
    private String deviceToken;

    @NotNull
    @Column(name = "device_id")
    private String deviceId;
}
