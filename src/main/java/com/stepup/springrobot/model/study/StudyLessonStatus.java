package com.stepup.springrobot.model.study;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum StudyLessonStatus {
    COMPLETED("COMPLETED"),
    PENDING("PENDING"),
    DISABLED("DISABLED");

    public static final Map<String, StudyLessonStatus> LESSON_TYPE_HASH_MAP = new HashMap<>();

    private final String type;

    StudyLessonStatus(String type) {
        this.type = type;
    }

    static {
        for (StudyLessonStatus e : values()) {
            LESSON_TYPE_HASH_MAP.put(e.type, e);
        }
    }

    public static StudyLessonStatus from(String type) {
        if (type == null) {
            return null;
        }

        return LESSON_TYPE_HASH_MAP.get(type);
    }
}
