package com.stepup.springrobot.model.study;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;

import javax.persistence.*;
import javax.validation.constraints.NotNull;


@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "study_lessons")
public class StudyLesson extends ModifierTrackingEntity {
    @Id
    private Long id;

    @NotNull
    @Column(name = "topic_id")
    private Long topicId;

    @NotNull
    private String name;

    @NotNull
    private String icon;

    @NotNull
    @Column(name = "orders")
    private Double order;

    @NotNull
    @Enumerated(EnumType.STRING)
    private StudyLessonType type;

    @NotNull
    @Column(name = "bot_id")
    private Long botId;

    private String description;

    private String thumbnail;

    private String title;
}
