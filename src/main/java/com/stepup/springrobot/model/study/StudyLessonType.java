package com.stepup.springrobot.model.study;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum StudyLessonType {
    INTRO("INTRO",  "Mở đầu"),
    LISTENING("LISTENING", "<PERSON><PERSON> năng Nghe"),
    SPEAKING("SPEAKING", "Kỹ năng Nói"),
    READING("READING", "Kĩ năng Đọc"),
    WRITING("WRITING", "<PERSON><PERSON> năng Viết");

    public static final Map<String, StudyLessonType> LESSON_TYPE_HASH_MAP = new HashMap<>();

    private final String type;

    private final String description;

    StudyLessonType(String type, String description) {
        this.type = type;
        this.description = description;
    }

    static {
        for (StudyLessonType e : values()) {
            LESSON_TYPE_HASH_MAP.put(e.type, e);
        }
    }

    public static StudyLessonType from(String type) {
        if (type == null) {
            return null;
        }

        return LESSON_TYPE_HASH_MAP.get(type);
    }
}
