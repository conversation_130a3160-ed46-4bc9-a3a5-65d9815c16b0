package com.stepup.springrobot.model.study;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;


@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "study_topics")
public class StudyTopic extends ModifierTrackingEntity {
    @Id
    private Long id;

    @NotNull
    @Column(name = "unit_id")
    private Long unitId;

    @NotNull
    private String name;

    @NotNull
    private String thumbnail;

    @NotNull
    @Column(name = "orders")
    private Double order;

    private Integer duration;
}
