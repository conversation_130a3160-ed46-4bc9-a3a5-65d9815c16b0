package com.stepup.springrobot.model.study;

import com.stepup.springrobot.model.ModifierTrackingEntity;

import lombok.*;

import javax.persistence.*;
import javax.validation.constraints.NotNull;


@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "study_units")
public class StudyUnit extends ModifierTrackingEntity {
    @Id
    private Long id;

    @NotNull
    private String name;

    @NotNull
    @Column(name = "orders")
    private Double order;
}
