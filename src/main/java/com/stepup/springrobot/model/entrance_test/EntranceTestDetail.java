package com.stepup.springrobot.model.entrance_test;

import com.fasterxml.jackson.databind.JsonNode;
import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import javax.validation.constraints.NotNull;


@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "entrance_test_details")
public class EntranceTestDetail extends ModifierTrackingEntity {
    @Id
    private String id;

    @Column(name = "orders")
    private Double order;

    @NotNull
    @Enumerated(EnumType.STRING)
    private EntranceTestDetailType type;

    @Type(type = "jsonb")
    @Column(columnDefinition = "jsonb")
    private JsonNode data;
}
