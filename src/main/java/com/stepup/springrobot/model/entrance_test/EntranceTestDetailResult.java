package com.stepup.springrobot.model.entrance_test;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;

import javax.persistence.*;


@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "entrance_test_detail_results", indexes = {
        @Index(name = "entrance_test_result_turn_play", columnList = "profile_id, entrance_test_detail_id, turn_play", unique = true)
})
public class EntranceTestDetailResult extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "entrance_test_detail_id")
    private String entranceTestDetailId;

    @Column(name = "profile_id")
    private String profileId;

    @Column(name = "turn_play")
    private Integer turnPlay;

    @Column(name = "is_correct", columnDefinition = "boolean default false")
    private Boolean isCorrect;
}
