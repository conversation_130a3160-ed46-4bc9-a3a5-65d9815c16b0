package com.stepup.springrobot.model.entrance_test;

import java.util.HashMap;
import java.util.Map;

public enum EntranceTestDetailType {
    IMAGE_GUESSING("IMAGE_GUESSING"),
    LISTENING("LISTENING"),
    FILLING("FILLING"),
    IMAGE_SPEAKING("IMAGE_SPEAKING");

    public static final Map<String, EntranceTestDetailType> TEST_DETAIL_TYPE_HASH_MAP = new HashMap<>();

    private final String type;

    EntranceTestDetailType(String type) {
        this.type = type;
    }

    static {
        for (EntranceTestDetailType e : values()) {
            TEST_DETAIL_TYPE_HASH_MAP.put(e.type, e);
        }
    }

    public static EntranceTestDetailType from(String type) {
        return TEST_DETAIL_TYPE_HASH_MAP.get(type);
    }

    public String getType() {
        return this.type;
    }
}
