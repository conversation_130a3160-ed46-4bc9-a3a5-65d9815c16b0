package com.stepup.springrobot.model.entrance_test;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;

import javax.persistence.*;


@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "entrance_test_profiles", indexes = {
        @Index(name = "entrance_test_profile_id", columnList = "profile_id")
})
public class EntranceTestProfile extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "profile_id")
    private String profileId;

    @Column(name = "turn_play")
    private Integer turnPlay;

    @Column(name = "is_complete", columnDefinition = "boolean default false")
    private Boolean isComplete;
}
