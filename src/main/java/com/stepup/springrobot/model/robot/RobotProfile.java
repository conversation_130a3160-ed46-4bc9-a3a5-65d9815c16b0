package com.stepup.springrobot.model.robot;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.Table;

import com.stepup.springrobot.model.ModifierTrackingEntity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "robot_profiles", indexes = {
        @Index(name = "robot_profiles_user_robot_idx", columnList = "user_robot_id"),
        @Index(name = "robot_profiles_profile_idx", columnList = "profile_id")
})
public class RobotProfile extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "profile_id", nullable = false)
    private String profileId;

    @Column(name = "user_robot_id", nullable = false)
    private Long userRobotId;
} 