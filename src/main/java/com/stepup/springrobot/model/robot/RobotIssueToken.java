package com.stepup.springrobot.model.robot;

import lombok.*;
import javax.persistence.*;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "robot_issue_token", indexes = {
        @Index(name = "robot_issue_token_robot_id", columnList = "robot_id")
})
public class RobotIssueToken {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, unique = true)
    private String token;

    @Column(name = "device_id")
    private String deviceId;

    @Column(name = "robot_id")
    private String robotId;

    @Column(name = "created_at")
    private LocalDateTime createdAt;
}