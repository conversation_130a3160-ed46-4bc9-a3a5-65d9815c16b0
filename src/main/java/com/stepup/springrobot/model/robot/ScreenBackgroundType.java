package com.stepup.springrobot.model.robot;

import java.util.HashMap;
import java.util.Map;

public enum ScreenBackgroundType {
    BLACK("BLACK"),
    DARK_BLUE("DARK_BLUE");

    private static final Map<String, ScreenBackgroundType> BACKGROUND_TYPE_HASH_MAP = new HashMap<>();

    private final String feature;

    ScreenBackgroundType(String feature) {
        this.feature = feature;
    }

    static {
        for (ScreenBackgroundType e : values()) {
            BACKGROUND_TYPE_HASH_MAP.put(e.feature, e);
        }
    }

    public static ScreenBackgroundType from(String feature) {
        return BACKGROUND_TYPE_HASH_MAP.get(feature);
    }

    public String getFeature() {
        return this.feature;
    }
}
