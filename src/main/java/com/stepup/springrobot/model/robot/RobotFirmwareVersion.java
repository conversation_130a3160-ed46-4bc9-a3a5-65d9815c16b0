package com.stepup.springrobot.model.robot;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "robot_firmware_versions")
public class RobotFirmwareVersion extends ModifierTrackingEntity {
    @Id
    private Long id;

    private String version;

    private String esp1;

    private String esp2;
} 