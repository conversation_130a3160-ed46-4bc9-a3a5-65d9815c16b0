package com.stepup.springrobot.model.robot;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "robots", indexes = {
    @Index(name = "robots_idx_serial_number", columnList = "serial_number", unique = true)
})
public class Robot extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", columnDefinition = "VARCHAR(50)")
    private String id;

    @Column(name = "serial_number")
    private String serialNumber;

    @Column(name = "device_id")
    private String deviceId;
    
    @Column(name = "password")
    private String password;

    @Column(name = "is_activated")
    private boolean activated;
} 