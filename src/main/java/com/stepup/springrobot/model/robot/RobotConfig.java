package com.stepup.springrobot.model.robot;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;

import javax.persistence.*;

@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "robot_config", indexes = {
    @Index(name = "robot_config_robot_id", columnList = "robot_id", unique = true)
})
public class RobotConfig extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "robot_id", nullable = false)
    private String robotId;

    @Column(name = "firmware_version")
    private String firmwareVersion;

    @Builder.Default
    private Integer volume = 21;

    @Column(name = "screen_emotion")
    private String screenEmotion;

    @Enumerated(EnumType.STRING)
    @Column(name = "screen_background")
    private ScreenBackgroundType screenBackground;

    @Builder.Default
    @Column(name = "screen_brightness")
    private Integer screenBrightness = 100;
} 