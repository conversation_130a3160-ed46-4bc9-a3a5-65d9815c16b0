package com.stepup.springrobot.model;


import lombok.*;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "ai_provider_tokens")
public class AIProviderTokens extends ModifierTrackingEntity {
    @Id
    @Column(name = "id", columnDefinition = "VARCHAR(50)")
    private String id;

    @Column(name = "token", nullable = false)
    private String token;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "provider", nullable = false)
    private ModelAIProvider provider;
}
