package com.stepup.springrobot.model.speech;

import java.util.HashMap;
import java.util.Map;

public enum UserLimitCheckType {
    SPEECH("SPEECH",  "lần check phát âm trong 1 ngày", "phát âm"),
    ASR_EN("ASR_EN", " lần check ASR trong 1 ngày", "nhận diện"),
    ASR_VI("ASR_VI", " lần check ASR_VI trong 1 ngày", "nhận diện"),
    INTERNAL_SPEECH_TO_TEXT("INTERNAL_SPEECH_TO_TEXT", " lần generate speech to text trong 1 ngày", "nhận diện"),
    INTERNAL_TEXT_TO_SPEECH("INTERNAL_TEXT_TO_SPEECH", " lần generate text to speech trong 1 ngày", "nhận diện");

    public static final Map<String, UserLimitCheckType> LIMIT_CHECK_TYPE_HASH_MAP = new HashMap<>();

    private final String type;

    private final String description;

    private final String featureDescription;

    UserLimitCheckType(String type, String description, String featureDescription) {
        this.type = type;
        this.description = description;
        this.featureDescription = featureDescription;
    }

    static {
        for (UserLimitCheckType e : values()) {
            LIMIT_CHECK_TYPE_HASH_MAP.put(e.type, e);
        }
    }

    public static UserLimitCheckType from(String type) {
        if (type == null) {
            return SPEECH;
        }

        return LIMIT_CHECK_TYPE_HASH_MAP.get(type);
    }

    public String getType() {
        return this.type;
    }

    public String getDescription() {
        return this.description;
    }

    public String getFeatureDescription() {
        return this.featureDescription;
    }
}
