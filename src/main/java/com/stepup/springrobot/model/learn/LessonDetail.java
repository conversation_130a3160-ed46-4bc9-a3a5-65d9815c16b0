package com.stepup.springrobot.model.learn;

import com.fasterxml.jackson.databind.JsonNode;
import com.stepup.springrobot.model.ModifierTrackingEntity;
import com.vladmihalcea.hibernate.type.json.JsonNodeBinaryType;
import lombok.*;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;

import javax.persistence.*;


@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "lesson_details")
@TypeDef(name = "jsonb", typeClass = JsonNodeBinaryType.class)
public class LessonDetail extends ModifierTrackingEntity {
    @Id
    private String id;

    @Column(name = "orders")
    private Double order;

    @Enumerated(EnumType.STRING)
    private LessonDetailType type;

    @Type(type = "jsonb")
    @Column(columnDefinition = "jsonb")
    private JsonNode data;

    //    @NotNull
    @Column(name = "lesson_id")
    private Long lessonId;
}
