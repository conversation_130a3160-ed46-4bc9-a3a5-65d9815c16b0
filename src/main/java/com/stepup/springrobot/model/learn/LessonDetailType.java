package com.stepup.springrobot.model.learn;

import java.util.HashMap;
import java.util.Map;

public enum LessonDetailType {
    FREE_TALK("FREE_TALK"),
    LISTENING("LISTENING"),
    CHOOSE_PICTURE("CHOOSE_PICTURE"),
    DESCRIBE_PICTURE("DESCRIBE_PICTURE");

    public static final Map<String, LessonDetailType> DETAIL_ORDER_HASH_MAP = new HashMap<>();

    private final String type;

    LessonDetailType(String type) {
        this.type = type;
    }

    static {
        for (LessonDetailType e : values()) {
            DETAIL_ORDER_HASH_MAP.put(e.type, e);
        }
    }

    public static LessonDetailType from(String type) {
        return DETAIL_ORDER_HASH_MAP.get(type);
    }

    public String getType() {
        return this.type;
    }
}
