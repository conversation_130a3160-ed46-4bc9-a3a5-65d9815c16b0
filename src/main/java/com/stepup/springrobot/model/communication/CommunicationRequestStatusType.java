package com.stepup.springrobot.model.communication;

import java.util.HashMap;
import java.util.Map;

public enum CommunicationRequestStatusType {
    SUCCESS("SUCCESS"),
    FAIL("FAIL"),
    PENDING("PENDING"),
    RESPONDED("RESPONDED"),
    IGNORED("IGNORED"),
    BUSY("BUSY");

    public static final Map<String, CommunicationRequestStatusType> REQUEST_STATUS_TYPE_HASH_MAP = new HashMap<>();

    private final String type;

    CommunicationRequestStatusType(String type) {
        this.type = type;
    }

    static {
        for (CommunicationRequestStatusType e : values()) {
            REQUEST_STATUS_TYPE_HASH_MAP.put(e.type, e);
        }
    }

    public static CommunicationRequestStatusType from(String type) {
        return REQUEST_STATUS_TYPE_HASH_MAP.get(type);
    }

    public String getType() {
        return this.type;
    }
}
