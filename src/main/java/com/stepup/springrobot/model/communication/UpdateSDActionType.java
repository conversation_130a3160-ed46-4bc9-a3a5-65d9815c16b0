package com.stepup.springrobot.model.communication;

import java.util.HashMap;
import java.util.Map;

public enum UpdateSDActionType {
    CREATE("CREATE"),
    UPDATE("UPDATE"),
    DELETE("DELETE");

    public static final Map<String, UpdateSDActionType> UPDATE_SD_ACTION_TYPE_HASH_MAP = new HashMap<>();

    private final String type;

    UpdateSDActionType(String type) {
        this.type = type;
    }

    static {
        for (UpdateSDActionType e : values()) {
            UPDATE_SD_ACTION_TYPE_HASH_MAP.put(e.type, e);
        }
    }

    public static UpdateSDActionType from(String type) {
        return UPDATE_SD_ACTION_TYPE_HASH_MAP.get(type);
    }

    public String getType() {
        return this.type;
    }
}
