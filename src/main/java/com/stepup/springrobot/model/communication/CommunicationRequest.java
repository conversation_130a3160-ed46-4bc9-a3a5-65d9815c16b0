package com.stepup.springrobot.model.communication;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import com.stepup.springrobot.model.mqtt.MqttMessageType;
import com.sun.istack.NotNull;
import lombok.*;

import javax.persistence.*;


@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "communication_requests", indexes = {
        @Index(name = "communication_requests_user_id", columnList = "user_id"),
        @Index(name = "communication_requests_robot_id", columnList = "robot_id"),
})
public class CommunicationRequest extends ModifierTrackingEntity {
    @Id
    private String id;

    @NotNull
    @Column(name = "user_id")
    private String userId;

    @Column(name = "profile_id")
    private String profileId;

    @Column(name = "robot_id")
    private String robotId;

    @NotNull
    @Enumerated(EnumType.STRING)
    private MqttMessageType type;

    @Column(name = "request_data", columnDefinition = "TEXT")
    private String requestData;

    @Column(name = "received_data", columnDefinition = "TEXT")
    private String receivedData;

    @Column(name = "response_data", columnDefinition = "TEXT")
    private String responseData;

    @Enumerated(EnumType.STRING)
    private CommunicationRequestStatusType status;
}
