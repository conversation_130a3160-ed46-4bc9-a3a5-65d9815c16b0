package com.stepup.springrobot.model.communication;

import java.util.HashMap;
import java.util.Map;

public enum UpdateSDDataType {
    FILE("FILE"),
    FOLDER("FOLDER");

    public static final Map<String, UpdateSDDataType> UPDATE_SD_DATA_TYPE_HASH_MAP = new HashMap<>();

    private final String type;

    UpdateSDDataType(String type) {
        this.type = type;
    }

    static {
        for (UpdateSDDataType e : values()) {
            UPDATE_SD_DATA_TYPE_HASH_MAP.put(e.type, e);
        }
    }

    public static UpdateSDDataType from(String type) {
        return UPDATE_SD_DATA_TYPE_HASH_MAP.get(type);
    }

    public String getType() {
        return this.type;
    }
}
