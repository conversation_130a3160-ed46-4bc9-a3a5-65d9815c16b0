package com.stepup.springrobot.model.personal;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;

import javax.persistence.*;

@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "user_profile_history", indexes = {
        @Index(name = "user_profile_history_user", columnList = "user_id")
})
public class UserProfileHistory extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id", nullable = false)
    private String userId;

    @Column(name = "profile_id", nullable = false)
    private String profileId;
}
