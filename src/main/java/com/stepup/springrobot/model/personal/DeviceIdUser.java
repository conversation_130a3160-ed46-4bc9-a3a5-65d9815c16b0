package com.stepup.springrobot.model.personal;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;

import javax.persistence.*;


@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "device_id_user", indexes = {
        @Index(name = "device_id_user_user_id", columnList = "user_id", unique = true),
        @Index(name = "device_id_user_device_id", columnList = "device_id", unique = true)
})
public class DeviceIdUser extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "device_id")
    private String deviceId;

    @Column(name = "user_id")
    private String userId;

    @Column(name = "version")
    private String version;

    @Column(name = "os")
    private String os;

    @Column(name = "device_name")
    private String deviceName;

    @Column(name = "is_enable_notification")
    private Boolean isEnableNotification;
}
