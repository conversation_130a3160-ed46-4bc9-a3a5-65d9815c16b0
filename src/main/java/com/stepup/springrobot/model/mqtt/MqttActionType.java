package com.stepup.springrobot.model.mqtt;

import java.util.HashMap;
import java.util.Map;

public enum MqttActionType {
    publish("publish"),
    subscribe("subscribe"),
    all("all");

    private static final Map<String, MqttActionType> MQTT_MESSAGE_TYPE_HASH_MAP = new HashMap<>();

    private final String type;

    MqttActionType(String type) {
        this.type = type;
    }

    static {
        for (MqttActionType e : values()) {
            MQTT_MESSAGE_TYPE_HASH_MAP.put(e.type, e);
        }
    }

    public static MqttActionType from(String type) {
        return MQTT_MESSAGE_TYPE_HASH_MAP.get(type);
    }

    public String getType() {
        return this.type;
    }
}
