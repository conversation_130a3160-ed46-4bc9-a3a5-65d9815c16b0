package com.stepup.springrobot.model.mqtt;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.UUID;


@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "mqtt_users", indexes = {
        @Index(name = "mqtt_users_profile_id_index", columnList = "profile_id", unique = true),
        @Index(name = "mqtt_users_profile_username_index", columnList = "username", unique = true)
})
public class MqttUser extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", columnDefinition = "VARCHAR(50)")
    private String id;

    @Column(name = "profile_id")
    private UUID profileId;

    private String username;

    private String password;

    @Builder.Default
    @Column(name = "is_admin", columnDefinition = "boolean default false")
    private Boolean isAdmin = false;

    @Builder.Default
    @Column(name = "is_active", columnDefinition = "boolean default true")
    private Boolean isActive = true;
}
