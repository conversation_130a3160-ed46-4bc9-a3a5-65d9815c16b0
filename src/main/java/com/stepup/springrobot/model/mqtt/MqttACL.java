package com.stepup.springrobot.model.mqtt;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;


@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "mqtt_acls", indexes = {
        @Index(name = "mqtt_acls_user_id_index", columnList = "mqtt_user_id")
})
public class MqttACL extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", columnDefinition = "VARCHAR(50)")
    private String id;

    @Column(name = "mqtt_user_id")
    private String mqttUserId;

    private String topic;

    private Long role;

    @Enumerated(EnumType.STRING)
    private MqttActionType action;

    @Enumerated(EnumType.STRING)
    private MqttPermissionType permission;
}
