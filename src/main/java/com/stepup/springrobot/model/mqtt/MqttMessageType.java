package com.stepup.springrobot.model.mqtt;

import java.util.HashMap;
import java.util.Map;

public enum MqttMessageType {
    WAKE_UP("WAKE_UP"),
    ALERT("ALERT"),
    MUSIC("<PERSON>US<PERSON>"),
    STORY("STORY"),
    SCAN_WIFI("SCAN_WIFI"),
    CONNECT_WIFI("CONNECT_WIFI"),
    UPDATE_FIRMWARE("UPDATE_FIRMWARE"),
    VOLUME("VOLUME"),
    SCREEN_EMOTION("SCREEN_EMOTION"),
    SCREEN_BACKGROUND("SCREEN_BACKGROUND"),
    SCREEN_BRIGHTNESS("SCREEN_BRIGHTNESS"),
    UPDATE_SD("UPDATE_SD"),
    ASSIGN_LESSON("ASSIGN_LESSON"),
    FACE_DEMO("FACE_DEMO"),
    DEMO_CUSTOM_EMOTIONS("DEMO_CUSTOM_EMOTIONS"),
    ALARM("ALARM");

    private static final Map<String, MqttMessageType> MQTT_MESSAGE_TYPE_HASH_MAP = new HashMap<>();

    private final String type;

    MqttMessageType(String type) {
        this.type = type;
    }

    static {
        for (MqttMessageType e : values()) {
            MQTT_MESSAGE_TYPE_HASH_MAP.put(e.type, e);
        }
    }

    public static MqttMessageType from(String type) {
        return MQTT_MESSAGE_TYPE_HASH_MAP.get(type);
    }

    public String getType() {
        return this.type;
    }
}
