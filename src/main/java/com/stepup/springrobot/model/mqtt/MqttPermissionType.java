package com.stepup.springrobot.model.mqtt;

import java.util.HashMap;
import java.util.Map;

public enum MqttPermissionType {
    allow("allow"),
    deny("deny");

    private static final Map<String, MqttPermissionType> MQTT_MESSAGE_TYPE_HASH_MAP = new HashMap<>();

    private final String type;

    MqttPermissionType(String type) {
        this.type = type;
    }

    static {
        for (MqttPermissionType e : values()) {
            MQTT_MESSAGE_TYPE_HASH_MAP.put(e.type, e);
        }
    }

    public static MqttPermissionType from(String type) {
        return MQTT_MESSAGE_TYPE_HASH_MAP.get(type);
    }

    public String getType() {
        return this.type;
    }
}
