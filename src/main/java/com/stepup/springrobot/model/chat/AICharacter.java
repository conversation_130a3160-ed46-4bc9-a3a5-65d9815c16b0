package com.stepup.springrobot.model.chat;

import java.util.HashMap;
import java.util.Map;

public enum AI<PERSON>haracter {
    BOT_RESPONSE_CONVERSATION("BOT_RESPONSE_CONVERSATION"),
    FAST_RESPONSE("FAST_RESPONSE"),
    USER("USER");

    public static final Map<String, AI<PERSON>haracter> AI_CHARACTER_HASH_MAP = new HashMap<>();

    private final String character;

    AICharacter(String character) {
        this.character = character;
    }

    static {
        for (AICharacter e : values()) {
            AI_CHARACTER_HASH_MAP.put(e.character, e);
        }
    }

    public static <PERSON><PERSON><PERSON>cter from(String type) {
        return AI_CHARACTER_HASH_MAP.get(type);
    }

    public String getCharacter() {
        return this.character;
    }
}
