
package com.stepup.springrobot.model.chat;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import com.sun.istack.NotNull;
import lombok.*;

import javax.persistence.*;


@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "robot_user_conversation_stt_handler", indexes = {
        @Index(name = "robot_user_stt_handler_user", columnList = "user_id", unique = true)
})
public class RobotUserConversationSTTHandler extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id")
    private String userId;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "speech_to_text")
    private STTHandlerType speechToText;
}
