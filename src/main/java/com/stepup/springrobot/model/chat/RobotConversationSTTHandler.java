
package com.stepup.springrobot.model.chat;

import com.fasterxml.jackson.databind.JsonNode;
import com.stepup.springrobot.model.ModifierTrackingEntity;
import com.sun.istack.NotNull;
import lombok.*;
import org.hibernate.annotations.Type;

import javax.persistence.*;


@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "robot_conversation_stt_handler", indexes = {
    @Index(name = "robot_conversation_stt_handler_type", columnList = "asr_type", unique = true)
})
public class RobotConversationSTTHandler extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "asr_type")
    private STTHandlerType asrType;

    @Type(type = "jsonb")
    @Column(columnDefinition = "jsonb")
    private JsonNode data;
}
