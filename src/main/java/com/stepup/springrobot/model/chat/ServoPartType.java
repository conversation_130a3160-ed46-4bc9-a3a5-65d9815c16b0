package com.stepup.springrobot.model.chat;

import java.util.HashMap;
import java.util.Map;

public enum ServoPartType {
    HEAD("HEAD"),
    BASE("BASE"),
    LEFT_HAND("LEFT_HAND"),
    RIGHT_HAND("RIGHT_HAND");

    public static final Map<String, ServoPartType> SERVO_PART_TYPE_HASH_MAP = new HashMap<>();

    private final String type;

    ServoPartType(String type) {
        this.type = type;
    }

    static {
        for (ServoPartType e : values()) {
            SERVO_PART_TYPE_HASH_MAP.put(e.type, e);
        }
    }

    public static ServoPartType from(String type) {
        return SERVO_PART_TYPE_HASH_MAP.get(type);
    }

    public String getType() {
        return this.type;
    }
}
