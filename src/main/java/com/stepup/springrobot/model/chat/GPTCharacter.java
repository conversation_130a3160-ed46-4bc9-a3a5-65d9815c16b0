package com.stepup.springrobot.model.chat;

import java.util.HashMap;
import java.util.Map;

public enum GPTCharacter {
    system("system"),
    assistant("assistant"),
    user("user");

    public static final Map<String, GPTCharacter> GPT_CHARACTER_HASH_MAP = new HashMap<>();

    private final String character;

    GPTCharacter(String character) {
        this.character = character;
    }

    static {
        for (GPTCharacter e : values()) {
            GPT_CHARACTER_HASH_MAP.put(e.character, e);
        }
    }

    public static GPTCharacter from(String type) {
        return GPT_CHARACTER_HASH_MAP.get(type);
    }

    public String getCharacter() {
        return this.character;
    }
}
