
package com.stepup.springrobot.model.chat;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;

import javax.persistence.*;


@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "robot_user_conversation_llm_bot_map", indexes = {
        @Index(name = "user_llm_bot_user_id", columnList = "user_id")
})
public class RobotUserConversationLLMBotMap extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id")
    private String userId;

    @Column(name = "bot_id")
    private Long botId;
}
