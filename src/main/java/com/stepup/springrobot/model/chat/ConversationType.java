package com.stepup.springrobot.model.chat;

import java.util.HashMap;
import java.util.Map;

public enum ConversationType {
    AI_COACH("AI_COACH"),
    EXTEND_PARAGRAPH("EXTEND_PARAGRAPH"),
    CHEC<PERSON>_GRAMMAR("CHECK_GRAMMAR"),
    SUMMARY("SUMMARY");

    public static final Map<String, ConversationType> CONVERSATION_TYPE_HASH_MAP = new HashMap<>();

    private final String type;

    ConversationType(String type) {
        this.type = type;
    }

    static {
        for (ConversationType e : values()) {
            CONVERSATION_TYPE_HASH_MAP.put(e.type, e);
        }
    }

    public static ConversationType from(String type) {
        return CONVERSATION_TYPE_HASH_MAP.get(type);
    }

    public String getType() {
        return this.type;
    }
}
