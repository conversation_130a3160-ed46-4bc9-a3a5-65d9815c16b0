package com.stepup.springrobot.model.chat;

import java.util.HashMap;
import java.util.Map;

public enum MediaType {
    IMAGE("IMAGE"),
    VIDEO("VIDEO");

    public static final Map<String, MediaType> MEDIA_TYPE_HASH_MAP = new HashMap<>();

    private final String type;

    MediaType(String type) {
        this.type = type;
    }

    static {
        for (MediaType e : values()) {
            MEDIA_TYPE_HASH_MAP.put(e.type, e);
        }
    }

    public static MediaType from(String type) {
        return MEDIA_TYPE_HASH_MAP.get(type);
    }

    public String getType() {
        return this.type;
    }
}
