package com.stepup.springrobot.model.chat;

import java.util.HashMap;
import java.util.Map;

public enum BotSourceType {
    IELTS("IELTS"),
    LLM("LLM");

    public static final Map<String, BotSourceType> BOT_SOURCE_TYPE_HASH_MAP = new HashMap<>();

    private final String type;

    BotSourceType(String type) {
        this.type = type;
    }

    static {
        for (BotSourceType e : values()) {
            BOT_SOURCE_TYPE_HASH_MAP.put(e.type, e);
        }
    }

    public static BotSourceType from(String type) {
        if (type == null) {
            return null;
        }

        return BOT_SOURCE_TYPE_HASH_MAP.get(type);
    }

    public String getType() {
        return this.type;
    }
}
