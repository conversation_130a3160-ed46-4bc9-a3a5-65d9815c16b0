package com.stepup.springrobot.model.chat;

import java.util.HashMap;
import java.util.Map;

public enum ConversationLogType {
    LLM_RESPONSE("LLM_RESPONSE", "llm_response"),
    FAST_RESPONSE("FAST_RESPONSE", "fast_response"),
    SERVER_RESPONSE("SERVER_RESPONSE", "server_response");

    public static final Map<String, ConversationLogType> CONVERSATION_LOG_TYPE_HASH_MAP = new HashMap<>();

    private final String type;

    private final String description;

    ConversationLogType(String type, String description) {
        this.type = type;
        this.description = description;
    }

    static {
        for (ConversationLogType e : values()) {
            CONVERSATION_LOG_TYPE_HASH_MAP.put(e.type, e);
        }
    }

    public static ConversationLogType from(String type) {
        if (type == null) {
            return null;
        }

        return CONVERSATION_LOG_TYPE_HASH_MAP.get(type);
    }

    public String getType() {
        return this.type;
    }

    public String getDescription() {
        return this.description;
    }
}
