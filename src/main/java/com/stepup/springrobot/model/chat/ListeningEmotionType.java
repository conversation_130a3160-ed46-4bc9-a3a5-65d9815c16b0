package com.stepup.springrobot.model.chat;

import java.util.HashMap;
import java.util.Map;

public enum ListeningEmotionType {
    WELCOME("WELCOME"),
    HAPPY("HAPPY");

    public static final Map<String, ListeningEmotionType> EMOTION_TYPE_HASH_MAP = new HashMap<>();

    private final String type;

    ListeningEmotionType(String type) {
        this.type = type;
    }

    static {
        for (ListeningEmotionType e : values()) {
            EMOTION_TYPE_HASH_MAP.put(e.type, e);
        }
    }

    public static ListeningEmotionType from(String type) {
        return EMOTION_TYPE_HASH_MAP.get(type);
    }

    public String getType() {
        return this.type;
    }
}
