package com.stepup.springrobot.model.chat;

import java.util.HashMap;
import java.util.Map;

public enum AnswerModeType {
    RECORDING("RECORDING"),
    BUTTON_2("BUTTON_2"),
    BUTTON_3("BUTTON_3");

    public static final Map<String, AnswerModeType> MODE_TYPE_HASH_MAP = new HashMap<>();

    private final String type;

    AnswerModeType(String type) {
        this.type = type;
    }

    static {
        for (AnswerModeType e : values()) {
            MODE_TYPE_HASH_MAP.put(e.type, e);
        }
    }

    public static AnswerModeType from(String type) {
        if (type == null) {
            return null;
        }

        return MODE_TYPE_HASH_MAP.get(type);
    }

    public String getType() {
        return this.type;
    }
}
