package com.stepup.springrobot.model.chat;

import java.util.HashMap;
import java.util.Map;

public enum STTHandlerType {
    GOOGLE("GOOGLE"),
    ASR("ASR"),
    DEEP_GRAM("DEEP_GRAM");

    public static final Map<String, STTHandlerType> HANDLER_TYPE_HASH_MAP = new HashMap<>();

    private final String type;

    STTHandlerType(String type) {
        this.type = type;
    }

    static {
        for (STTHandlerType e : values()) {
            HANDLER_TYPE_HASH_MAP.put(e.type, e);
        }
    }

    public static STTHandlerType from(String type) {
        return HANDLER_TYPE_HASH_MAP.get(type);
    }

    public String getType() {
        return this.type;
    }
}
