package com.stepup.springrobot.model.chat;

import com.fasterxml.jackson.databind.JsonNode;
import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;
import org.hibernate.annotations.Type;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;


@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "listening_emotions")
public class ListeningEmotion extends ModifierTrackingEntity {
    @Id
    private Long id;

    @NotNull
    private String type;

    @Type(type = "jsonb")
    @Column(columnDefinition = "jsonb")
    private JsonNode data;
}
