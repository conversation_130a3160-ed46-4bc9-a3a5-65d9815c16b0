package com.stepup.springrobot.model.chat;

import java.util.HashMap;
import java.util.Map;

public enum RobotConversationResponseType {
    ASR("ASR"),
    CHAT_RESPONSE("CHAT_RESPONSE"),
    CHAT_STALLING("CHAT_STALLING"),
    PING("PING");

    public static final Map<String, RobotConversationResponseType> RESPONSE_TYPE_HASH_MAP = new HashMap<>();

    private final String type;

    RobotConversationResponseType(String type) {
        this.type = type;
    }

    static {
        for (RobotConversationResponseType e : values()) {
            RESPONSE_TYPE_HASH_MAP.put(e.type, e);
        }
    }

    public static RobotConversationResponseType from(String type) {
        if (type == null) {
            return null;
        }

        return RESPONSE_TYPE_HASH_MAP.get(type);
    }

    public String getType() {
        return this.type;
    }
}
