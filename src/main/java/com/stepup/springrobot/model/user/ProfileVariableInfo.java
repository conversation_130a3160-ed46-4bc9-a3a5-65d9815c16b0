package com.stepup.springrobot.model.user;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "profile_variable_info")
public class ProfileVariableInfo extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "profile_id")
    private String profileId;

    @Column(name = "robot_id", nullable = false)
    private String robotId;

    @NotNull
    private String key;

    @NotNull
    @Column(name = "value", columnDefinition = "TEXT")
    private String value;
}
