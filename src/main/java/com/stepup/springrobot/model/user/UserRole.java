package com.stepup.springrobot.model.user;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum UserRole {
    USER("USER"),
    ADMIN("ADMIN");

    private static final Map<String, UserRole> USER_ROLE_TYPE_HASH_MAP = new HashMap<>();

    private final String type;

    UserRole(String type) {
        this.type = type;
    }

    static {
        for (UserRole e : values()) {
            USER_ROLE_TYPE_HASH_MAP.put(e.type, e);
        }
    }

    public static UserRole from(String type) {
        return USER_ROLE_TYPE_HASH_MAP.get(type);
    }
}
