package com.stepup.springrobot.model.user;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import com.stepup.springrobot.model.onboarding.OnboardingAnswer;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.List;

import static com.stepup.springrobot.common.CodeDefine.URL_AVATAR_USER_DEFAULT;

@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "profiles", indexes = {
        @Index(name = "profile_user", columnList = "user_id"),
        @Index(name = "profile_device", columnList = "device_id", unique = true)
})
public class Profile extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", columnDefinition = "VARCHAR(50)")
    private String id;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "user_id")
    private String userId;

    @Column(name = "device_id")
    private String deviceId;

    @Builder.Default
    private String avatar = URL_AVATAR_USER_DEFAULT;

    @Builder.Default
    @Column(name = "is_current", columnDefinition = "BOOLEAN DEFAULT FALSE")
    private Boolean isCurrent = false;

    @Builder.Default
    @OneToMany(mappedBy = "profile", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<ProfileProduct> purchases = new ArrayList<>();

    @Builder.Default
    @OneToMany(mappedBy = "profile", cascade = CascadeType.ALL, orphanRemoval = true)
    @NotEmpty(message = "Onboarding answers must not be empty")
    private List<OnboardingAnswer> onboardingAnswers = new ArrayList<>();
}
