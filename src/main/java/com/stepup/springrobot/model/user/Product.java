package com.stepup.springrobot.model.user;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum Product {
    ROBOT("ROBOT");

    private static final Map<String, Product> PRODUCT_HASH_MAP = new HashMap<>();

    private final String type;

    Product(String type) {
        this.type = type;
    }

    static {
        for (Product e : values()) {
            PRODUCT_HASH_MAP.put(e.type, e);
        }
    }

    public static Product from(String type) {
        return PRODUCT_HASH_MAP.get(type);
    }
}
