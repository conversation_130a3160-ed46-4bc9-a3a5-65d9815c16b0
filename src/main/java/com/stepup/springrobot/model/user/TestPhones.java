package com.stepup.springrobot.model.user;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;

import javax.persistence.*;

@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "test_phones")
public class TestPhones extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "phone", nullable = false)
    private String phone;

    private String description;
}
