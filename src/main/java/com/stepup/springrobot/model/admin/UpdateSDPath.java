package com.stepup.springrobot.model.admin;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;

import javax.persistence.*;
import javax.validation.constraints.NotNull;


@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "update_sd_path")
public class UpdateSDPath extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    private String path;

    @NotNull
    @Column(name = "description", unique = true)
    private String description;

    @NotNull
    @Column(name = "file_name")
    @JsonProperty("file_name")
    private String fileName;
}
