package com.stepup.springrobot.model.onboarding;

import java.util.HashMap;
import java.util.Map;

public enum OnboardingAnswerType {
    TYPING("TYPING"),
    YEAR("YEAR"),
    SINGLE_CHOICE("SINGLE_CHOICE"),
    TIME("TIME"),
    MULTIPLE_CHOICE("MULTIPLE_CHOICE");

    private static final Map<String, OnboardingAnswerType> ANSWER_TYPE_HASH_MAP = new HashMap<>();

    private final String type;

    OnboardingAnswerType(String type) {
        this.type = type;
    }

    static {
        for (OnboardingAnswerType e : values()) {
            ANSWER_TYPE_HASH_MAP.put(e.type, e);
        }
    }

    public static OnboardingAnswerType from(String type) {
        return ANSWER_TYPE_HASH_MAP.get(type);
    }

    public String getType() {
        return this.type;
    }
}
