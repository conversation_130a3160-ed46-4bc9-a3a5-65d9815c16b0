package com.stepup.springrobot.model.onboarding;

import com.fasterxml.jackson.databind.JsonNode;
import com.stepup.springrobot.model.ModifierTrackingEntity;
import com.stepup.springrobot.model.user.Profile;
import com.vladmihalcea.hibernate.type.json.JsonNodeBinaryType;
import lombok.*;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;

import javax.persistence.*;

@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "onboarding_answer", indexes = {
        @Index(name = "on_boarding_answer_profile", columnList = "profile_id"),
        @Index(name = "on_boarding_answer_age_gender", columnList = "age, gender")
})
@TypeDef(name = "jsonb", typeClass = JsonNodeBinaryType.class)
public class OnboardingAnswer extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "profile_id")
    private Profile profile;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "age")
    private Integer age;

    @Column(name = "gender")
    private String gender;

    @Type(type = "jsonb")
    @Column(name = "additional_data", columnDefinition = "jsonb")
    private JsonNode additionalData;
}
