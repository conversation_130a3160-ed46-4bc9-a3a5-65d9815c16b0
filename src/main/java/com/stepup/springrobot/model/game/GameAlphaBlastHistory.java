package com.stepup.springrobot.model.game;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;

import javax.persistence.*;
import javax.validation.constraints.NotNull;


@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "game_alpha_blast_history", indexes = {
        @Index(name = "game_alpha_blast_history_profile", columnList = "profile_id")
})
public class GameAlphaBlastHistory extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    @Column(name = "profile_id")
    private String profileId;

    @Column(name = "word_count")
    private Integer wordCount;

    @Column(name = "character_count")
    private Integer characterCount;

    @Column(name = "score")
    private Long score;
}
