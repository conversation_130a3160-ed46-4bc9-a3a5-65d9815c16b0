package com.stepup.springrobot.model.game;

import java.util.HashMap;
import java.util.Map;

public enum GameType {
    ECHO_TOWER("ECHO_TOWER");

    public static final Map<String, GameType> GAME_TYPE_HASH_MAP = new HashMap<>();

    private final String type;

    GameType(String type) {
        this.type = type;
    }

    static {
        for (GameType e : values()) {
            GAME_TYPE_HASH_MAP.put(e.type, e);
        }
    }

    public static GameType from(String type) {
        if (type == null) {
            return null;
        }

        return GAME_TYPE_HASH_MAP.get(type);
    }

    public String getType() {
        return this.type;
    }
}
