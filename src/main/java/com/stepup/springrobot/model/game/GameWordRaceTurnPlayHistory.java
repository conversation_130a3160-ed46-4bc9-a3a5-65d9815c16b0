package com.stepup.springrobot.model.game;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;

import javax.persistence.*;


@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "game_word_race_turn_play_history", indexes = {
        @Index(name = "game_word_race_turn_play_profile", columnList = "profile_id")
})
public class GameWordRaceTurnPlayHistory extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "profile_id")
    private String profileId;

    @Column(name = "turn_play")
    private Integer turnPlay;

    private String word;

    @Column(name = "is_correct", columnDefinition = "boolean default false")
    private Boolean isCorrect;

    @Column(name = "response_time")
    private Double responseTime;
}
