package com.stepup.springrobot.model.game;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;

import javax.persistence.*;

@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "game_echo_tower_words", indexes = {
        @Index(name = "game_echo_tower_words_floor", columnList = "floor_id")
})
public class GameEchoTowerWord extends ModifierTrackingEntity {
    @Id
    private Long id;

    @Column(name = "floor_id")
    private Long floorId;

    @Column(name = "orders")
    private Double order;

    private String word;
}
