package com.stepup.springrobot.model.game;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;

import javax.persistence.*;


@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "game_word_race_history", indexes = {
        @Index(name = "game_word_race_history_profile", columnList = "profile_id")
})
public class GameWordRaceHistory extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "profile_id")
    private String profileId;

    @Column(name = "word_count")
    private Integer wordCount;

    @Column(name = "turn_play")
    private Integer turnPlay;

    @Column(name = "current_score")
    private Long score;
}
