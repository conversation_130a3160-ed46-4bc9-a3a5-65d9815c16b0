package com.stepup.springrobot.model.game;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;

import javax.persistence.*;
import javax.validation.constraints.NotNull;


@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "game_word_race_state", indexes = {
        @Index(name = "game_word_race_state_profile", columnList = "profile_id", unique = true)
})
public class GameWordRaceState extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    @Column(name = "profile_id")
    private String profileId;

    @Column(name = "word_count")
    private Integer wordCount;

    @Column(name = "score")
    private Long score;
}
