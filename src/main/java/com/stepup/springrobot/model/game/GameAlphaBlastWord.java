package com.stepup.springrobot.model.game;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "game_alpha_blast_words")
public class GameAlphaBlastWord extends ModifierTrackingEntity {
    @Id
    private Long id;

    @NotNull
    private String word;
}
