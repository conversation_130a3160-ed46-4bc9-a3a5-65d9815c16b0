package com.stepup.springrobot.model.game;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;

import javax.persistence.*;


@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "game_echo_tower_floors")
public class GameEchoTowerFloor extends ModifierTrackingEntity {
    @Id
    private Long id;

    @Column(name = "orders")
    private Double order;

    private String name;
}
