package com.stepup.springrobot.model.game;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;

import javax.persistence.*;


@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "game_echo_tower_floor_turn_play", indexes = {
        @Index(name = "game_echo_tower_floor_turn_play_profile", columnList = "profile_id, turn_play", unique = true)
})
public class GameEchoTowerFloorTurnPlay extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "turn_play")
    private Integer turnPlay;

    @Column(name = "profile_id")
    private String profileId;

    @Column(name = "player_health")
    private Integer playerHealth;

    @Column(name = "current_monster_health")
    private Integer currentMonsterHealth;

    @Column(name = "current_floor_id")
    private Long currentFloorId;

    @Column(name = "current_score")
    private Long score;
}
