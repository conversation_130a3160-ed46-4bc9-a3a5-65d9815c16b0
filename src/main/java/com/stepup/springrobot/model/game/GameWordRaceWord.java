package com.stepup.springrobot.model.game;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;

import javax.persistence.*;

@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "game_word_race_words")
public class GameWordRaceWord extends ModifierTrackingEntity {
    @Id
    private Long id;

    private String word;

    @Column(name = "part_one")
    private String partOne;

    @Column(name = "part_two")
    private String partTwo;

    @Column(name = "part_three")
    private String partThree;

    @Column(name = "is_power_up", columnDefinition = "boolean default false")
    private Boolean isPowerUp;
}
