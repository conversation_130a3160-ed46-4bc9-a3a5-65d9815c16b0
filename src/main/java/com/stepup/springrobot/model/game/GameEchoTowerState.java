package com.stepup.springrobot.model.game;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;

import javax.persistence.*;
import javax.validation.constraints.NotNull;


@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "game_echo_tower_state", indexes = {
        @Index(name = "game_echo_tower_state_profile", columnList = "profile_id", unique = true)
})
public class GameEchoTowerState extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    @Column(name = "profile_id")
    private String profileId;

    private Long floorId;

    @Column(name = "score")
    private Long score;
}
