package com.stepup.springrobot.model.game;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;

import javax.persistence.*;

@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "game_echo_tower_history", indexes = {
        @Index(name = "game_echo_tower_history_profile", columnList = "profile_id, word_id, turn_play")
})
public class GameEchoTowerHistory extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "profile_id")
    private String profileId;

    @Column(name = "word_id")
    private Long wordId;

    @Column(name = "turn_play")
    private Integer turnPlay;

    @Column(name = "data", columnDefinition = "TEXT")
    private String data;

    @Column(name = "plus_score")
    private Integer plusScore;
}
