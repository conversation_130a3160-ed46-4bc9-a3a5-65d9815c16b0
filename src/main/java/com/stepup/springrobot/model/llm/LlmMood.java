package com.stepup.springrobot.model.llm;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import javax.validation.constraints.NotNull;


@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "llm_moods")
public class LlmMood extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", columnDefinition = "VARCHAR(50)")
    private String id;

    @NotNull
    @Column(name = "mood_name")
    private String moodName;

    @NotNull
    @Column(name = "description", columnDefinition = "text")
    private String description;

    @NotNull
    @Column(name = "example", columnDefinition = "text")
    private String example;
}
