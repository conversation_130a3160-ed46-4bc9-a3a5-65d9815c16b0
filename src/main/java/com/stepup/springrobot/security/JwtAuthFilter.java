package com.stepup.springrobot.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.exception.business.user.InvalidTokenException;
import com.stepup.springrobot.exception.business.user.TokenExpiredException;
import com.stepup.springrobot.exception.dto.ErrorResponse;
import com.stepup.springrobot.service.auth.UserDetailsServiceImpl;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Log4j2
@Component
public class JwtAuthFilter extends OncePerRequestFilter {
    @Autowired
    private JwtService jwtService;

    @Autowired
    private UserDetailsServiceImpl userDetailsServiceImpl;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        try {
            String path = request.getRequestURI();
            // Skip JWT check for permitAll endpoints
            if (path.startsWith("/robot/api/v2/device/")) {
                filterChain.doFilter(request, response);
                return;
            }

            String authHeader = request.getHeader("Authorization");
            String token;
            String phone;
            if (authHeader != null) {
                log.info("Authorization header: {}", authHeader);

                token = authHeader.startsWith("Bearer ") ? authHeader.substring(7) : authHeader;
                log.info("Extracted token: {}", token);
                jwtService.validateToken(token);
                phone = jwtService.extractPhone(token);
                log.info("Extracted phone: {}", phone);

                if (phone != null && SecurityContextHolder.getContext().getAuthentication() == null) {
                    UserDetails userDetails = userDetailsServiceImpl.loadUserByUsername(phone);
                    log.info("Authenticated user: {}", userDetails.getAuthorities());
                    UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
                    authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                    SecurityContextHolder.getContext().setAuthentication(authenticationToken);
                }
            }

            filterChain.doFilter(request, response);
        } catch (TokenExpiredException | InvalidTokenException e) {
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            response.setContentType("application/json");
            response.getWriter().write(objectMapper.writeValueAsString(ErrorResponse.builder()
                    .message(e.getMessage())
                    .status(e.getStatus())
                    .build()));
        }
    }
}
