package com.stepup.springrobot.security.robot;

import com.stepup.springrobot.model.robot.Robot;
import com.stepup.springrobot.model.robot.RobotIssueToken;
import com.stepup.springrobot.repository.robot.RobotIssueTokenRepository;
import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.security.Key;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Optional;

@Slf4j
@Service
public class RobotJwtService {
    private static final String SECRET = "robot_simple_secret_key_12345678901234567890";
    private static final long EXPIRATION_MS = 1000 * 60 * 60 * 24; // 24 hours
    private static final long WS_TOKEN_EXPIRATION_MS = 1000 * 60 * 5; // 5 minutes

    @Autowired
    private RobotIssueTokenRepository robotIssueTokenRepository;

    private Key getSignKey() {
        return Keys.hmacShaKeyFor(SECRET.getBytes());
    }

    public String generateToken(Robot robot) {
        String token = Jwts.builder()
                .setSubject(robot.getDeviceId())
                .claim("robot_id", robot.getDeviceId())
                .claim("device_id", robot.getDeviceId())
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + EXPIRATION_MS))
                .signWith(getSignKey(), SignatureAlgorithm.HS256)
                .compact();

        robotIssueTokenRepository.deleteByRobotId(robot.getDeviceId());

        robotIssueTokenRepository.save(RobotIssueToken.builder()
                .token(token)
                .deviceId(robot.getDeviceId())
                .robotId(robot.getDeviceId())
                .createdAt(LocalDateTime.now())
                .build());
        return token;
    }

    public boolean validateToken(String token) {
        try {
            log.info("================Validate robot token: {}", token);
            Jws<Claims> claimsJws = Jwts.parserBuilder()
                    .setSigningKey(getSignKey())
                    .build()
                    .parseClaimsJws(token);
            Date expiration = claimsJws.getBody().getExpiration();
            if (expiration.before(new Date()))
                return false;
            Optional<RobotIssueToken> tokenOpt = robotIssueTokenRepository.findByToken(token);
            return tokenOpt.isPresent();
        } catch (JwtException e) {
            return false;
        }
    }

    public void revokeToken(String token) {
        log.info("Revoke token: {}", token);
        robotIssueTokenRepository.deleteByToken(token);
    }

    public String extractRobotId(String token) {
        Claims claims = Jwts.parserBuilder()
                .setSigningKey(getSignKey())
                .build()
                .parseClaimsJws(token)
                .getBody();
        return claims.get("robot_id", String.class);
    }

    public String generateWebSocketToken(String originalToken) {
        Claims claims = Jwts.parserBuilder()
                .setSigningKey(getSignKey())
                .build()
                .parseClaimsJws(originalToken)
                .getBody();

        String wsToken = Jwts.builder()
                .setSubject(claims.getSubject())
                .claim("robot_id", claims.get("robot_id"))
                .claim("device_id", claims.get("device_id"))
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + WS_TOKEN_EXPIRATION_MS))
                .signWith(getSignKey(), SignatureAlgorithm.HS256)
                .compact();

        robotIssueTokenRepository.save(RobotIssueToken.builder()
                .token(wsToken)
                .deviceId(claims.get("device_id", String.class))
                .robotId(claims.get("robot_id", String.class))
                .createdAt(LocalDateTime.now())
                .build());

        return wsToken;
    }
}