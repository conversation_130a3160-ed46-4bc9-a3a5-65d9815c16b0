package com.stepup.springrobot.security.robot;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.exception.dto.ErrorResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collections;

public class RobotJwtAuthFilter extends OncePerRequestFilter {
    private static final Logger log = LoggerFactory.getLogger(RobotJwtAuthFilter.class);
    private final RobotJwtService robotJwtService;

    private final ObjectMapper objectMapper;

    public RobotJwtAuthFilter(RobotJwtService robotJwtService, ObjectMapper objectMapper) {
        this.robotJwtService = robotJwtService;
        this.objectMapper = objectMapper;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        String path = request.getRequestURI();
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null) {

            String token = authHeader.substring(7);
            if (!robotJwtService.validateToken(token)) {
                log.warn("Invalid or expired robot token for path: {}", path);
                response.setStatus(HttpStatus.UNAUTHORIZED.value());
                response.setContentType("application/json");
                response.getWriter().write(objectMapper.writeValueAsString(ErrorResponse.builder()
                        .message("Invalid or expired robot token")
                        .status(HttpStatus.UNAUTHORIZED.value())
                        .build()));
                return;
            }

            // Set authentication in the security context
            String robotId = robotJwtService.extractRobotId(token);
            UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(robotId, null,
                    Collections.emptyList());
            SecurityContextHolder.getContext().setAuthentication(authentication);
            log.info("Robot token validated successfully for path: {}", path);
        }

        filterChain.doFilter(request, response);
    }
}