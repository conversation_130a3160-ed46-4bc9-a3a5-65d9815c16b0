package com.stepup.springrobot.security.robot;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

@Configuration
public class RobotDeviceSecurityConfig {
    private final RobotJwtService robotJwtService;

    private final ObjectMapper objectMapper;

    public RobotDeviceSecurityConfig(RobotJwtService robotJwtService, ObjectMapper objectMapper) {
        this.robotJwtService = robotJwtService;
        this.objectMapper = objectMapper;
    }

    @Bean
    @Order(1)
    public SecurityFilterChain robotDeviceSecurityFilterChain(HttpSecurity http) throws Exception {
        http
                .antMatcher("/robot/api/v2/device/**")
                .csrf(AbstractHttpConfigurer::disable)
                .authorizeRequests(auth -> auth
                        .antMatchers("/robot/api/v2/device/login").permitAll()
                        .anyRequest().authenticated())
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .addFilterBefore(new RobotJwtAuthFilter(robotJwtService, objectMapper), UsernamePasswordAuthenticationFilter.class);
        return http.build();
    }
}