package com.stepup.springrobot.security;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.common.RandomString;
import com.stepup.springrobot.exception.business.user.InvalidTokenException;
import com.stepup.springrobot.exception.business.user.TokenExpiredException;
import com.stepup.springrobot.model.auth.UserIssueToken;
import com.stepup.springrobot.model.user.User;
import com.stepup.springrobot.repository.auth.UserIssueTokenRepository;
import com.stepup.springrobot.repository.auth.UserRepository;
import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.security.Key;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Log4j2
@Component
public class JwtService {
    public static final String SECRET = "04K1ctpz3cTca+pNNQYQXZX6bQLJEbckmZeU1NtbEPc=";

    @Value("${access_token_duration_in_hours}")
    private Long accessTokenDurationInHour;

    @Value("${max_sign_in_device}")
    private Long maxSignInDevice;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserIssueTokenRepository userIssueTokenRepository;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private RedissonClient redissonClient;

    public String extractPhone(String token) throws JsonProcessingException {
        final Claims claims = extractAllClaims(token);
        Map<String, Object> claimsMap = objectMapper.readValue(objectMapper.writeValueAsString(claims), Map.class);

        return claimsMap.get("phone").toString();
    }

    public Date extractExpiration(String token) {
        return extractClaim(token, Claims::getExpiration);
    }

    public <T> T extractClaim(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = extractAllClaims(token);
        log.info("Claims: {}", claims);
        return claimsResolver.apply(claims);
    }

    public Claims extractAllClaims(String token) {
        try {
            return Jwts
                    .parserBuilder()
                    .setSigningKey(getSignKey())
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
        } catch (ExpiredJwtException e) {
            throw new TokenExpiredException();
        } catch (MalformedJwtException e) {
            throw new InvalidTokenException();
        }
    }

    private boolean checkTokenExpired(String token) {
        return extractExpiration(token).before(new Date());
    }

    public void validateToken(String token) throws JsonProcessingException {
        checkTokenExpired(token);
        boolean isValidJti = checkValidJti(token);
        if (!isValidJti) {
            throw new TokenExpiredException();
        }
    }

    private boolean checkValidJti(String token) throws JsonProcessingException {
        final Claims claims = extractAllClaims(token);
        Map<String, Object> claimsMap = objectMapper.readValue(objectMapper.writeValueAsString(claims), new TypeReference<>() {
        });
        String jti = claimsMap.get("jti").toString();
        if (StringUtils.isEmpty(jti)) {
            log.info("jti is empty");
            return false;
        }

        RMapCache<String, String> users = redissonClient.getMapCache(CodeDefine.REDIS_KEY_USERS_ISSUE_TOKEN);
        if (!users.containsKey(jti)) {
            UserIssueToken userIssueToken = userIssueTokenRepository.findByToken(jti);
            if (userIssueToken == null) {
                return false;
            }

            User user = userRepository.findById(userIssueToken.getUserId()).orElse(null);
            if (user == null) {
                return false;
            }

            log.info("=====load jti token from db====={}", user);

            // cần được update lại redis
            users.put(jti, objectMapper.writeValueAsString(user), 1, TimeUnit.DAYS);
            return true;
        } else {
            try {
                User userDataDTO = objectMapper.readValue(users.get(jti), new TypeReference<>() {
                });
                log.info("=====load jti token from redisson======{}", userDataDTO);
            } catch (Exception e) {
                log.error("Error when load jti token from redisson", e);
            }

            return true;
        }
    }

    public String generateToken(User user, String deviceId) {
        String jti = genJtiToken(user.getId());
        Map<String, Object> claims = new HashMap<>();
        claims.put("jti", jti);
        claims.put("user_id", user.getId());
        claims.put("phone", user.getPhone());
        claims.put("authorities", user.getRoles());

        saveUserIssueTokenAndCheckMaxDeviceUse(user, jti, deviceId);
        return createToken(claims, user.getPhone(), jti);
    }

    private String createToken(Map<String, Object> claims, String phone, String jti) {
        return Jwts.builder()
                .setSubject(phone)
                .setClaims(claims)
                .setIssuedAt(new Date(System.currentTimeMillis()))
                .setExpiration(new Date(System.currentTimeMillis() + 1000 * 60 * 60 * accessTokenDurationInHour))
                .signWith(getSignKey(), SignatureAlgorithm.HS256)
                .setId(jti)
                .compact();
    }

   private void saveUserIssueTokenAndCheckMaxDeviceUse(User user, String jti, String deviceId) {
       List<UserIssueToken> userIssueTokens = userIssueTokenRepository.findByUserIdOrderByIdAsc(user.getId());
       // order by updated_at
       userIssueTokens.sort(Comparator.comparing(UserIssueToken::getUpdatedAt));

       RMapCache<String, String> userTokenMapCache = redissonClient.getMapCache(CodeDefine.REDIS_KEY_USERS_ISSUE_TOKEN);
       Set<String> currentDeviceIds = userIssueTokens.stream().map(UserIssueToken::getDeviceId).collect(Collectors.toSet());
       if (currentDeviceIds.size() >= maxSignInDevice && !currentDeviceIds.contains(deviceId)) {
           UserIssueToken deleteJtiToken = userIssueTokens.get(0);
           userIssueTokenRepository.deleteById(deleteJtiToken.getId());
           userTokenMapCache.remove(deleteJtiToken.getToken());
       }

       UserIssueToken userIssueToken = userIssueTokens.stream().filter(x -> Objects.equals(x.getDeviceId(), deviceId)).findFirst().orElse(null);
       if (userIssueToken != null) {
           userTokenMapCache.remove(userIssueToken.getToken());
           userIssueToken.setToken(jti);
       } else {
           userIssueToken = new UserIssueToken();
           userIssueToken.setToken(jti);
           userIssueToken.setUserId(user.getId());
           userIssueToken.setDeviceId(deviceId);
       }

       userIssueTokenRepository.save(userIssueToken);
       try {
           userTokenMapCache.put(jti, objectMapper.writeValueAsString(user), 1, TimeUnit.DAYS);
       } catch (JsonProcessingException e) {
           log.error("Error when save jti token to redisson", e);
       }
   }

    private Key getSignKey() {
        byte[] keyBytes = SECRET.getBytes();
        return Keys.hmacShaKeyFor(keyBytes);
    }

    /**
     * Sinh ra token cho user, cần check xem token sinh ra có duy nhất ko? Nếu trùng thì cần gen lại
     */
    private String genJtiToken(String userId) {
        int lenStringRandom = 21;
        String jti = new RandomString(lenStringRandom).nextString() + "_" + userId;
        while (userIssueTokenRepository.findByToken(jti) != null) {
            jti = new RandomString(lenStringRandom).nextString() + "_" + userId;
        }

        return jti;
    }
}
