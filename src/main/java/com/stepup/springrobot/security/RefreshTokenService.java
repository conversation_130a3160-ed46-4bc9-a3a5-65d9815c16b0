package com.stepup.springrobot.security;

import com.stepup.springrobot.dto.auth.LoginResDTO;
import com.stepup.springrobot.exception.business.user.RefreshTokenExpiredException;
import com.stepup.springrobot.model.auth.RefreshToken;
import com.stepup.springrobot.model.user.User;
import com.stepup.springrobot.repository.auth.RefreshTokenRepository;
import com.stepup.springrobot.repository.auth.UserRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Optional;
import java.util.UUID;

@Service
public class RefreshTokenService {
    @Value("${refresh_token_duration_in_days}")
    private Long refreshTokenDurationInDays;

    private final RefreshTokenRepository refreshTokenRepository;

    private final UserRepository userRepository;

    private final JwtService jwtService;

    public RefreshTokenService(RefreshTokenRepository refreshTokenRepository, UserRepository userRepository, JwtService jwtService) {
        this.refreshTokenRepository = refreshTokenRepository;
        this.userRepository = userRepository;
        this.jwtService = jwtService;
    }

    public RefreshToken handleCreateRefreshToken(String userId) {
        return createRefreshToken(userId);
    }

    public RefreshToken createRefreshToken(String userId) {
        RefreshToken refreshToken = refreshTokenRepository.findByUserId(userId);
        // expire after 30 days
        Date expiryDate = new Date(System.currentTimeMillis() + refreshTokenDurationInDays * 24 * 60 * 60 * 1000);
        String newRefreshToken = UUID.randomUUID() + "-" + userId;
        if (refreshToken != null) {
            refreshToken.setToken(newRefreshToken);
            refreshToken.setExpiryDate(expiryDate);
        } else {
            refreshToken = RefreshToken.builder()
                    .userId(userId)
                    .token(newRefreshToken)
                    .expiryDate(expiryDate)
                    .build();
        }

        return refreshTokenRepository.save(refreshToken);
    }

    public Optional<RefreshToken> findRefreshTokenByToken(String token) {
        return refreshTokenRepository.findByToken(token);
    }

    public void verifyExpiration(RefreshToken token) {
        if (token.getExpiryDate().before(new Date())) {
            refreshTokenRepository.delete(token);
            throw new RefreshTokenExpiredException();
        }
    }

    public LoginResDTO handleRefreshToken(String token, String deviceId) {
        if (StringUtils.isEmpty(token)) {
            throw new RefreshTokenExpiredException();
        }

        Optional<RefreshToken> oRefreshToken = findRefreshTokenByToken(token);
        if (oRefreshToken.isEmpty()) {
            throw new RefreshTokenExpiredException();
        }

        RefreshToken refreshToken = oRefreshToken.get();
        verifyExpiration(refreshToken);

        User user = userRepository.findById(refreshToken.getUserId()).orElseThrow(RefreshTokenExpiredException::new);
        return LoginResDTO.builder()
                .accessToken(jwtService.generateToken(user, deviceId))
                .refreshToken(createRefreshToken(user.getId()).getToken())
                .build();
    }
}
