package com.stepup.springrobot.security;

import com.stepup.springrobot.model.user.User;
import com.stepup.springrobot.model.user.UserRole;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

public class CustomUserDetails extends User implements UserDetails {
    private String phone;

    private String password;

    Collection<? extends GrantedAuthority> authorities;

    public CustomUserDetails(User userByPhone) {
        this.phone = userByPhone.getPhone();
        this.password = userByPhone.getPassword();
        List<GrantedAuthority> authorities = new ArrayList<>();

        for (UserRole role : userByPhone.getRoles()) {
            authorities.add(new SimpleGrantedAuthority(role.getType()));
        }

        this.authorities = authorities;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return authorities;
    }

    @Override
    public String getUsername() {
        return phone;
    }

    @Override
    public String getPassword() {
        return password;
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return true;
    }
}
