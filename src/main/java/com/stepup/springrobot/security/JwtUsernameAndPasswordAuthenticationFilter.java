package com.stepup.springrobot.security;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.config.HttpUtils;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.auth.LoginReqDTO;
import com.stepup.springrobot.dto.auth.LoginResDTO;
import com.stepup.springrobot.exception.dto.ErrorResponse;
import com.stepup.springrobot.model.user.User;
import com.stepup.springrobot.repository.auth.UserRepository;
import com.stepup.springrobot.service.auth.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Slf4j
public class JwtUsernameAndPasswordAuthenticationFilter extends UsernamePasswordAuthenticationFilter {
    // We use auth manager to validate the user credentials
    private final AuthenticationManager authManager;

    private final ObjectMapper objectMapper;

    private final JwtService jwtService;

    private final RefreshTokenService refreshTokenService;

    private final UserService userService;

    private final UserRepository userRepository;

    private String deviceId;

    private String deviceToken;

    public JwtUsernameAndPasswordAuthenticationFilter(AuthenticationManager authenticationManager, ObjectMapper objectMapper, JwtService jwtService, RefreshTokenService refreshTokenService,
                                                      UserService userService, UserRepository userRepository) {
        this.authManager = authenticationManager;
        this.objectMapper = objectMapper;
        this.jwtService = jwtService;
        this.refreshTokenService = refreshTokenService;
        this.userService = userService;
        this.userRepository = userRepository;
        super.setAuthenticationManager(authenticationManager);
        setFilterProcessesUrl("/robot-user/api/v1/auth/login");
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) throws AuthenticationException {
        String ipAddress = HttpUtils.getClientIpAddress(request);
        try {
            LoginReqDTO loginReqDTO = objectMapper.readValue(request.getInputStream(), new TypeReference<>() {
            });
            log.info("====attempt Login =======>ip: {}, request: {}", ipAddress, objectMapper.writeValueAsString(loginReqDTO));

            UsernamePasswordAuthenticationToken authToken = new UsernamePasswordAuthenticationToken(loginReqDTO.getPhone(), loginReqDTO.getPassword());
            setDetails(request, authToken);

            this.deviceId = loginReqDTO.getDeviceId();
            this.deviceToken = loginReqDTO.getDeviceToken();

            return this.authManager.authenticate(authToken);
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    protected void successfulAuthentication(HttpServletRequest request, HttpServletResponse response, FilterChain chain, Authentication authResult) throws IOException, ServletException {
        response.setContentType("application/json");
        User user = userRepository.findByPhone(authResult.getName());
        LoginResDTO loginResDTO = LoginResDTO.builder()
                .accessToken(jwtService.generateToken(user, deviceId))
                .refreshToken(refreshTokenService.handleCreateRefreshToken(user.getId()).getToken())
                .build();

        if (!StringUtils.isEmpty(deviceId) && !StringUtils.isEmpty(deviceToken)) {
            userService.updateNotificationDeviceToken(deviceId, deviceToken, user);
        }

        DataResponseDTO<LoginResDTO> dataResponseDTO = new DataResponseDTO<>(CodeDefine.OK, "Thành công", loginResDTO);
        objectMapper.writeValue(response.getOutputStream(), dataResponseDTO);
    }

    @Override
    protected void unsuccessfulAuthentication(HttpServletRequest request, HttpServletResponse response, AuthenticationException failed) throws IOException, ServletException {
        log.error("Authentication error: {}", failed.getMessage());

        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json");

        if (failed instanceof BadCredentialsException) {
            ErrorResponse errorResponse = ErrorResponse.builder()
                    .status(HttpServletResponse.SC_UNAUTHORIZED)
                    .message("Sai mật khẩu")
                    .build();
            objectMapper.writeValue(response.getOutputStream(), errorResponse);
            return;
        }

        ErrorResponse errorResponse = ErrorResponse.builder()
                .status(HttpServletResponse.SC_UNAUTHORIZED)
                .message(failed.getMessage())
                .build();
        objectMapper.writeValue(response.getOutputStream(), errorResponse);
    }
}
