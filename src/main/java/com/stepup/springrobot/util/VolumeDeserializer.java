package com.stepup.springrobot.util;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;

import java.io.IOException;

/**
 * Custom deserializer for Integer volume field to handle both numeric and string inputs
 * Handles cases like: 5, "5", "5.0", null
 */
public class VolumeDeserializer extends JsonDeserializer<Integer> {

    @Override
    public Integer deserialize(JsonParser jp, DeserializationContext ctxt) throws IOException {
        JsonNode node = jp.getCodec().readTree(jp);

        // Handle null values
        if (node.isNull()) {
            return null;
        }

        // Handle numeric values (direct integer)
        if (node.isInt()) {
            return node.asInt();
        }

        // Handle string values (e.g., "5", "5.0")
        if (node.isTextual()) {
            String textValue = node.asText().trim();

            // Handle empty or blank strings
            if (textValue.isEmpty()) {
                return null;
            }

            try {
                // Parse as double first to handle "5.0" format, then convert to int
                double doubleValue = Double.parseDouble(textValue);
                return (int) Math.round(doubleValue);
            } catch (NumberFormatException e) {
                throw new IOException("Cannot deserialize volume from string: " + textValue, e);
            }
        }

        // Handle numeric values that come as double/float
        if (node.isNumber()) {
            return (int) Math.round(node.asDouble());
        }

        throw new IOException("Cannot deserialize volume from: " + node + " (type: " + node.getNodeType() + ")");
    }
}
