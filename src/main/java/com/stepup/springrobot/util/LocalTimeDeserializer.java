package com.stepup.springrobot.util;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;

import java.io.IOException;
import java.time.LocalTime;

/**
 * Custom deserializer for LocalTime to handle JSON objects with hour, minute, second, and nano properties
 */
public class LocalTimeDeserializer extends JsonDeserializer<LocalTime> {

    @Override
    public LocalTime deserialize(JsonParser jp, DeserializationContext ctxt) throws IOException {
        JsonNode node = jp.getCodec().readTree(jp);

        // Handle string format (e.g., "13:45:30")
        if (node.isTextual()) {
            return LocalTime.parse(node.asText());
        }

        // Handle object format (e.g., {"hour": 13, "minute": 45, "second": 30, "nano": 0})
        if (node.isObject()) {
            int hour = node.has("hour") ? Integer.parseInt(node.get("hour").asText()) : 0;
            int minute = node.has("minute") ? Integer.parseInt(node.get("minute").asText()) : 0;
            int second = node.has("second") ? Integer.parseInt(node.get("second").asText()) : 0;
            int nano = node.has("nano") ? node.get("nano").asInt() : 0;

            return LocalTime.of(hour, minute, second, nano);
        }

        // Handle array format (e.g., [13, 45, 30])
        if (node.isArray() && node.size() >= 2) {
            int hour = node.get(0).asInt();
            int minute = node.get(1).asInt();
            int second = node.size() > 2 ? node.get(2).asInt() : 0;
            int nano = node.size() > 3 ? node.get(3).asInt() : 0;

            return LocalTime.of(hour, minute, second, nano);
        }

        throw new IOException("Cannot deserialize LocalTime from: " + node);
    }
}
