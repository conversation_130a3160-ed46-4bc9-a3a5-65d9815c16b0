package com.stepup.springrobot.util;

import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.Objects;
import java.util.UUID;

public class FileUtil {

    private FileUtil() {
        // Private constructor to prevent instantiation
    }

    public static File convertMultiPartToFile(MultipartFile file) throws IOException {
        File convertedFile = new File(Objects.requireNonNull(file.getOriginalFilename()));
        try (FileOutputStream fos = new FileOutputStream(convertedFile)) {
            fos.write(file.getBytes());
        }
        return convertedFile;
    }

    public static String generateFileName(MultipartFile multipartFile) {
        return UUID.randomUUID() + "-" + Objects.requireNonNull(multipartFile.getOriginalFilename()).replace(" ", "_");
    }

    public static String getFileExtension(String fileName) {
        return fileName.substring(fileName.lastIndexOf("."));
    }

    /**
     * Convert Base64 encoded string to File
     *
     * @param base64Content Base64 encoded content
     * @param fileName      Original file name
     * @return Converted File
     * @throws IOException If an I/O error occurs
     */
    public static File convertBase64ToFile(String base64Content, String fileName) throws IOException {
        // Remove potential Base64 prefix like "data:image/jpeg;base64,"
        if (base64Content.contains(",")) {
            base64Content = base64Content.split(",")[1];
        }

        // Decode Base64 string
        byte[] decodedBytes = Base64.getDecoder().decode(base64Content);

        // Create a temporary file
        File convertedFile = new File(fileName);
        try (FileOutputStream fos = new FileOutputStream(convertedFile)) {
            fos.write(decodedBytes);
        }

        return convertedFile;
    }
}