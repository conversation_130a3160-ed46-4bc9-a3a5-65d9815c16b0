package com.stepup.springrobot.util;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;

import java.io.IOException;

/**
 * Custom deserializer for Double voice_speed field to handle both numeric and string inputs
 * Handles cases like: 1.0, "1.0", "1", null
 */
public class VoiceSpeedDeserializer extends JsonDeserializer<Double> {

    @Override
    public Double deserialize(JsonParser jp, DeserializationContext ctxt) throws IOException {
        JsonNode node = jp.getCodec().readTree(jp);

        // Handle null values
        if (node.isNull()) {
            return null;
        }

        // Handle numeric values (direct double/float)
        if (node.isNumber()) {
            return node.asDouble();
        }

        // Handle string values (e.g., "1.0", "1", "0.9")
        if (node.isTextual()) {
            String textValue = node.asText().trim();
            
            // Handle empty or blank strings
            if (textValue.isEmpty()) {
                return null;
            }
            
            try {
                return Double.parseDouble(textValue);
            } catch (NumberFormatException e) {
                throw new IOException("Cannot deserialize voice_speed from string: " + textValue, e);
            }
        }

        throw new IOException("Cannot deserialize voice_speed from: " + node + " (type: " + node.getNodeType() + ")");
    }
}
