package com.stepup.springrobot.mapper;

import com.stepup.springrobot.dto.onboarding.OnboardingQuestionV2DTO;
import com.stepup.springrobot.model.onboarding.OnboardingQuestion;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

import java.util.List;

/**
 * <AUTHOR>
 * @since : 6/28/20, Sun
 */
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface OnBoardingQuestionMapper {
    List<OnboardingQuestionV2DTO> toOnboardingQuestionV2DTOs(List<OnboardingQuestion> onboardingQuestionV2s);
}
