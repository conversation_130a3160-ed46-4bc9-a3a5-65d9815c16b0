package com.stepup.springrobot.repository.game;

import com.stepup.springrobot.model.robot.RobotFirmwareVersion;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface RobotFirmwareVersionRepository extends JpaRepository<RobotFirmwareVersion, Long> {
  @Transactional
  @Modifying
  @Query(value = "TRUNCATE TABLE robot_firmware_versions RESTART IDENTITY CASCADE", nativeQuery = true)
  void truncateTable();

  @Query(value = "SELECT * FROM robot_firmware_versions ORDER BY id DESC LIMIT 1", nativeQuery = true)
  RobotFirmwareVersion getLatestFirmware();
}