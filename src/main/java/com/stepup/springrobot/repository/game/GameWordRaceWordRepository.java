package com.stepup.springrobot.repository.game;

import com.stepup.springrobot.model.game.GameWordRaceWord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface GameWordRaceWordRepository extends JpaRepository<GameWordRaceWord, Long> {
    @Transactional
    @Modifying
    @Query(value = "TRUNCATE TABLE game_word_race_words RESTART IDENTITY CASCADE", nativeQuery = true)
    void truncateTable();
}