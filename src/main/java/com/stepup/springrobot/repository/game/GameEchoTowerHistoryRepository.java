package com.stepup.springrobot.repository.game;

import com.stepup.springrobot.model.game.GameEchoTowerHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface GameEchoTowerHistoryRepository extends JpaRepository<GameEchoTowerHistory, Long> {
    @Query(value = "SELECT geth.* " +
            "FROM game_echo_tower_history geth " +
            "         JOIN game_echo_tower_words getw ON geth.word_id = getw.id " +
            "WHERE geth.profile_id = :profile_id " +
            "  AND getw.floor_id = :floor_id " +
            "  AND geth.turn_play = :turn_play " +
            "ORDER BY getw.orders", nativeQuery = true)
    List<GameEchoTowerHistory> findByProfileIdAndFloorId(@Param("profile_id") String profileId, @Param("floor_id") Long floorId, @Param("turn_play") Long turnPlay);

}