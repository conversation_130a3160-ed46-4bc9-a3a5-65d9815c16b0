package com.stepup.springrobot.repository.game;

import com.stepup.springrobot.model.game.GameEchoTowerWord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface GameEchoTowerWordRepository extends JpaRepository<GameEchoTowerWord, Long> {
    @Transactional
    @Modifying
    @Query(value = "TRUNCATE TABLE game_echo_tower_words RESTART IDENTITY CASCADE", nativeQuery = true)
    void truncateTable();

    List<GameEchoTowerWord> findByFloorIdOrderByOrderAsc(Long floorId);

}