package com.stepup.springrobot.repository.game;

import com.stepup.springrobot.model.game.GameEchoTowerFloor;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface GameEchoTowerFloorRepository extends JpaRepository<GameEchoTowerFloor, Long> {
    @Transactional
    @Modifying
    @Query(value = "TRUNCATE TABLE game_echo_tower_floors RESTART IDENTITY CASCADE", nativeQuery = true)
    void truncateTable();
}