package com.stepup.springrobot.repository.notification;

import com.stepup.springrobot.model.notification.UserDeviceToken;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DeviceTokenRepository extends JpaRepository<UserDeviceToken, String> {
    long deleteByDeviceToken(String deviceToken);

    List<UserDeviceToken> findByUserId(String userId);

    UserDeviceToken findByDeviceId(String deviceId);

    long deleteByUserIdAndDeviceId(String userId, String deviceId);
}