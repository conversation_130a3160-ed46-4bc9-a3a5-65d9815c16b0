package com.stepup.springrobot.repository.communication;

import com.stepup.springrobot.model.communication.CommunicationRequest;
import com.stepup.springrobot.model.mqtt.MqttMessageType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface CommunicationRequestRepository extends JpaRepository<CommunicationRequest, String> {
    CommunicationRequest findFirstByRobotIdAndUserIdAndTypeOrderByIdDesc(String robotId, String userId, MqttMessageType type);
}