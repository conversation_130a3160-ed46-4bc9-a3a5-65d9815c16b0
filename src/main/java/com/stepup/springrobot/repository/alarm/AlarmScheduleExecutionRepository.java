package com.stepup.springrobot.repository.alarm;

import com.stepup.springrobot.model.alarm.AlarmScheduleExecution;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AlarmScheduleExecutionRepository extends JpaRepository<AlarmScheduleExecution, String> {
    List<AlarmScheduleExecution> findByTimeSchedule(String timeSchedule);
}