package com.stepup.springrobot.repository.alarm;

import com.stepup.springrobot.model.alarm.AlarmSchedule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

@Repository
public interface AlarmScheduleRepository extends JpaRepository<AlarmSchedule, Long> {
    // Find alarm schedules by user ID
    List<AlarmSchedule> findByUserId(String userId);

    // Find active alarm schedules by user ID
    List<AlarmSchedule> findByUserIdAndIsActiveTrue(Long userId);

    // Find alarm schedules by activity ID
    List<AlarmSchedule> findByActivityId(Long activityId);

    // Find alarm schedules by user ID and activity ID
    List<AlarmSchedule> findByUserIdAndActivityId(Long userId, Long activityId);

    /**
     * Find conflicting schedules based on time and days
     * Conflict is defined as:
     * 1. Same user
     * 2. Not the same schedule (if scheduleId is provided)
     * 3. Exact same time (hour and minute)
     * 4. At least one day in common
     *
     * @param userId User ID
     * @param hour Hour of the schedule to check
     * @param minute Minute of the schedule to check
     * @return List of conflicting schedules
     */
    @Query(value = "SELECT * FROM alarm_schedules a " +
            "WHERE a.user_id = :userId " +
            "AND a.is_active = true " +
            "AND a.time_hour = :hour " +
            "AND a.time_minute = :minute",
            nativeQuery = true)
    List<AlarmSchedule> getConflictSchedules(
            @Param("userId") String userId,
            @Param("hour") Integer hour,
            @Param("minute") Integer minute);

        /**
         * Find active alarm schedules by time (hour and minute)
         *
         * @param hour   Hour to check
         * @param minute Minute to check
         * @return List of active alarm schedules matching the time
         */
        @Query("SELECT a FROM AlarmSchedule a WHERE a.timeHour = :hour AND a.timeMinute = :minute AND a.isActive = true")
        List<AlarmSchedule> findActiveSchedulesByTime(@Param("hour") int hour, @Param("minute") int minute);


    @Transactional
    @Modifying
    @Query(value = "DELETE FROM alarm_schedules WHERE id IN :ids AND user_id = :user_id", nativeQuery = true)
    void deleteByIdInAndUserId(@Param("ids") Collection<Long> ids, @Param("user_id") String userId);

    List<AlarmSchedule> findByIdInAndUserId(Collection<Long> ids, String userId);
}
