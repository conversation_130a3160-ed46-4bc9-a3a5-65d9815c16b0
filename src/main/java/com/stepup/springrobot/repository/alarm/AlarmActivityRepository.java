package com.stepup.springrobot.repository.alarm;

import com.stepup.springrobot.model.alarm.AlarmActivity;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AlarmActivityRepository extends JpaRepository<AlarmActivity, Long> {
    @Query(value = "select * from alarm_activities  order by id asc", nativeQuery = true)
    List<AlarmActivity> findAllOrderById();
}
