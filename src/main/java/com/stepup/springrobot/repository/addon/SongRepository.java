package com.stepup.springrobot.repository.addon;

import com.stepup.springrobot.model.addon.Song;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface SongRepository extends JpaRepository<Song, Long> {
    @Transactional
    @Modifying
    @Query(value = "TRUNCATE TABLE songs RESTART IDENTITY CASCADE", nativeQuery = true)
    void truncateTable();

    @Query(value = "SELECT * FROM songs " +
            " ORDER BY orders", nativeQuery = true)
    Page<Song> findAllSongs(Pageable pageable);
}