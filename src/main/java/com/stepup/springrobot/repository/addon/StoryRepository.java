package com.stepup.springrobot.repository.addon;

import com.stepup.springrobot.model.addon.Song;
import com.stepup.springrobot.model.addon.Story;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface StoryRepository extends JpaRepository<Story, Long> {
    @Transactional
    @Modifying
    @Query(value = "TRUNCATE TABLE story RESTART IDENTITY CASCADE", nativeQuery = true)
    void truncateTable();

    @Query(value = "SELECT * FROM story " +
            " ORDER BY orders", nativeQuery = true)
    Page<Story> findAllStories(Pageable pageable);
}