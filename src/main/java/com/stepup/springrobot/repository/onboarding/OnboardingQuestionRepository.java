package com.stepup.springrobot.repository.onboarding;

import com.stepup.springrobot.model.onboarding.OnboardingQuestion;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OnboardingQuestionRepository extends JpaRepository<OnboardingQuestion, Integer> {
    @Modifying
    @Query(value = "TRUNCATE TABLE onboarding_question RESTART IDENTITY CASCADE", nativeQuery = true)
    void truncateTable();

    @Query(value = "SELECT type FROM onboarding_question", nativeQuery = true)
    List<String> getQuestionTypes();
}
