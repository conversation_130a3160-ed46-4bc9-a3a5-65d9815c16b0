package com.stepup.springrobot.repository.sms;

import com.stepup.springrobot.model.sms.SMSSendHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SMSSendHistoryRepository extends JpaRepository<SMSSendHistory, Integer> {
    @Query(value = "select * from sms_send_history where to_phone = ?1 and verified = false and otp = ?2 and action = ?3 and created_at > now() AT TIME ZONE 'Asia/Ho_Chi_Minh' - INTERVAL '5 min' ", nativeQuery = true)
    List<SMSSendHistory> findSMSSendHistoriesByToPhoneAndOtp(String phone, String otp, String smsAction);

    @Query(value = "SELECT * " +
            "FROM sms_send_history " +
            "WHERE to_phone = :phone " +
            "  AND verified = FALSE " +
            "  AND status = TRUE " +
            "  AND otp = :otp " +
            "  AND action = :action " +
            "  AND created_at > NOW() AT TIME ZONE 'Asia/Ho_Chi_Minh' - INTERVAL '5 min' ", nativeQuery = true)
    List<SMSSendHistory> findSMSSendHistoriesByToPhoneAndOtpAndAction(@Param("phone") String phone, @Param("otp") String otp, @Param("action") String smsAction);

    @Query(value = "select * from sms_send_history where to_phone = ?1 and verified = true and action = ?2 and created_at > now() AT TIME ZONE 'Asia/Ho_Chi_Minh' - INTERVAL '5 min' ", nativeQuery = true)
    List<SMSSendHistory> findSMSSendHistoriesByToPhone(String phone, String smsAction);

    @Query(value = "SELECT exists(SELECT * FROM sms_send_history " +
            "WHERE to_phone = :phone " +
            "  AND created_at > NOW() AT TIME ZONE 'Asia/Ho_Chi_Minh' - INTERVAL '3 min' " +
            "  AND verified " +
            "  AND status);", nativeQuery = true)
    boolean existsPhoneVerifyOTP(@Param("phone") String phone);
}