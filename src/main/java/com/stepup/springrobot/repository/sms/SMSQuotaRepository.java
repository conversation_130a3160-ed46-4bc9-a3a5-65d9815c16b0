package com.stepup.springrobot.repository.sms;

import com.stepup.springrobot.model.sms.SMSQuota;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;

@Repository
public interface SMSQuotaRepository extends JpaRepository<SMSQuota, Integer> {
    SMSQuota findSMSQuotaByDatetimeAndService(Integer datetime, String service);

    @Modifying
    @Transactional
    @Query(value = "UPDATE sms_quota " +
            "SET used = sms_quota.used + 1, updated_at = NOW() AT TIME ZONE 'Asia/Ho_Chi_Minh' " +
            "WHERE datetime = :datetime AND service = :service", nativeQuery = true)
    void incrementUsedByDatetimeAndService(@Param("datetime") Integer datetime, @Param("service") String service);
}