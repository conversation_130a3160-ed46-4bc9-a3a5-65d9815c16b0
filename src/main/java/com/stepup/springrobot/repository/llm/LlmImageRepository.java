package com.stepup.springrobot.repository.llm;

import com.stepup.springrobot.model.llm.LlmImage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface LlmImageRepository extends JpaRepository<LlmImage, String> {
    @Transactional
    @Modifying
    @Query(value = "TRUNCATE TABLE llm_images RESTART IDENTITY CASCADE", nativeQuery = true)
    void truncateTable();

    List<LlmImage> findByBotId(Long botId);

    long deleteByBotId(Long botId);
}