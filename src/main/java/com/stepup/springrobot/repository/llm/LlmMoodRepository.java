package com.stepup.springrobot.repository.llm;

import com.stepup.springrobot.model.llm.LlmMood;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface LlmMoodRepository extends JpaRepository<LlmMood, String> {
    @Transactional
    @Modifying
    @Query(value = "TRUNCATE TABLE llm_moods RESTART IDENTITY CASCADE", nativeQuery = true)
    void truncateTable();
}