package com.stepup.springrobot.repository.robot;

import com.stepup.springrobot.model.robot.RobotIssueToken;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Repository
public interface RobotIssueTokenRepository extends JpaRepository<RobotIssueToken, Long> {
    Optional<RobotIssueToken> findByToken(String token);

    @Transactional
    @Modifying
    long deleteByToken(String token);

    @Transactional
    @Modifying
    long deleteByRobotId(String robotId);
}