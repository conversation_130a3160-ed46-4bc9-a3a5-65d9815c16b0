package com.stepup.springrobot.repository.robot;

import com.stepup.springrobot.model.robot.Robot;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface RobotRepository extends JpaRepository<Robot, String> {
    Optional<Robot> findBySerialNumber(String serialNumber);

    Optional<Robot> findByDeviceId(String deviceId);
}