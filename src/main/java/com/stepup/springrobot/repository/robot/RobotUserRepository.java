package com.stepup.springrobot.repository.robot;

import com.stepup.springrobot.model.robot.RobotUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RobotUserRepository extends JpaRepository<RobotUser, Long> {
    List<RobotUser> findByUserIdOrderByIdAsc(String userId);

    List<RobotUser> findByUserId(String userId);

    RobotUser findFirstByUserIdOrderByIdDesc(String userId);

    RobotUser findFirstByRobotIdOrderByIdDesc(String robotId);
}