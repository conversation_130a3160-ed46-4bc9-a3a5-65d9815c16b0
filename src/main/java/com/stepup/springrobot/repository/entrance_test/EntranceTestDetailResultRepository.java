package com.stepup.springrobot.repository.entrance_test;

import com.stepup.springrobot.model.entrance_test.EntranceTestDetailResult;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EntranceTestDetailResultRepository extends JpaRepository<EntranceTestDetailResult, Long> {
    List<EntranceTestDetailResult> findByProfileIdAndTurnPlay(String profileId, Integer turnPlay);

    @Query(value = "SELECT count(etd.id) = count(etdr.id) " +
            "FROM entrance_test_details etd " +
            "         LEFT JOIN entrance_test_detail_results etdr " +
            "                   ON etdr.profile_id = :profile_id AND etd.id = etdr.entrance_test_detail_id AND " +
            "                      etdr.turn_play = :turn_play", nativeQuery = true)
    boolean isUserFinishAllTestDetailsByTurnPlay(@Param("profile_id") String profileId, @Param("turn_play") Integer turnPlay);
}