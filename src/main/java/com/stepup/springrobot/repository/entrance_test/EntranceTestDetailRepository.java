package com.stepup.springrobot.repository.entrance_test;

import com.stepup.springrobot.model.entrance_test.EntranceTestDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface EntranceTestDetailRepository extends JpaRepository<EntranceTestDetail, String> {
    @Transactional
    @Modifying
    @Query(value = "TRUNCATE TABLE entrance_test_details RESTART IDENTITY CASCADE", nativeQuery = true)
    void truncateTable();

    @Query(value = "SELECT * FROM entrance_test_details ORDER BY orders ASC", nativeQuery = true)
    List<EntranceTestDetail> findAllTestDetails();
}