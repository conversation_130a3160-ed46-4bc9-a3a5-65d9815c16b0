package com.stepup.springrobot.repository.entrance_test;

import com.stepup.springrobot.model.entrance_test.EntranceTestProfile;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface EntranceTestProfileRepository extends JpaRepository<EntranceTestProfile, Long> {
  boolean existsByProfileIdAndIsComplete(String profileId, Boolean isComplete);

  List<EntranceTestProfile> findByProfileIdOrderByIdAsc(String profileId);

  @Transactional
  @Modifying
  @Query(value = "UPDATE entrance_test_profiles " +
          "SET is_complete = TRUE, " +
          "    updated_at  = NOW() AT TIME ZONE 'Asia/Ho_Chi_Minh' " +
          "WHERE profile_id = :profile_id " +
          "  AND turn_play = :turn_play", nativeQuery = true)
  void updateStatusByProfileIdAndTurnPlay(@Param("profile_id") String profileId, @Param("turn_play") Integer turnPlay);

  @Query(value = "SELECT * FROM entrance_test_profiles " +
          "WHERE profile_id IN :profile_ids " +
          "  AND is_complete IS TRUE", nativeQuery = true)
  List<EntranceTestProfile> getListCompletedTestByProfileIds(@Param("profile_ids") List<String> profileIds);
}