package com.stepup.springrobot.repository.community;

import com.stepup.springrobot.model.community.FeatureLike;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface FeatureLikeRepository extends JpaRepository<FeatureLike, Long> {
    FeatureLike findByFeatureIdAndUserId(Long featureId, String userId);

    /**
     * Find all likes by a specific user for a list of feature IDs
     *
     * @param featureIds List of feature IDs to check
     * @param userId     User ID to check likes for
     * @return List of FeatureLike objects
     */
    @Query("SELECT fl FROM FeatureLike fl WHERE fl.featureId IN :featureIds AND fl.userId = :userId")
    List<FeatureLike> findUserLikesForFeatures(@Param("featureIds") List<Long> featureIds, @Param("userId") String userId);
}