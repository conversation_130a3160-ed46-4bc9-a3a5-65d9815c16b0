package com.stepup.springrobot.repository.community;

import com.stepup.springrobot.model.community.CommunityFeature;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface CommunityFeatureRepository extends JpaRepository<CommunityFeature, Long> {
    /**
     * Atomically increment the likes count for a feature
     *
     * @param featureId ID of the feature to increment likes for
     */
    @Modifying
    @Query("UPDATE CommunityFeature f SET f.likes = f.likes + 1 WHERE f.id = :featureId")
    void incrementLikes(@Param("featureId") Long featureId);

    /**
     * Atomically decrement the likes count for a feature
     *
     * @param featureId ID of the feature to decrement likes for
     */
    @Modifying
    @Query("UPDATE CommunityFeature f SET f.likes = f.likes - 1 WHERE f.id = :featureId")
    void decrementLikes(@Param("featureId") Long featureId);
}
