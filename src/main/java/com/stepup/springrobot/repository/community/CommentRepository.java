package com.stepup.springrobot.repository.community;

import com.stepup.springrobot.model.community.Comment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CommentRepository extends JpaRepository<Comment, Long> {
    Page<Comment> findByFeatureId(Long featureId, Pageable pageable);

    long countByFeatureId(Long featureId);

    /**
     * Count comments for multiple feature IDs and return pairs of [featureId, count]
     *
     * @param featureIds List of feature IDs to count comments for
     * @return List of Object[] where each array contains [featureId, count] as Longs
     */
    @Query("SELECT c.featureId as featureId, COUNT(c) as commentCount FROM Comment c WHERE c.featureId IN :featureIds GROUP BY c.featureId")
    List<Object[]> countByFeatureIdIn(@Param("featureIds") List<Long> featureIds);

    /**
     * Atomically increment the likes count for a comment
     *
     * @param commentId ID of the comment to increment likes for
     */
    @Modifying
    @Query("UPDATE Comment c SET c.likes = c.likes + 1 WHERE c.id = :commentId")
    void incrementLikes(@Param("commentId") Long commentId);

    /**
     * Atomically decrement the likes count for a comment
     *
     * @param commentId ID of the comment to decrement likes for
     */
    @Modifying
    @Query("UPDATE Comment c SET c.likes = c.likes - 1 WHERE c.id = :commentId")
    void decrementLikes(@Param("commentId") Long commentId);
}