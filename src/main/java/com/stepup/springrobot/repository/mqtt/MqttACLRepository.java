package com.stepup.springrobot.repository.mqtt;

import com.stepup.springrobot.model.mqtt.MqttACL;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MqttACLRepository extends JpaRepository<MqttACL, String> {
    List<MqttACL> findByMqttUserId(String mqttUserId);

    List<MqttACL> findByMqttUserIdAndTopic(String mqttUserId, String topic);
}