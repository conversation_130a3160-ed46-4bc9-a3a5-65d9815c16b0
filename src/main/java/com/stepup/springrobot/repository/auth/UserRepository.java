package com.stepup.springrobot.repository.auth;

import com.stepup.springrobot.model.user.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface UserRepository extends JpaRepository<User, String> {
    User findByPhone(String phone);

    @Transactional
    @Modifying
    @Query(value = "UPDATE users SET phone = ?1, updated_at = NOW() AT TIME ZONE 'Asia/Ho_Chi_Minh' WHERE phone = ?2", nativeQuery = true)
    void updatePhoneById(String newPhone, String oldPhone);
}