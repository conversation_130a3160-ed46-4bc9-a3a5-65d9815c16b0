package com.stepup.springrobot.repository.auth;

import com.stepup.springrobot.model.user.ProfileVariableInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProfileVariableInfoRepository extends JpaRepository<ProfileVariableInfo, Long> {
  List<ProfileVariableInfo> findByRobotIdOr(String robotId);
}