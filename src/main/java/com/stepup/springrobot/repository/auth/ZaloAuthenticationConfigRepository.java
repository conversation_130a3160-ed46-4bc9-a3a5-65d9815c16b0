package com.stepup.springrobot.repository.auth;

import com.stepup.springrobot.model.auth.ZaloAuthenticationConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface ZaloAuthenticationConfigRepository extends JpaRepository<ZaloAuthenticationConfig, Long> {
    @Query(value = "SELECT * FROM zalo_authentication_config LIMIT 1", nativeQuery = true)
    ZaloAuthenticationConfig getZaloAuthenticationConfig();
}