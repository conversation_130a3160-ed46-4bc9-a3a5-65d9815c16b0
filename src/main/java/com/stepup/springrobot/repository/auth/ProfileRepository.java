package com.stepup.springrobot.repository.auth;

import com.stepup.springrobot.model.user.Profile;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface ProfileRepository extends JpaRepository<Profile, String> {
    List<Profile> findByUserId(String userId);

    List<Profile> findByDeviceId(String deviceId);

    @Query(value = "SELECT * FROM profiles WHERE user_id = :user_id AND is_current IS TRUE", nativeQuery = true)
    Profile getCurrentProfileByUserId(@Param("user_id") String userId);

    @Query(value = "SELECT * FROM profiles WHERE device_id = :device_id AND is_current IS TRUE", nativeQuery = true)
    Profile getCurrentProfileByDeviceId(@Param("device_id") String deviceId);

    @Transactional
    @Modifying
    @Query(value = "UPDATE profiles " +
            "SET is_current = FALSE, " +
            "    updated_at = NOW() AT TIME ZONE 'Asia/Ho_Chi_Minh' " +
            "WHERE user_id = :user_id " +
            "  AND id <> :profile_id", nativeQuery = true)
    void deselectNonCurrentProfile(@Param("user_id") String userId, @Param("profile_id") String currentProfileId);

    @Transactional
    @Modifying
    @Query(value = "UPDATE profiles " +
            "SET is_current = (id = :profile_id), " +
            "    updated_at = NOW() AT TIME ZONE 'Asia/Ho_Chi_Minh' " +
            "WHERE user_id = :user_id", nativeQuery = true)
    void setCurrenProfileAndDeselectNonCurrentProfile(@Param("user_id") String userId, @Param("profile_id") String currentProfileId);
}