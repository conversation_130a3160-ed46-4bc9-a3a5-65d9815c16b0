package com.stepup.springrobot.repository;

import com.stepup.springrobot.model.ModelAIProvider;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.stepup.springrobot.model.AIProviderTokens;

import java.util.List;

@Repository
public interface AIProviderTokensRepository extends JpaRepository<AIProviderTokens, String> {
    List<AIProviderTokens> findByProvider(ModelAIProvider provider);
}