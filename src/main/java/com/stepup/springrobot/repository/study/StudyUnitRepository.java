package com.stepup.springrobot.repository.study;

import com.stepup.springrobot.model.study.StudyUnit;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface StudyUnitRepository extends JpaRepository<StudyUnit, Long> {
    @Transactional
    @Modifying
    @Query(value = "TRUNCATE TABLE study_units RESTART IDENTITY CASCADE", nativeQuery = true)
    void truncateTable();

    @Query(value = "SELECT * FROM study_units ORDER BY orders", nativeQuery = true)
    List<StudyUnit> getAllStudyUnits();

}