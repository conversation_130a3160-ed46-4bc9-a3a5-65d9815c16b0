package com.stepup.springrobot.repository.study;

import com.stepup.springrobot.model.study.StudyLesson;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface StudyLessonRepository extends JpaRepository<StudyLesson, Long> {
    @Transactional
    @Modifying
    @Query(value = "TRUNCATE TABLE study_lessons RESTART IDENTITY CASCADE", nativeQuery = true)
    void truncateTable();

    @Query(value = "SELECT * FROM study_lessons WHERE topic_id = :topic_id ORDER BY orders", nativeQuery = true)
    List<StudyLesson> getLessonsByTopicId(@Param("topic_id") Long topicId);
}