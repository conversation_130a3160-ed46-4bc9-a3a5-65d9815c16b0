package com.stepup.springrobot.repository.learn;

import com.stepup.springrobot.model.learn.Lesson;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface LessonRepository extends JpaRepository<Lesson, Long> {
    @Transactional
    @Modifying
    @Query(value = "TRUNCATE TABLE lessons RESTART IDENTITY CASCADE", nativeQuery = true)
    void truncateTable();
}