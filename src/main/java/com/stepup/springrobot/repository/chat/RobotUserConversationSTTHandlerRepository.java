package com.stepup.springrobot.repository.chat;

import com.stepup.springrobot.model.chat.RobotUserConversationSTTHandler;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface RobotUserConversationSTTHandlerRepository extends JpaRepository<RobotUserConversationSTTHandler, Long> {
    RobotUserConversationSTTHandler findByUserId(String userId);
}