package com.stepup.springrobot.repository.chat;

import com.stepup.springrobot.model.chat.ServoAction;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface ServoActionRepository extends JpaRepository<ServoAction, Long> {
  @Transactional
  @Modifying
  @Query(value = "TRUNCATE TABLE servo_actions RESTART IDENTITY CASCADE", nativeQuery = true)
  void truncateTable();

  List<ServoAction> findByType(String type);
}