package com.stepup.springrobot.repository.chat;

import com.stepup.springrobot.model.chat.RobotUserConversationLLMBotMap;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RobotUserConversationLLMBotMapRepository extends JpaRepository<RobotUserConversationLLMBotMap, Long> {
    List<RobotUserConversationLLMBotMap> findByUserId(String userId);
}