package com.stepup.springrobot.repository.chat;

import com.stepup.springrobot.model.chat.ListeningEmotion;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface ListeningEmotionRepository extends JpaRepository<ListeningEmotion, Long> {
    @Transactional
    @Modifying
    @Query(value = "TRUNCATE TABLE listening_emotions RESTART IDENTITY CASCADE", nativeQuery = true)
    void truncateTable();

    List<ListeningEmotion> findByType(String type);
}