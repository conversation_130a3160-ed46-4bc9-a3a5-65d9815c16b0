package com.stepup.springrobot.repository.chat;

import com.stepup.springrobot.model.chat.RobotConversationSTTHandler;
import com.stepup.springrobot.model.chat.STTHandlerType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface RobotConversationSTTHandlerRepository extends JpaRepository<RobotConversationSTTHandler, Long> {
  RobotConversationSTTHandler findByAsrType(STTHandlerType asrType);
}