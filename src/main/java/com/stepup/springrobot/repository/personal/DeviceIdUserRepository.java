package com.stepup.springrobot.repository.personal;

import com.stepup.springrobot.model.personal.DeviceIdUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface DeviceIdUserRepository extends JpaRepository<DeviceIdUser, Long> {
    @Modifying
    @Query(value = "CALL updateUserIdAndRemoveDeviceId(:user_id, :device_id)", nativeQuery = true)
    void updateUserIdAndRemoveDeviceId(@Param("user_id") String userId, @Param("device_id") String deviceId);

    DeviceIdUser findByDeviceId(String deviceId);

    DeviceIdUser findByUserId(String userId);

    boolean existsByDeviceId(String deviceId);
}