package com.stepup.springrobot.service.robot;

import com.stepup.springrobot.model.robot.Robot;
import com.stepup.springrobot.repository.robot.RobotRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class RobotAuthService {

    private final RobotRepository robotRepository;

    @Autowired
    public RobotAuthService(RobotRepository robotRepository) {
        this.robotRepository = robotRepository;
    }

    /**
     * Register a new robot with the given password
     * @param serialNumber The robot's serial number
     * @param rawPassword The robot's raw password
     * @return The registered robot
     */
    public Robot registerRobot(String serialNumber, String deviceId, String rawPassword) {
        Robot robot = Robot.builder()
                .serialNumber(serialNumber)
                .deviceId(deviceId)
                .password(rawPassword)
                .activated(true)
                .build();
        
        return robotRepository.save(robot);
    }
} 