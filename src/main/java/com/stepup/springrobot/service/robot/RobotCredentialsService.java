package com.stepup.springrobot.service.robot;

import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.robot.RobotCredentialsRequestDTO;
import com.stepup.springrobot.dto.robot.RobotCredentialsResponseDTO;
import com.stepup.springrobot.model.robot.Robot;
import com.stepup.springrobot.repository.robot.RobotRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.util.Base64;
import java.util.Optional;

@Service
public class RobotCredentialsService {

    private final RobotRepository robotRepository;
    private final RobotAuthService robotAuthService;
    
    @Value("${message_payload_secret_key}")
    private String robotGlobalKey;

    private final SecureRandom secureRandom = new SecureRandom();

    @Autowired
    public RobotCredentialsService(RobotRepository robotRepository, RobotAuthService robotAuthService) {
        this.robotRepository = robotRepository;
        this.robotAuthService = robotAuthService;
    }

    /**
     * Process a robot credentials request and return the appropriate response
     * @param request The credentials request
     * @return The credentials response
     */
    public DataResponseDTO<RobotCredentialsResponseDTO>  processCredentialsRequest(RobotCredentialsRequestDTO request) {
        Optional<Robot> existingRobot = robotRepository.findByDeviceId(request.getDeviceId());

        String newPassword = generateSecurePassword();
        if (existingRobot.isPresent()) {
            newPassword = existingRobot.get().getPassword();
        } else {
            // For new robots, register and return credentials
            robotAuthService.registerRobot(request.getMacAddress(), request.getDeviceId(), newPassword);
        }

        return new DataResponseDTO<>(CodeDefine.OK, "Retrieve robot credentials successfully",  RobotCredentialsResponseDTO.builder()
                .robotId(request.getDeviceId())
                .password(newPassword)
                .encryptionKey(robotGlobalKey)
                .build());
    }

    /**
     * Generate a secure random password
     * @return A secure random password
     */
    private String generateSecurePassword() {
        byte[] randomBytes = new byte[12];
        secureRandom.nextBytes(randomBytes);
        return Base64.getUrlEncoder().withoutPadding().encodeToString(randomBytes);
    }
} 