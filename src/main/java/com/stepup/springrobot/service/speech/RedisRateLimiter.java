package com.stepup.springrobot.service.speech;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.config.ConfigUtil;
import com.stepup.springrobot.exception.business.request.TooManyRequestException;
import com.stepup.springrobot.model.speech.UserLimitCheckType;
import com.stepup.springrobot.service.SlackWarningSystemService;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.SessionCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Log4j2
@Service
public class RedisRateLimiter {
    @Value("${rate_limit_check_speech_trial}")
    private int RATE_LIMIT_CHECK_SPEECH_MAX_TRIAL;

    @Value("${rate_limit_check_speech_open}")
    private int RATE_LIMIT_CHECK_SPEECH_MAX_OPEN;

    @Value("${rate_limit_check_speech}")
    private int RATE_LIMIT_CHECK_SPEECH_MAX;

    @Value("${rate_limit_speech_to_text_trial}")
    private int RATE_LIMIT_SPEECH_TO_TEXT_MAX_TRIAL;

    @Value("${rate_limit_speech_to_text_open}")
    private int RATE_LIMIT_SPEECH_TO_TEXT_MAX_OPEN;

    @Value("${rate_limit_speech_to_text}")
    private int RATE_LIMIT_SPEECH_TO_TEXT_MAX;

    @Value("${rate_limit_tts_trial}")
    private int RATE_LIMIT_TEXT_TO_SPEECH_MAX_TRIAL;

    @Value("${rate_limit_tts_open}")
    private int RATE_LIMIT_TEXT_TO_SPEECH_MAX_OPEN;

    @Value("${rate_limit_tts}")
    private int RATE_LIMIT_TEXT_TO_SPEECH_MAX;

    @Autowired
    private StringRedisTemplate stringTemplate;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private SlackWarningSystemService slackWarningSystemService;

    private static final int NO_OF_QUICK_SERVICE_THREADS = 20;

    /**
     * this statement create a thread pool of twenty threads
     */
    private final ScheduledExecutorService quickService = Executors.newScheduledThreadPool(NO_OF_QUICK_SERVICE_THREADS); // Creates a thread pool that reuses fixed number of threads(as specified by noOfThreads in this case).


    public boolean isAllowed(String key, int requestPerTime, int blockTime, TimeUnit timeUnit) {
        ValueOperations<String, String> operations = stringTemplate.opsForValue();
        String requests = operations.get(key);
        if (StringUtils.isNotBlank(requests) && Integer.parseInt(requests) >= requestPerTime) {
            return false;
        }
        List<Object> txResults = stringTemplate.execute(new SessionCallback<>() {
            @Override
            public <K, V> List<Object> execute(RedisOperations<K, V> operations) throws DataAccessException {
                final StringRedisTemplate redisTemplate = (StringRedisTemplate) operations;
                final ValueOperations<String, String> valueOperations = redisTemplate.opsForValue();
                operations.multi();
                valueOperations.increment(key);
                redisTemplate.expire(key, blockTime, timeUnit);
                // This will contain the results of all operations in the transaction
                return operations.exec();
            }
        });
        log.info("Current request count: {}", txResults.get(0));
        return true;
    }

    public boolean isAllowedButNotIncreaseValue(String key, int requestPerTime, int blockTime, TimeUnit timeUnit) {
        ValueOperations<String, String> operations = stringTemplate.opsForValue();
        String requests = operations.get(key);
        if (StringUtils.isNotBlank(requests) && Integer.parseInt(requests) >= requestPerTime) {
            return false;
        }

        List<Object> txResults = stringTemplate.execute(new SessionCallback<>() {
            @Override
            public <K, V> List<Object> execute(RedisOperations<K, V> operations) throws DataAccessException {
                final StringRedisTemplate redisTemplate = (StringRedisTemplate) operations;
                operations.multi();
                redisTemplate.expire(key, blockTime, timeUnit);
                // This will contain the results of all operations in the transaction
                return operations.exec();
            }
        });
        log.info("Current request count: {}", txResults.get(0));
        return true;
    }

    protected boolean checkRateLimitIP(String ipAddress, UserLimitCheckType checkType) {
        LocalDate localDate = LocalDate.now();
        Date now = Date.from(localDate.atStartOfDay(ZoneId.of("Asia/Ho_Chi_Minh")).toInstant());
        int numberOfChecks = getTodayNumberOfChecksByDevice(ipAddress, now, checkType);
        boolean isAllowed;
        int rateLimit = 0;
        String checkTypeDescription = " lần check trong 1 ngày";
        switch (checkType) {
            case SPEECH:
                isAllowed = numberOfChecks < RATE_LIMIT_CHECK_SPEECH_MAX_OPEN;
                rateLimit = RATE_LIMIT_CHECK_SPEECH_MAX_OPEN;
                checkTypeDescription = UserLimitCheckType.SPEECH.getDescription();
                break;
            case INTERNAL_SPEECH_TO_TEXT:
                isAllowed = numberOfChecks < RATE_LIMIT_SPEECH_TO_TEXT_MAX_OPEN;
                rateLimit = RATE_LIMIT_SPEECH_TO_TEXT_MAX_OPEN;
                checkTypeDescription = UserLimitCheckType.INTERNAL_SPEECH_TO_TEXT.getDescription();
                break;
            case INTERNAL_TEXT_TO_SPEECH:
                isAllowed = numberOfChecks < RATE_LIMIT_TEXT_TO_SPEECH_MAX_OPEN;
                rateLimit = RATE_LIMIT_TEXT_TO_SPEECH_MAX_OPEN;
                checkTypeDescription = UserLimitCheckType.INTERNAL_TEXT_TO_SPEECH.getDescription();
                break;
            default:
                isAllowed = false;
                break;
        }

        if (isAllowed) {
            saveUserLimitCheckSpeech(ipAddress, now, numberOfChecks + 1, checkType);
        } else {
            String msg = "IP " + ipAddress + " (đang dùng thử app) đã vượt hạn mức " + rateLimit + checkTypeDescription;
            slackWarningSystemService.sendWarningSystemToSlack(msg, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
        }

        return isAllowed;
    }

    private int getTodayNumberOfChecksByDevice(String ipAddress, Date now, UserLimitCheckType checkType) {
        int numberOfChecks = 0;
        RMapCache<String, Integer> limitCheckSpeechMapCache = redissonClient.getMapCache(UserLimitCheckType.ASR_EN == checkType ? CodeDefine.REDIS_LIMIT_CHECK_ASR : CodeDefine.REDIS_LIMIT_CHECK_SPEECH);
        String key = "ip_" + ipAddress + "_date_" + now + "_type_" + checkType.getType();
        if (limitCheckSpeechMapCache.containsKey(key)) {
            numberOfChecks = limitCheckSpeechMapCache.get(key);
        } else {
            limitCheckSpeechMapCache.putAsync(key, numberOfChecks, CodeDefine.TTL_KEY_LIMIT_CHECK_SPEECH, TimeUnit.DAYS);
        }

        return numberOfChecks;
    }

    protected boolean checkRateLimit(String userId, boolean isUserPro, UserLimitCheckType checkType) {
        LocalDate localDate = LocalDate.now();
        Date now = Date.from(localDate.atStartOfDay(ZoneId.of("Asia/Ho_Chi_Minh")).toInstant());
        int rateLimit;
        String checkTypeDescription;
        if (!isUserPro) {
            switch (checkType) {
                case SPEECH:
                    rateLimit = RATE_LIMIT_CHECK_SPEECH_MAX_TRIAL;
                    checkTypeDescription = UserLimitCheckType.SPEECH.getDescription();
                    break;
                case INTERNAL_SPEECH_TO_TEXT:
                    rateLimit = RATE_LIMIT_SPEECH_TO_TEXT_MAX_TRIAL;
                    checkTypeDescription = UserLimitCheckType.INTERNAL_SPEECH_TO_TEXT.getDescription();
                    break;
                case INTERNAL_TEXT_TO_SPEECH:
                    rateLimit = RATE_LIMIT_TEXT_TO_SPEECH_MAX_TRIAL;
                    checkTypeDescription = UserLimitCheckType.INTERNAL_TEXT_TO_SPEECH.getDescription();
                    break;
                default:
                    rateLimit = 0;
                    checkTypeDescription = " lần check trong 1 ngày";
                    break;
            }
        } else {
            switch (checkType) {
                case SPEECH:
                    rateLimit = RATE_LIMIT_CHECK_SPEECH_MAX;
                    checkTypeDescription = UserLimitCheckType.SPEECH.getDescription();
                    break;
                case INTERNAL_SPEECH_TO_TEXT:
                    rateLimit = RATE_LIMIT_SPEECH_TO_TEXT_MAX;
                    checkTypeDescription = UserLimitCheckType.INTERNAL_SPEECH_TO_TEXT.getDescription();
                    break;
                case INTERNAL_TEXT_TO_SPEECH:
                    rateLimit = RATE_LIMIT_TEXT_TO_SPEECH_MAX;
                    checkTypeDescription = UserLimitCheckType.INTERNAL_TEXT_TO_SPEECH.getDescription();
                    break;
                default:
                    rateLimit = 0;
                    checkTypeDescription = " lần check trong 1 ngày";
                    break;
            }
        }

        int numberOfChecks = getTodayNumberOfChecksByUser(userId, now, checkType);
        boolean isAllowed = numberOfChecks < rateLimit;
        if (isAllowed) {
            saveUserLimitCheckSpeech(userId, now, numberOfChecks + 1, checkType);
        } else {
            String msg;
            if (!isUserPro) {
                msg = "User_id " + userId + " (đang dùng thử app) đã vượt hạn mức " + rateLimit + checkTypeDescription;
            } else {
                msg = "User_id " + userId + " đã vượt hạn mức " + rateLimit + checkTypeDescription;
            }

            log.error(msg);
        }

        return isAllowed;
    }

    private int getTodayNumberOfChecksByUser(String userId, Date now, UserLimitCheckType checkType) {
        int numberOfChecks = 0;
        RMapCache<String, Integer> limitCheckSpeechMapCache = redissonClient.getMapCache(UserLimitCheckType.ASR_EN == checkType ? CodeDefine.REDIS_LIMIT_CHECK_ASR : CodeDefine.REDIS_LIMIT_CHECK_SPEECH);
        String key = "user_id_" + userId + "_date_" + now + "_type_" + checkType.getType();
        if (limitCheckSpeechMapCache.containsKey(key)) {
            numberOfChecks = limitCheckSpeechMapCache.get(key);
        } else {
            limitCheckSpeechMapCache.putAsync(key, numberOfChecks, CodeDefine.TTL_KEY_LIMIT_CHECK_SPEECH, TimeUnit.DAYS);
        }

        return numberOfChecks;
    }

    private void saveUserLimitCheckSpeech(String userId, Date now, Integer numberOfChecks, UserLimitCheckType checkType) {
        quickService.submit(() -> {
            String key = "user_id_" + userId + "_date_" + now + "_type_" + checkType.getType();
            redissonClient.getMapCache(UserLimitCheckType.ASR_EN == checkType ? CodeDefine.REDIS_LIMIT_CHECK_ASR : CodeDefine.REDIS_LIMIT_CHECK_SPEECH).putAsync(key, numberOfChecks, CodeDefine.TTL_KEY_LIMIT_CHECK_SPEECH, TimeUnit.DAYS);
        });
    }

    protected void renderTooManyRequestResponseData(String userId, boolean isUserPro, UserLimitCheckType checkType) throws JsonProcessingException {
        TooManyRequestException exception = new TooManyRequestException("Bạn đã dùng hết lượt truy cập cho hôm nay rồi. Hãy quay lại vào hôm sau nha :D");
        if (userId == null) {
            exception.addDetail("popup_type", "SIGN_UP");
            exception.addDetail("data", objectMapper.readTree("{\"title\":\"Bạn đã hết lượt " + checkType.getFeatureDescription() + "\",\"description\":\"Bạn đã đạt ngưỡng sử dụng tính năng " + checkType.getFeatureDescription() + " của ngày hôm nay và cần đăng ký tài khoản để tiếp tục sử dụng thêm\"}"));
            throw exception;
        } else {
            if (isUserPro) {
                exception.addDetail("popup_type", "MOTIVATE");
                exception.addDetail("data", objectMapper.readTree("{\"title\":\"Hôm nay bạn đã\\nhọc rất chăm chỉ rồi!\",\"description\":\"Bạn nên học vừa đủ mỗi ngày để\\nđạt hiệu quả cao nhất.\\nHãy tự thưởng cho mình, nghỉ ngơi\\nvà quay lại vào hôm sau nha!\",\"button\":\"Ok, cám ơn bạn!\"}"));
                throw exception;
            } else {
                exception.addDetail("popup_type", "UPGRADE");
                exception.addDetail("data", objectMapper.readTree("{\"title\":\"Bạn đã hết lượt " + checkType.getFeatureDescription() + "\",\"description\":\"Bạn đã đạt ngưỡng sử dụng tính năng " + checkType.getFeatureDescription() + " của ngày hôm nay và cần nâng cấp tài khoản để tiếp tục sử dụng thêm\"}"));
                throw exception;
            }
        }
    }

    protected void saveCountRequestCheck(UserLimitCheckType checkType) {
        quickService.submit(() -> {
            RMapCache<String, Integer> mapTotalCheckSpeech = redissonClient.getMapCache(checkType == UserLimitCheckType.SPEECH ? CodeDefine.REDIS_COUNT_INTERNAL_CHECK_SPEECH : CodeDefine.REDIS_COUNT_INTERNAL_CHECK_ASR);
            String keyByDay = "KEY_TOTAL_CHECK_" + checkType.getType() + "_A_DAY";
            String keyTotal = "KEY_TOTAL_CHECK_" + checkType.getType();
            RLock keyLock = mapTotalCheckSpeech.getLock(keyByDay);
            RLock keyLocks = mapTotalCheckSpeech.getLock(keyTotal);

            try {
                keyLock.lockInterruptibly(3, TimeUnit.SECONDS);
                keyLocks.lockInterruptibly(3, TimeUnit.SECONDS);

                // tổng lần check trong 1 ngày
                Integer valueCounter = ObjectUtils.isEmpty(mapTotalCheckSpeech.get(keyByDay)) ? 1 : mapTotalCheckSpeech.get(keyByDay) + 1;
                if (ObjectUtils.isEmpty(mapTotalCheckSpeech.get(keyByDay))) {
                    mapTotalCheckSpeech.put(keyByDay, valueCounter, CodeDefine.TTL_CHECK_SPEECH_A_DAY, TimeUnit.DAYS);
                } else {
                    mapTotalCheckSpeech.put(keyByDay, valueCounter);
                }

                // tổng tất cả lần check trong 1 năm
                Integer valueCounters = ObjectUtils.isEmpty(mapTotalCheckSpeech.get(keyTotal)) ? 1 : mapTotalCheckSpeech.get(keyTotal) + 1;
                if (ObjectUtils.isEmpty(mapTotalCheckSpeech.get(keyTotal))) {
                    mapTotalCheckSpeech.put(keyTotal, valueCounters, CodeDefine.TTL_CHECK_SPEECH, TimeUnit.DAYS);
                } else {
                    mapTotalCheckSpeech.put(keyTotal, valueCounters);
                }

                log.info("Total check {} current a day is: {}", checkType.getType(), valueCounter);
                log.info("Total check {} current is: {}", checkType.getType(), valueCounters);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            } finally {
                keyLock.unlock();
                keyLocks.unlock();
            }
        });
    }
}
