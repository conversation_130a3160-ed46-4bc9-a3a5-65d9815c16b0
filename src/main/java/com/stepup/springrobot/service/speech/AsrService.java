package com.stepup.springrobot.service.speech;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.config.ConfigUtil;
import com.stepup.springrobot.config.HttpUtils;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.auth.AccountInfoDTO;
import com.stepup.springrobot.dto.recognize.InternalAsrDTO;
import com.stepup.springrobot.exception.business.audio.AudioErrorException;
import com.stepup.springrobot.exception.business.audio.AudioTooShortException;
import com.stepup.springrobot.model.speech.UserLimitCheckType;
import com.stepup.springrobot.security.JwtService;
import com.stepup.springrobot.service.CommonService;
import com.stepup.springrobot.service.SlackWarningSystemService;
import com.stepup.springrobot.service.UploadFileToS3;
import io.sentry.Sentry;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class AsrService extends CommonService {
    @Value("${internal_stt_host_name}")
    private String internalSpeechToTextHostname;

    @Value("${internal_stt_uri}")
    private String internalSpeechToTextUri;

    @Value("${internal_stt_token}")
    private String internalSpeechToTextToken;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private  SlackWarningSystemService slackWarningSystemService;

    @Autowired
    private RedisRateLimiter redisRateLimiter;

    protected AsrService(ObjectMapper objectMapper, UploadFileToS3 uploadFileToS3, JwtService jwtService, SlackWarningSystemService slackWarningSystemService) {
        super(objectMapper, uploadFileToS3, jwtService, slackWarningSystemService);
    }


    private JsonNode getResponseAsrSpeechToText(File file) throws IOException {
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(20, TimeUnit.SECONDS)
                .writeTimeout(20, TimeUnit.SECONDS)
                .readTimeout(20, TimeUnit.SECONDS)
                .build();

        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("file", file.getName(), RequestBody.create(MediaType.parse("application/octet-stream"), file))
                .addFormDataPart("token", internalSpeechToTextToken)
                .addFormDataPart("language", "en")
                .build();

        Request request = new Request.Builder()
                .url(internalSpeechToTextHostname + internalSpeechToTextUri)
                .post(requestBody)
                .build();

        Response response = client.newCall(request).execute();
        return objectMapper.readTree(response.body().string());
    }

    public DataResponseDTO<?> generateSpeechToText(HttpServletRequest request, MultipartFile multipartFile, boolean isOpenApi) throws IOException {
        String ipAddress = HttpUtils.getClientIpAddress(request);
        boolean isAllowed;
        String userId = null;
        AccountInfoDTO dataUserHeader = getAccountInfo(request, ipAddress, isOpenApi);
        if (isOpenApi) {
            isAllowed = redisRateLimiter.checkRateLimitIP(ipAddress, UserLimitCheckType.INTERNAL_SPEECH_TO_TEXT);
        } else {
            userId = dataUserHeader.getUserData().getUserId();
            isAllowed = redisRateLimiter.checkRateLimit(userId, true, UserLimitCheckType.INTERNAL_SPEECH_TO_TEXT);
        }

        if (!isAllowed) {
            redisRateLimiter.renderTooManyRequestResponseData(userId, true, UserLimitCheckType.INTERNAL_SPEECH_TO_TEXT);
        }

        File file = convertMultiPartFileToFile(multipartFile, isOpenApi ? ipAddress : userId, true);
        //tmp
        if (file == null) {
            throw new AudioErrorException();
        }

        JsonNode response = null;
        try {
            response = getResponseAsrSpeechToText(file);
            InternalAsrDTO responseAsr = objectMapper.convertValue(response, new TypeReference<>() {
            });
            if (responseAsr.getSuccess().equals("false")) {
                throw new RuntimeException("Response lỗi: " + responseAsr);
            }

            String text = responseAsr.getResult().getText();
            if (StringUtils.isEmpty(text)) {
                throw new RuntimeException("Text rỗng");
            }

            log.info("==========Lấy text từ internal asr thành công: {}", text);
            return new DataResponseDTO<>(CodeDefine.OK, CodeDefine.SUCCESS_MESSAGE, responseAsr.getResult());
        } catch (Exception e) {
            Sentry.captureException(e);
            slackWarningSystemService.sendWarningSystemToSlack("(AI) Lỗi nhận diện audio của Internal ASR: " + e.getMessage() + ", user_id: " + userId + ", ip: " + ipAddress + ", response: " + response, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
            throw new AudioTooShortException();
        } finally {
            file.delete();
        }
    }
}
