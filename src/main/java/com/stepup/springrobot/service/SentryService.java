package com.stepup.springrobot.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.RobotMetricsDTO;
import com.stepup.springrobot.dto.sentry.SentryDiscoveryEventDTO;
import com.stepup.springrobot.dto.sentry.SentryEventDetailDTO;
import com.stepup.springrobot.dto.sentry.SentryResponse;
import com.stepup.springrobot.model.chat.RobotUserConversation;
import com.stepup.springrobot.repository.chat.RobotUserConversationRepository;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Log4j2
@Service
public class SentryService {
    @Autowired
    private ObjectMapper objectMapper;

    @Value("${sentry.environment}")
    private String sentryEnvironment;

    @Value("${sentry_token}")
    private String sentryToken;

    private final String sentryHost = "https://sentry.io/";

    @Autowired
    private RobotUserConversationRepository robotUserConversationRepository;


    public DataResponseDTO<?> getMetrics(HttpServletRequest request, String cursor) throws IOException {
        // Get one page of events using the cursor
        SentryResponse sentryResponse = getSentryLogByQuery("\"ROBOT - res time\"", cursor);
        log.info("==================SentryResponse: {}", objectMapper.writeValueAsString(sentryResponse));
        List<SentryDiscoveryEventDTO> events = objectMapper.convertValue(sentryResponse.getBody(),
                new TypeReference<>() {
                });

        // Get next cursor from response headers
        String nextCursor = null;
        String[] links = objectMapper.convertValue(sentryResponse.getHeaders().get("link"), new TypeReference<>() {
        });
        if (links != null && links.length > 0) {
            nextCursor = extractNextCursor(links[0]);
        }

        // Convert events to metrics
        List<RobotMetricsDTO> metrics = events.stream()
                .map(this::parseRobotMetrics)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        
        Set<Long> conversationIdsSet = metrics.stream().map(RobotMetricsDTO::getConversationId).collect(Collectors.toSet());
        List<RobotUserConversation> conversations = robotUserConversationRepository.findByIdIn(conversationIdsSet);
        Map<Long, String> conversationsMap = conversations.stream().collect(Collectors.toMap(RobotUserConversation::getId, RobotUserConversation::getExternalConversationId));

        for (RobotMetricsDTO metric : metrics) {
            metric.setLlmConversationId(conversationsMap.getOrDefault(metric.getConversationId(), null));
        }

        Map<String, Object> response = new HashMap<>();
        response.put("metrics", metrics);
        response.put("nextCursor", nextCursor);

        return new DataResponseDTO<>(CodeDefine.OK, "Lấy danh sách metrics thành công", response);
    }

    private RobotMetricsDTO parseRobotMetrics(SentryDiscoveryEventDTO event) {
        try {
            RobotMetricsDTO metrics = new RobotMetricsDTO();

            // Set the timestamp from the event
            metrics.setTimestamp(event.getTimestamp());

            // Remove the "ROBOT - " prefix
            String cleanMessage = event.getTitle().replace("ROBOT - ", "").trim();

            // Split by comma and process each part
            String[] parts = cleanMessage.split(",");
            for (String part : parts) {
                part = part.trim();
                String[] keyValue = part.split(":");
                if (keyValue.length != 2)
                    continue;

                String key = keyValue[0].trim();
                String value = keyValue[1].trim();

                switch (key) {
                    case "res time":
                        metrics.setResTime(parseMilliseconds(value));
                        break;
                    case "stalling":
                        metrics.setStalling(parseMilliseconds(value));
                        break;
                    case "bot LLM 1":
                        metrics.setBotLlmTime(parseMilliseconds(value));
                        break;
                    case "tts":
                        metrics.setTtsTime(parseMilliseconds(value));
                        break;
                    case "animations":
                        metrics.setAnimationsTime(parseMilliseconds(value));
                        break;
                    case "upload audio":
                        metrics.setUploadAudio(parseMilliseconds(value));
                        break;
                    case "conversation_id":
                        metrics.setConversationId(Long.parseLong(value));
                        break;

                }
            }

            if (metrics.getConversationId() == null) {
                return null;
            }

            return metrics;
        } catch (Exception e) {
            log.error("Error parsing robot metrics: {}", event, e);
            return null;
        }
    }

    private Long parseMilliseconds(String value) {
        return Long.parseLong(value.replace("ms", ""));
    }

    public List<SentryDiscoveryEventDTO> getAllSentryDiscoveryLogByKeyword(String query, boolean isGetDetail) {
        SentryResponse sentryResponse = getSentryLogByQuery(query, null);
        List<SentryDiscoveryEventDTO> firstPageEvents = objectMapper.convertValue(sentryResponse.getBody(),
                new TypeReference<>() {
                });
        List<SentryDiscoveryEventDTO> events = new ArrayList<>(firstPageEvents);
        String[] links = objectMapper.convertValue(sentryResponse.getHeaders().get("link"), new TypeReference<>() {
        });
        log.info("==================cursor link: {}", links[0] + "\n size: " + events.size());
        String nextCursor = extractNextCursor(links[0]);
        log.info("==================next cursor: {}", nextCursor);
        while (!StringUtils.isEmpty(nextCursor)) {
            try {
                sentryResponse = getSentryLogByQuery(query, nextCursor);
                List<SentryDiscoveryEventDTO> nextPageEvents = objectMapper.convertValue(sentryResponse.getBody(),
                        new TypeReference<>() {
                        });
                events.addAll(nextPageEvents);
                links = objectMapper.convertValue(sentryResponse.getHeaders().get("link"), new TypeReference<>() {
                });
                log.info("==================cursor link: {}", links[0] + "\n size: " + events.size());
                nextCursor = extractNextCursor(links[0]);
                log.info("==================next cursor: {}", nextCursor);
            } catch (Exception e) {
                break;
            }
        }

        return events;
    }

    public SentryResponse getSentryLogByQuery(String query, String cursor) {
        try {
            WebClient client3 = WebClient
                    .builder()
                    .baseUrl(sentryHost)
                    .build();

            String sendRequestSentryDiscoveryLogs = "api/0/organizations/step-up-english/events/";
            ResponseEntity<DataResponseDTO> dataDTOMono = client3.get()
                    .uri(sendRequestSentryDiscoveryLogs, uriBuilder -> uriBuilder
                            .queryParam("project", "4508458876862464")
                            .queryParam("statsPeriod", "48h")
                            .queryParam("field", "title")
                            .queryParam("field", "project")
                            .queryParam("field", "timestamp")
                            .queryParam("query", query)
                            .queryParam("cursor", StringUtils.isEmpty(cursor) ? "" : cursor)
                            .queryParam("environment", sentryEnvironment)
                            .queryParam("sort", "-timestamp")
                            .build())
                    .header("Authorization", "Bearer " + sentryToken)
                    .retrieve()
                    .toEntity(DataResponseDTO.class)
                    .block();

            return SentryResponse.builder()
                    .headers(dataDTOMono.getHeaders())
                    .body(dataDTOMono.getBody().getData())
                    .build();
        } catch (Exception e) {
            log.error("Error when call api get sentry log {}", e.getMessage());
            return null;
        }
    }

    public static String extractNextCursor(String linkHeader) {
        AtomicReference<String> resultStr = new AtomicReference<>("");
        if (linkHeader != null) {
            Arrays.stream(linkHeader.split(",")).forEach(link -> {
                Matcher matcher = Pattern
                        .compile("<([^>]+)>; rel=\"([^\"]+)\"; results=\"([^\"]+)\"; cursor=\"([^\"]+)\"")
                        .matcher(link.trim());
                if (matcher.find()) {
                    String url = matcher.group(1);
                    String rel = matcher.group(2);
                    String result = matcher.group(3);
                    String cursor = matcher.group(4);
                    if (rel.equals("next") && result.equals("true")) {
                        resultStr.set(cursor);
                    }
                }
            });
        }

        return resultStr.get(); // No suitable next link found
    }

    public SentryEventDetailDTO getEventDetail(String eventId) {
        try {
            WebClient client3 = WebClient
                    .builder()
                    .baseUrl(sentryHost)
                    .build();

            String sendRequestSentryEventDetail = "api/0/projects/step-up-english/su-tofu-backend/events/";
            Mono<SentryEventDetailDTO> dataDTOMono = client3.get()
                    .uri(sendRequestSentryEventDetail + eventId + "/")
                    .header("Authorization", "Bearer " + sentryToken)
                    .retrieve()
                    .bodyToMono(SentryEventDetailDTO.class);

            SentryEventDetailDTO sentryEventDetailDTO = objectMapper.convertValue(dataDTOMono.block(),
                    new TypeReference<>() {
                    });
            log.info("Retrieve event detail by id: " + sentryEventDetailDTO.getId());
            return sentryEventDetailDTO;
        } catch (Exception e) {
            log.error("Error when call service addon {}", e.getMessage());
            return null;
        }
    }
}
