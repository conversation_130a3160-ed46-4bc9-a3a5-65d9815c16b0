package com.stepup.springrobot.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.config.ConfigUtil;
import com.stepup.springrobot.dto.recognize.InternalAsrDTO;
import com.stepup.springrobot.dto.recognize.InternalTTSReqDTO;
import com.stepup.springrobot.dto.recognize.InternalTTSResDTO;
import com.stepup.springrobot.model.InternalTextToSpeechVoice;
import io.sentry.Sentry;
import lombok.extern.log4j.Log4j2;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.net.SocketTimeoutException;
import java.util.concurrent.TimeUnit;

@Log4j2
@Service
public class RecognizeService {
    @Value("${internal_tts_host_name}")
    private String internalTTSHostName;

    @Value("${internal_tts_uri}")
    private String internalTTSUri;

    @Value("${internal_tts_token}")
    private String internalTTSToken;

    @Value("${internal_stt_host_name}")
    private String internalSTTHostName;

    @Value("${internal_stt_uri}")
    private String internalSTTUri;

    @Value("${internal_stt_token}")
    private String internalSTTToken;

    @Value("${default_tts_volume}")
    private Integer ttsVolume;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private SlackWarningSystemService slackWarningSystemService;

    protected String convertTextToSpeech (String text, InternalTextToSpeechVoice voiceName, String source, String format, Double speed, Integer volume) {
        String ttsResponse = null;
        try {
            ttsResponse = generateTTSAudioUrlByInternalTTS(text, voiceName, source, format, speed, volume);
            InternalTTSResDTO internalTTSResDTO = objectMapper.readValue(ttsResponse, new TypeReference<>() {
            });
            log.info("============Internal TTS response: " + objectMapper.writeValueAsString(internalTTSResDTO));
            return internalTTSResDTO.getData().getAudioUrl();
        } catch (Exception e) {
            log.error("Lỗi khi sinh audio từ text qua internal tts: " + e.getMessage());
        }

        return null;
    }

    private String generateTTSAudioUrlByInternalTTS(String text, InternalTextToSpeechVoice voiceName, String source, String format, Double speed, Integer volume) throws IOException {
        if (speed == null) {
            speed = 1.0;
        }

        if (volume == null) {
            volume = ttsVolume;
        }

        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(20, TimeUnit.SECONDS)
                .writeTimeout(20, TimeUnit.SECONDS)
                .readTimeout(20, TimeUnit.SECONDS)
                .build();

        InternalTTSReqDTO internalTTSReqDTO = InternalTTSReqDTO.builder()
                .voice(voiceName)
                .format(format)
                .text(text)
                .source(source)
                .speed(speed)
                .token(internalTTSToken)
                .vol(volume)
                .build();

        RequestBody body = RequestBody.create(
                okhttp3.MediaType.parse("application/json"), objectMapper.writeValueAsBytes(internalTTSReqDTO));

        Request request = new Request.Builder()
                .url(internalTTSHostName + internalTTSUri)
                .post(body)
                .build();

        Response response = client.newCall(request).execute();
        return response.body().string();
    }

    private JsonNode getResponseAsrSpeechToText(File file) throws IOException {
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(20, TimeUnit.SECONDS)
                .writeTimeout(20, TimeUnit.SECONDS)
                .readTimeout(20, TimeUnit.SECONDS)
                .build();

        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("file", file.getName(), RequestBody.create(okhttp3.MediaType.parse("application/octet-stream"), file))
                .addFormDataPart("token",  internalSTTToken)
                .addFormDataPart("language", "en")
                .build();

        Request request = new Request.Builder()
                .url(internalSTTHostName + internalSTTUri)
                .post(requestBody)
                .build();

        Response response = client.newCall(request).execute();
        return objectMapper.readTree(response.body().string());
    }

    public String convertSpeechToText(File file, Long userId, String deviceId) throws Exception {
        JsonNode response = null;
        try {
            response = getResponseAsrSpeechToText(file);
            InternalAsrDTO responseAsr = objectMapper.convertValue(response, new TypeReference<>() {
            });
            if (responseAsr.getSuccess().equals("false")) {
                throw new RuntimeException("Response lỗi: " + responseAsr);
            }

            String text = responseAsr.getResult().getText();
            if (StringUtils.isEmpty(text)) {
                throw new RuntimeException("Text rỗng");
            }

            log.info("==========Lấy text từ internal asr thành công: {}", text);
            return text;
        } catch (SocketTimeoutException e) {
            Sentry.captureException(e);
            slackWarningSystemService.sendWarningSystemToSlack("Lỗi nhận diện audio của Internal ASR, user_id: " + userId + ", user_id: " + deviceId, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
            throw e;
        } catch (Exception e) {
            Sentry.captureException(e);
            slackWarningSystemService.sendWarningSystemToSlack("Lỗi nhận diện audio của Internal ASR: " + e.getMessage() + ", user_id: " + userId + ", user_id: " + deviceId + ", response: " + response, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
            throw new RuntimeException("Lỗi khi lấy text từ internal asr: " + e.getMessage());
        }
    }
}
