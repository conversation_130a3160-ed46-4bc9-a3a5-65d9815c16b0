package com.stepup.springrobot.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.config.ConfigUtil;
import com.stepup.springrobot.dto.AlertSystemMonitorDTO;
import io.sentry.Sentry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

@Slf4j
@Service
public class SlackWarningSystemService {
    @Async
    public void sendWarningSystemToSlack(String message, String pathToken) {
        try {
            AlertSystemMonitorDTO alertSystemMonitorDTO = new AlertSystemMonitorDTO();
            alertSystemMonitorDTO.setText("(Robot) Thông tin: " + message);

            WebClient client3 = WebClient
                    .builder()
                    .baseUrl(ConfigUtil.INSTANCE.getHOSTNAME_WP_ALERT_SYSTEM())
                    .build();

            client3.post().uri(pathToken)
                    .contentType(MediaType.APPLICATION_JSON)
                    .bodyValue(new ObjectMapper().writeValueAsString(alertSystemMonitorDTO))
                    .retrieve().toBodilessEntity().block();

            log.info("Sent alert warning to wp successfully: {}", message);
        } catch (Exception e) {
            log.error("Sent alert warning to wp error: {}", e.getMessage());
            Sentry.captureException(e);
        }
    }
}
