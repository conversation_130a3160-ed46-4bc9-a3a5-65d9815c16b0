package com.stepup.springrobot.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.security.JwtService;
import com.theokanning.openai.completion.chat.ChatCompletionRequest;
import com.theokanning.openai.completion.chat.ChatCompletionResult;
import com.theokanning.openai.completion.chat.ChatMessage;
import com.theokanning.openai.service.OpenAiService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;

@Log4j2
@Service
public class OpenAIService extends CommonService {
    public final String[] onionGptRemoveWords = {"[part 1]", "[part 2]", "(pause)", "(listen)", "(listening)", "(part 1)", "(part 2)", "(Question 1)", "(Question 2)", "(post 1)", "(post 2)", "(post 3)", "(post 4)", "Part 1: ", "Part 2: ", "part 1:", "part 2:"};

    // Default model and token settings
    private static final int DEFAULT_MAX_TOKENS = 2048;

    @Autowired
    protected OpenAIService(ObjectMapper objectMapper, UploadFileToS3 uploadFileToS3, JwtService jwtService,
                            SlackWarningSystemService slackWarningSystemService) {
        super(objectMapper, uploadFileToS3, jwtService, slackWarningSystemService);
    }

    /**
     * Sends a request to OpenAI API and returns the chat completion result
     *
     * @param token OpenAI API key
     * @param chatMessages List of chat messages to send to OpenAI
     * @return ChatCompletionResult containing the API response
     */
    protected ChatCompletionResult getOpenAIResponse(String token, String model, List<ChatMessage> chatMessages) {
        return callOpenAI(token, chatMessages, model, DEFAULT_MAX_TOKENS);
    }

    /**
     * Sends a request to OpenAI API with custom model and max tokens
     *
     * @param token OpenAI API token
     * @param chatMessages List of chat messages to send to OpenAI
     * @return ChatCompletionResult containing the API response
     */
    protected ChatCompletionResult callOpenAI(String token, List<ChatMessage> chatMessages, String modelName, int maxTokens) {
        OpenAiService openAiService = new OpenAiService(token, Duration.ofSeconds(60));

        ChatCompletionRequest request = ChatCompletionRequest.builder()
                .model(modelName)
                .messages(chatMessages)
                .temperature(0.0)
                .maxTokens(maxTokens)
                .n(1)
                .frequencyPenalty(0.0)
                .topP(1.0)
                .presencePenalty(0.0)
                .build();

        ChatCompletionResult chatCompletionResult = openAiService.createChatCompletion(request);
        log.info("Gửi request tới OpenAI thành công, model {}, request: {}, response: {}", modelName, request, chatCompletionResult);
        return chatCompletionResult;
    }

    /**
     * Helper method to extract the response content from ChatCompletionResult
     *
     * @param result The ChatCompletionResult from OpenAI API
     * @return The text content of the response
     */
    protected String extractResponseContent(ChatCompletionResult result) {
        if (result != null && result.getChoices() != null && !result.getChoices().isEmpty()) {
            return result.getChoices().get(0).getMessage().getContent()
                    .replace("```json", "").replace("```", "");
        }
        return "";
    }

    /**
     * Removes unwanted words from the OpenAI response
     *
     * @param response The original response text
     * @return Cleaned response text
     */
    protected String cleanResponse(String response) {
        String cleanedResponse = response;
        for (String word : onionGptRemoveWords) {
            cleanedResponse = cleanedResponse.replace(word, "");
        }
        return cleanedResponse.trim();
    }


    public String getOpenAIChatResponse(String token, String model, List<ChatMessage> messages) {
        ChatCompletionResult result = getOpenAIResponse(token, model, messages);
        return extractResponseContent(result);
    }
}