package com.stepup.springrobot.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.SearchPageLimit;
import com.stepup.springrobot.dto.addon.*;
import com.stepup.springrobot.dto.auth.AccountInfoDTO;
import com.stepup.springrobot.exception.business.content.ContentNotFoundException;
import com.stepup.springrobot.exception.business.request.InvalidFormatException;
import com.stepup.springrobot.model.addon.Song;
import com.stepup.springrobot.model.addon.Story;
import com.stepup.springrobot.model.mqtt.MqttMessageType;
import com.stepup.springrobot.model.robot.RobotUser;
import com.stepup.springrobot.repository.addon.SongRepository;
import com.stepup.springrobot.repository.addon.StoryRepository;
import com.stepup.springrobot.repository.robot.RobotUserRepository;
import com.stepup.springrobot.security.JwtService;
import com.stepup.springrobot.service.communication.ConnectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AddonService extends CommonService {

    @Autowired
    private SongRepository songRepository;

    @Autowired
    private StoryRepository storyRepository;

    @Autowired
    private RobotUserRepository robotUserRepository;

    @Autowired
    private ConnectService connectService;

    protected AddonService(ObjectMapper objectMapper, UploadFileToS3 uploadFileToS3, JwtService jwtService,
            SlackWarningSystemService slackWarningSystemService) {
        super(objectMapper, uploadFileToS3, jwtService, slackWarningSystemService);
    }

    public DataResponseDTO<?> getSongs(HttpServletRequest request, int page) throws IOException {
        AccountInfoDTO accountInfoDTO = getAccountInfo(request, null, false);
        String userId = accountInfoDTO.getUserData().getUserId();

        SearchPageLimit pageLimit = new SearchPageLimit(page, 10);
        Page<Song> songs = songRepository.findAllSongs(pageLimit.getPageRequest());
        SongListResDTO songListResDTO = SongListResDTO.builder()
                .songs(songs.getContent().stream()
                        .map(song -> SongDTO.builder()
                                .songId(song.getId())
                                .thumbnail(song.getThumbnail())
                                .title(song.getTitle())
                                .artist(song.getArtist())
                                .build())
                        .collect(Collectors.toList()))
                .currentPage(page)
                .totalPage(songs.getTotalPages())
                .build();

        return new DataResponseDTO<>(CodeDefine.OK, "Lấy danh sách bài hát thành công", songListResDTO);
    }

    public DataResponseDTO<?> getStories(HttpServletRequest request, int page) throws IOException {
        AccountInfoDTO accountInfoDTO = getAccountInfo(request, null, false);
        String userId = accountInfoDTO.getUserData().getUserId();

        SearchPageLimit pageLimit = new SearchPageLimit(page, 10);
        Page<Story> stories = storyRepository.findAllStories(pageLimit.getPageRequest());
        StoryListResDTO songListResDTO = StoryListResDTO.builder()
                .stories(stories.getContent().stream()
                        .map(story -> StoryDTO.builder()
                                .storyId(story.getId())
                                .thumbnail(story.getThumbnail())
                                .title(story.getTitle())
                                .author(story.getAuthor())
                                .description(story.getDescription())
                                .build())
                        .collect(Collectors.toList()))
                .currentPage(page)
                .totalPage(stories.getTotalPages())
                .build();

        return new DataResponseDTO<>(CodeDefine.OK, "Lấy danh sách truyện thành công", songListResDTO);
    }

    public DataResponseDTO<?> playMusic(HttpServletRequest request, SongPlayReqDTO reqDTO) throws IOException {
        AccountInfoDTO accountInfoDTO = getAccountInfo(request, null, false);
        String userId = accountInfoDTO.getUserData().getUserId();

        Long songId = reqDTO.getSongId();
        Song song = songRepository.findById(songId).orElseThrow(() -> new ContentNotFoundException("song ", songId.toString()));

        RobotUser robotUser = robotUserRepository.findFirstByUserIdOrderByIdDesc(userId);
        if (robotUser == null) {
            throw new InvalidFormatException("Tài khoản của bạn chưa được tích hợp với thiết bị robot");
        }

        connectService.handleRequestPlayMusicOrStory(userId, robotUser.getRobotId(), song.getUrl(), song.getThumbnail(), MqttMessageType.MUSIC);
        return new DataResponseDTO<>(CodeDefine.OK, "Request phát nhạc thành công");
    }

    public DataResponseDTO<?> playStory(HttpServletRequest request, StoryPlayReqDTO reqDTO) throws IOException {
        AccountInfoDTO accountInfoDTO = getAccountInfo(request, null, false);
        String userId = accountInfoDTO.getUserData().getUserId();

        Long storyId = reqDTO.getStoryId();
        Story story = storyRepository.findById(storyId).orElseThrow(() -> new ContentNotFoundException("story ", storyId.toString()));

        RobotUser robotUser = robotUserRepository.findFirstByUserIdOrderByIdDesc(userId);
        if (robotUser == null) {
            throw new InvalidFormatException("Tài khoản của bạn chưa được tích hợp với thiết bị robot");
        }

        connectService.handleRequestPlayMusicOrStory(userId, robotUser.getRobotId(), story.getUrl(), story.getThumbnail(), MqttMessageType.STORY);
        return new DataResponseDTO<>(CodeDefine.OK, "Request phát truyện thành công");
    }
}
