package com.stepup.springrobot.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.auth.AccountInfoDTO;
import com.stepup.springrobot.dto.onboarding.OnboardAnswerReqDTO;
import com.stepup.springrobot.dto.onboarding.OnboardingAnswerDTO;
import com.stepup.springrobot.dto.onboarding.OnboardingQuestionV2DTO;
import com.stepup.springrobot.exception.business.user.AnonymousAccessException;
import com.stepup.springrobot.exception.security.UnauthorizedException;
import com.stepup.springrobot.mapper.OnBoardingQuestionMapper;
import com.stepup.springrobot.model.onboarding.OnboardingAnswer;
import com.stepup.springrobot.model.onboarding.OnboardingQuestion;
import com.stepup.springrobot.model.user.Profile;
import com.stepup.springrobot.repository.auth.ProfileRepository;
import com.stepup.springrobot.repository.entrance_test.EntranceTestProfileRepository;
import com.stepup.springrobot.repository.onboarding.OnboardingAnswerRepository;
import com.stepup.springrobot.repository.onboarding.OnboardingQuestionRepository;
import com.stepup.springrobot.repository.personal.DeviceIdUserRepository;
import com.stepup.springrobot.security.JwtService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class OnboardService extends CommonService {
    @Autowired
    private OnboardingQuestionRepository onboardingQuestionRepository;

    @Autowired
    private OnBoardingQuestionMapper onBoardingQuestionMapper;

    @Autowired
    private ProfileRepository profileRepository;

    @Autowired
    private OnboardingAnswerRepository onboardingAnswerRepository;

    @Autowired
    private DeviceIdUserRepository deviceIdUserRepository;

    @Autowired
    private EntranceTestProfileRepository entranceTestProfileRepository;

    @Autowired
    private SharedService sharedService;

    protected OnboardService(ObjectMapper objectMapper, UploadFileToS3 uploadFileToS3, JwtService jwtService, SlackWarningSystemService slackWarningSystemService) {
        super(objectMapper, uploadFileToS3, jwtService, slackWarningSystemService);
    }

    public DataResponseDTO<?> getOnboardQuestion() {
        try {
            List<OnboardingQuestionV2DTO> onboardingQuestionV2DTOS;
            List<OnboardingQuestion> onboardingQuestion = onboardingQuestionRepository.findAll();
            onboardingQuestionV2DTOS = onBoardingQuestionMapper.toOnboardingQuestionV2DTOs(onboardingQuestion);
            return new DataResponseDTO<>(CodeDefine.OK, "Lấy dữ liệu màn onboard thành công", onboardingQuestionV2DTOS);
        } catch (Exception e) {
            return new DataResponseDTO<>(CodeDefine.SERVER_ERROR, "L��i khi lấy dữ liệu màn onboard", null);
        }
    }

    @Transactional
    public DataResponseDTO<?> saveOnBoardingAnswer(HttpServletRequest request, OnboardAnswerReqDTO onboardingQuestion, boolean isOpenApi) throws IOException {
        handleSaveOnboardingAnswer(request, onboardingQuestion, isOpenApi, false);
        return new DataResponseDTO<>(CodeDefine.OK, "Lưu kết quả onboarding thành công");
    }

    @Transactional
    public DataResponseDTO<?> updateOnBoardingAnswer(HttpServletRequest request, OnboardAnswerReqDTO onboardingQuestion, boolean isOpenApi) throws IOException {
        handleSaveOnboardingAnswer(request, onboardingQuestion, isOpenApi, true);
        return new DataResponseDTO<>(CodeDefine.OK, "Update kết quả onboarding thành công");
    }

    private void handleSaveOnboardingAnswer(HttpServletRequest request, OnboardAnswerReqDTO onboardAnswerReqDTO, boolean isOpenApi, boolean isUpdateAnswer) throws IOException {
        AccountInfoDTO accountInfoDTO = getAccountInfo(request, onboardAnswerReqDTO.getDeviceId(), isOpenApi);
        String userId = isOpenApi ? null : accountInfoDTO.getUserData().getUserId();
        String deviceId = accountInfoDTO.getDeviceId();
        if (isOpenApi && !deviceIdUserRepository.existsByDeviceId(deviceId)) {
            throw new AnonymousAccessException();
        }

        Profile currentProfile = isOpenApi
                ? profileRepository.getCurrentProfileByDeviceId(deviceId)
                : profileRepository.getCurrentProfileByUserId(userId);
        if (currentProfile != null && deviceId != null)
            throw new UnauthorizedException(); // Chưa đăng nhập chỉ được onboard 1 lần

        List<String> questionTypes = onboardingQuestionRepository.getQuestionTypes()
                .stream()
                .filter(type -> !"START".equals(type))
                .collect(Collectors.toList());

        Map<String, JsonNode> answerMap = onboardAnswerReqDTO.getObAnswers().stream()
                .collect(Collectors.toMap(OnboardingAnswerDTO::getType, OnboardingAnswerDTO::getAnswer));

        for (String key : questionTypes) {
            if (!answerMap.containsKey(key)) {
                throw new IllegalArgumentException("Missing required key in onboarding answers: " + key);
            }
        }

        String username = answerMap.get("USERNAME").asText();
        Integer age = Integer.valueOf(answerMap.get("AGE").asText());
        String gender = answerMap.get("GENDER").asText();

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode additionalData = objectMapper.valueToTree(answerMap);

        OnboardingAnswer onboardingAnswer = OnboardingAnswer.builder()
                .name(username)
                .age(age)
                .gender(gender)
                .additionalData(additionalData)
                .build();

        if (currentProfile != null && isUpdateAnswer) {    // 1 profile có thể có nhiều kết quả onboard
            onboardingAnswer.setProfile(currentProfile);
            onboardingAnswerRepository.save(onboardingAnswer);
        } else if (currentProfile == null) { // Onboard lần đầu
            currentProfile = Profile.builder()
                    .userId(userId)
                    .deviceId(deviceId)
                    .isCurrent(true)
                    .purchases(new ArrayList<>())
                    .name(username)
                    .build();
            onboardingAnswer.setProfile(currentProfile);
            currentProfile.setOnboardingAnswers(List.of(onboardingAnswer));
            currentProfile = profileRepository.save(currentProfile);
            sharedService.saveNewUserProfileHistory(userId, currentProfile.getId());
        } else if (entranceTestProfileRepository.existsByProfileIdAndIsComplete(currentProfile.getId(), true)) { // Onboard cho profile thứ 2 trở đi khi tài khoản đầu đã hoàn thành test đầu vào
            currentProfile = Profile.builder()
                    .userId(userId)
                    .deviceId(null)
                    .isCurrent(true)
                    .purchases(new ArrayList<>())
                    .name(username)
                    .build();
            onboardingAnswer.setProfile(currentProfile);
            currentProfile.setOnboardingAnswers(List.of(onboardingAnswer));
            currentProfile = profileRepository.save(currentProfile);
            if (userId != null) {
                sharedService.deselectNonCurrentProfile(userId, currentProfile.getId());
            }
        }
    }
}
