package com.stepup.springrobot.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.auth.AccountInfoDTO;
import com.stepup.springrobot.dto.study.*;
import com.stepup.springrobot.exception.business.content.ContentNotFoundException;
import com.stepup.springrobot.exception.business.request.InvalidFormatException;
import com.stepup.springrobot.model.chat.AICharacter;
import com.stepup.springrobot.model.chat.RobotUserConversation;
import com.stepup.springrobot.model.chat.RobotUserConversationRecordHistory;
import com.stepup.springrobot.model.robot.RobotUser;
import com.stepup.springrobot.model.study.StudyLesson;
import com.stepup.springrobot.model.study.StudyLessonStatus;
import com.stepup.springrobot.model.study.StudyTopic;
import com.stepup.springrobot.model.study.StudyUnit;
import com.stepup.springrobot.repository.chat.RobotUserConversationRecordHistoryRepository;
import com.stepup.springrobot.repository.chat.RobotUserConversationRepository;
import com.stepup.springrobot.repository.robot.RobotUserRepository;
import com.stepup.springrobot.repository.study.StudyLessonRepository;
import com.stepup.springrobot.repository.study.StudyTopicRepository;
import com.stepup.springrobot.repository.study.StudyUnitRepository;
import com.stepup.springrobot.security.JwtService;
import com.stepup.springrobot.service.communication.ConnectService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

@Log4j2
@Service
public class StudyService extends CommonService{

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private StudyUnitRepository studyUnitRepository;

    @Autowired
    private StudyTopicRepository studyTopicRepository;

    @Autowired
    private StudyLessonRepository studyLessonRepository;

    @Autowired
    private RobotUserRepository robotUserRepository;

    @Autowired
    private RobotUserConversationRepository robotUserConversationRepository;

    @Autowired
    private RobotUserConversationRecordHistoryRepository robotUserConversationRecordHistoryRepository;

    @Autowired
    private ConnectService connectService;

    protected StudyService(ObjectMapper objectMapper, UploadFileToS3 uploadFileToS3, JwtService jwtService, SlackWarningSystemService slackWarningSystemService) {
        super(objectMapper, uploadFileToS3, jwtService, slackWarningSystemService);
    }

    public DataResponseDTO<?> getStudyPlan(HttpServletRequest request) throws IOException {
        List<StudyUnit> studyUnits = studyUnitRepository.getAllStudyUnits();
        List<StudyUnitDTO> studyUnitDTOS = studyUnits.stream()
                .map(studyUnit -> StudyUnitDTO.builder()
                .name(studyUnit.getName())
                .id(studyUnit.getId())
                .build())
                .collect(Collectors.toList());

        List<StudyTopic> studyTopics = studyTopicRepository.getAllTopics();

        studyUnitDTOS.forEach(studyUnitDTO -> {
            List<StudyTopic> topics = studyTopics.stream()
                    .filter(studyTopic -> studyTopic.getUnitId().equals(studyUnitDTO.getId()))
                    .collect(Collectors.toList());
            studyUnitDTO.setTopics(topics.stream()
                    .map(studyTopic -> StudyTopicDTO.builder()
                            .id(studyTopic.getId())
                            .name(studyTopic.getName())
                            .description(studyTopic.getDuration() + " phút")
                            .thumbnail(studyTopic.getThumbnail())
                            .progress(0)
                            .build())
                    .collect(Collectors.toList()));
        });

        studyUnitDTOS.get(0).getTopics().get(0).setProgress(20);

        StudyPlanResDTO studyPlanResDTO = StudyPlanResDTO.builder()
                .units(studyUnitDTOS)
                .build();

        return new DataResponseDTO<>(CodeDefine.OK, "Lấy dữ liệu lộ trình học thành công",studyPlanResDTO);
    }

    public DataResponseDTO<?> getLessonsByTopicId(HttpServletRequest request, Long topicId) {
        StudyTopic studyTopic = studyTopicRepository.findById(topicId).orElse(null);
        if (studyTopic == null) {
            throw new ContentNotFoundException("Chủ đề học topic_id", topicId);
        }

        List<StudyLesson> studyLessons = studyLessonRepository.getLessonsByTopicId(topicId);
        List<StudyLessonDTO> studyLessonDTOS = studyLessons.stream()
                .map(studyLesson -> StudyLessonDTO.builder()
                        .id(studyLesson.getId())
                        .name(studyLesson.getName())
                        .tag(studyLesson.getType().getDescription())
                        .icon(studyLesson.getIcon())
                        .status(StudyLessonStatus.COMPLETED)
                        .build())
                .collect(Collectors.toList());

        return new DataResponseDTO<>(CodeDefine.OK, "Lấy dữ danh sách bài học theo tình huống thành công", StudyLessonResDTO.builder()
                .lessons(studyLessonDTOS)
                .build());
    }

    public DataResponseDTO<StudyLessonDetailDTO> getLessonResult(HttpServletRequest request, Long lessonId) throws IOException {
        StudyLesson studyLesson = studyLessonRepository.findById(lessonId).orElseThrow(() -> new ContentNotFoundException("Bài học lesson_id", lessonId));
        StudyTopic studyTopic = studyTopicRepository.findById(studyLesson.getTopicId()).orElseThrow(() -> new ContentNotFoundException("Chủ đề học topic_id", studyLesson.getTopicId()));
        StudyUnit studyUnit = studyUnitRepository.findById(studyTopic.getUnitId()).orElseThrow(() -> new ContentNotFoundException("Unit unit_id", studyTopic.getUnitId()));
        StudyLessonDetailDTO studyLessonDetailDTO = StudyLessonDetailDTO.builder()
                .header(StudyLessonDetailHeaderDTO.builder()
                        .title(studyUnit.getName())
                        .subtitle(studyLesson.getName())
                        .build())
                .lessonContent(StudyLessonDetailContentDTO.builder()
                        .title(studyLesson.getTitle())
                        .thumbnail(studyLesson.getThumbnail())
                        .description(studyLesson.getDescription())
                        .build())
                .progress(StudyLessonDetailProgressDTO.builder()
                        .percentage(10)
                        .title("Quá trình")
                        .build())
                .evaluation(List.of("Độ trôi chảy: Con có khả năng nói được các câu đơn", "Từ vựng: Con đã bắt đầu nắm được các từ vựng theo chủ đề"))
                .botId(studyLesson.getBotId())
                .build();

        return new DataResponseDTO<>(CodeDefine.OK, "Lấy kết quả bài học thành công", studyLessonDetailDTO);
    }

    public DataResponseDTO<?> getLessonConversation(HttpServletRequest request, Long lessonId) throws IOException {
        AccountInfoDTO userDataDTO = getAccountInfo(request, null, false);
        String userId = userDataDTO.getUserData().getUserId();
        StudyLesson studyLesson = studyLessonRepository.findById(lessonId).orElseThrow(() -> new ContentNotFoundException("Bài học lesson_id", lessonId));
        RobotUser robotUser = robotUserRepository.findFirstByUserIdOrderByIdDesc(userId);
        if (robotUser != null) {
            Long botId = studyLesson.getBotId();
            RobotUserConversation robotUserConversation = robotUserConversationRepository.findFirstByRobotIdAndBotIdOrderByIdDesc(robotUser.getRobotId(), botId);
            if (robotUserConversation != null) {
                List<RobotUserConversationRecordHistory> histories = robotUserConversationRecordHistoryRepository.findByRobotUserConversationId(robotUserConversation.getId(), List.of(AICharacter.USER.getCharacter(), AICharacter.BOT_RESPONSE_CONVERSATION.getCharacter()));
                List<ConversationMessageDTO> messageDTOS = histories.stream().map(history -> ConversationMessageDTO.builder()
                        .text(history.getContent())
                        .audio(history.getAudio())
                        .role(history.getCharacter() == AICharacter.USER ? "USER" : "ROBOT")
                        .build()).collect(Collectors.toList());
                ConversationResDTO conversationResDTO = ConversationResDTO.builder()
                        .botName("Pika Bot")
                        .botAvatar("https://smedia.stepup.edu.vn/robot/robot_avatar.jpg")
                        .caption("Một ngày thật tuyệt vời!")
                        .messages(messageDTOS)
                        .build();
                return new DataResponseDTO<>(CodeDefine.OK, "Lấy kết quả bài học thành công", conversationResDTO);
            }
        }

        String data = "{\"bot_avatar\":\"https://smedia.stepup.edu.vn/robot/robot_avatar.jpg\",\"bot_name\":\"Pika Bot\",\"caption\":\"Một ngày thật tuyệt vời!\",\"messages\":[{\"role\":\"ROBOT\",\"audio\":\"https://tts-file-mgc-52.hacknao.edu.vn/data/tts_female_linh_v1/c2a066d751ae82ee3831127a7c84cbcb_3_1.0.mp3\",\"text\":\"Chào cậu! Tớ là Pika. Cậu đã sẵn sàng chưa? Cùng tớ khám phá ngay hành trình hôm nay cậu nhé!. Sau giờ học cậu thường thích làm gì?\"},{\"role\":\"USER\",\"audio\":\"https://sustorage.stepup.edu.vn/audio/robot/user/241231/7ae5c2fc-0894-4bc7-1f93-0ea45b37af3f_1735618247234.wav\",\"text\":\"I like to write a book.\"}]}";
        return new DataResponseDTO<>(CodeDefine.OK, "Lấy kết quả bài học thành công", objectMapper.readTree(data));
    }

    public DataResponseDTO<?> assignLesson(HttpServletRequest request, AssignLessonReqDTO reqDTO) throws IOException {
        AccountInfoDTO userDataDTO = getAccountInfo(request, null, false);
        String userId = userDataDTO.getUserData().getUserId();
        RobotUser robotUser = robotUserRepository.findFirstByUserIdOrderByIdDesc(userId);
        if (robotUser == null) {
            throw new InvalidFormatException("Tài khoản của bạn chưa được tích hợp với thiết bị robot");
        }

        Long lessonId = reqDTO.getLessonId();
        StudyLesson studyLesson = studyLessonRepository.findById(lessonId)
                .orElseThrow(() -> new ContentNotFoundException("lesson", lessonId));
        connectService.handleRequestAssignLesson(userId, studyLesson.getBotId(), robotUser.getRobotId());

        return new DataResponseDTO<>(CodeDefine.OK, "Giao bài học thành công");
    }
}
