package com.stepup.springrobot.service;

import com.stepup.springrobot.config.firebase.FCMService;
import com.stepup.springrobot.dto.notification.PushNotificationReqDTO;
import com.stepup.springrobot.dto.notification.PushNotificationRequest;
import com.stepup.springrobot.model.notification.UserDeviceToken;
import com.stepup.springrobot.repository.notification.DeviceTokenRepository;
import io.sentry.Sentry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.stream.Collectors;

@Slf4j
@Service
public class NotificationService {
    @Autowired
    private FCMService fcmService;

    @Autowired
    private DeviceTokenRepository deviceTokenRepository;

    private final ScheduledExecutorService quickService = Executors.newScheduledThreadPool(20); // Creates a thread pool that reuses fixed number of threads(as specified by noOfThreads in this case).

    @Async
    public void sendPushNotificationToToken(PushNotificationRequest request) {
        try {
            fcmService.sendMessageToToken(request);
        } catch (InterruptedException | ExecutionException e) {
            // token không còn được sử dụng
            if ("com.google.firebase.messaging.FirebaseMessagingException: Requested entity was not found.".equals(e.getMessage())) {
                deviceTokenRepository.deleteByDeviceToken(request.getToken());
                log.error("===========Token was deleted because not use============" + request.getToken());
            } else if ("com.google.firebase.messaging.FirebaseMessagingException: The registration token is not a valid FCM registration token".equals(e.getMessage()) || "com.google.firebase.messaging.FirebaseMessagingException: SenderId mismatch".equals(e.getMessage())) {
                // token không hợp lệ

                deviceTokenRepository.deleteByDeviceToken(request.getToken());
                log.error("===========Token was deleted because not a valid FCM============" + request.getToken());
            } else {
                log.error(e.getMessage());
            }
        }
    }

    public void sendNotificationToUserId(PushNotificationReqDTO pushNotificationReqDTO) {
        try {
            String userId = pushNotificationReqDTO.getUserId();
            List<UserDeviceToken> allUserDeviceTokens = deviceTokenRepository.findByUserId(userId);
            List<String> listDeviceTokens = CollectionUtils.isEmpty(allUserDeviceTokens)
                    ? new ArrayList<>()
                    : allUserDeviceTokens.stream()
                    .map(UserDeviceToken::getDeviceToken)
                    .collect(Collectors.toList());

            PushNotificationRequest request = PushNotificationRequest.builder()
                    .title(pushNotificationReqDTO.getTitle())
                    .message(pushNotificationReqDTO.getMessage())
                    .postUrl(pushNotificationReqDTO.getPostUrl())
                    .bannerUrl(pushNotificationReqDTO.getBannerUrl())
                    .versionAppUpdate(pushNotificationReqDTO.getVersionAppUpdate())
                    .build();

            quickService.submit(() -> listDeviceTokens.forEach(deviceToken -> {
                request.setToken(deviceToken);
                sendPushNotificationToToken(request);
            }));

        } catch (Exception ex) {
            Sentry.captureException(ex);
            log.error("Error when send notification", ex);
        }
    }
}
