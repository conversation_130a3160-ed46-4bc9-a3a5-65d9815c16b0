package com.stepup.springrobot.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.common.ValidationUtils;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.auth.AccountInfoDTO;
import com.stepup.springrobot.dto.personal.*;
import com.stepup.springrobot.exception.business.content.ContentNotFoundException;
import com.stepup.springrobot.exception.business.request.PhoneDeletePermissionDenyException;
import com.stepup.springrobot.model.communication.CommunicationRequest;
import com.stepup.springrobot.model.communication.CommunicationRequestStatusType;
import com.stepup.springrobot.model.entrance_test.EntranceTestProfile;
import com.stepup.springrobot.model.mqtt.MqttMessageType;
import com.stepup.springrobot.model.personal.DeviceIdUser;
import com.stepup.springrobot.model.robot.RobotConfig;
import com.stepup.springrobot.model.robot.RobotFirmwareVersion;
import com.stepup.springrobot.model.robot.RobotUser;
import com.stepup.springrobot.model.user.Profile;
import com.stepup.springrobot.repository.auth.ProfileRepository;
import com.stepup.springrobot.repository.auth.TestPhonesRepository;
import com.stepup.springrobot.repository.auth.UserRepository;
import com.stepup.springrobot.repository.communication.CommunicationRequestRepository;
import com.stepup.springrobot.repository.entrance_test.EntranceTestProfileRepository;
import com.stepup.springrobot.repository.game.RobotFirmwareVersionRepository;
import com.stepup.springrobot.repository.personal.DeviceIdUserRepository;
import com.stepup.springrobot.repository.robot.RobotConfigRepository;
import com.stepup.springrobot.repository.robot.RobotUserRepository;
import com.stepup.springrobot.security.JwtService;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Log4j2
@Service
public class PersonalService extends CommonService {
    @Autowired
    private ProfileRepository profileRepository;

    @Autowired
    private DeviceIdUserRepository deviceIdUserRepository;

    @Autowired
    private EntranceTestProfileRepository entranceTestProfileRepository;

    @Autowired
    private RobotUserRepository robotUserRepository;

    @Autowired
    private RobotFirmwareVersionRepository robotFirmwareVersionRepository;

    @Autowired
    private CommunicationRequestRepository communicationRequestRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private TestPhonesRepository testPhonesRepository;

    @Autowired
    private RobotConfigRepository robotConfigRepository;

    @Autowired
    private SharedService sharedService;

    @Autowired
    private ObjectMapper objectMapper;

    protected PersonalService(ObjectMapper objectMapper, UploadFileToS3 uploadFileToS3, JwtService jwtService, SlackWarningSystemService slackWarningSystemService) {
        super(objectMapper, uploadFileToS3, jwtService, slackWarningSystemService);
    }

    @Transactional
    public DataResponseDTO<PersonalInfoDTO> getPersonalInfo(HttpServletRequest request, String deviceId, String appV, String platform, boolean isOpenApi) throws IOException {
        AccountInfoDTO accountInfoDTO = getAccountInfo(request, deviceId, isOpenApi);
        String userId = isOpenApi ? null : accountInfoDTO.getUserData().getUserId();
        deviceId = accountInfoDTO.getDeviceId();

        DeviceIdUser deviceIdUser = isOpenApi
                ? deviceIdUserRepository.findByDeviceId(deviceId)
                : deviceIdUserRepository.findByUserId(userId);
        if (deviceIdUser == null) {
            deviceIdUser = deviceIdUserRepository.save(DeviceIdUser.builder()
                    .deviceId(deviceId)
                    .userId(userId)
                    .version(appV)
                    .os(platform)
                    .build());
        } else {
            deviceIdUser.setVersion(appV);
            deviceIdUser.setOs(platform);
        }

        deviceIdUserRepository.save(deviceIdUser);

        List<Profile> profiles = isOpenApi
                ? profileRepository.findByDeviceId(deviceId)
                : profileRepository.findByUserId(userId);
        if (CollectionUtils.isEmpty(profiles)) {
            return new DataResponseDTO<>(CodeDefine.OK, "Lấy thông tin tài khoản thành công", PersonalInfoDTO.builder()
                    .isCompleteOnboard(false)
                    .phone(isOpenApi ? null : accountInfoDTO.getUserData().getPhone())
                    .build());
        }

        Profile currentProfile = profiles.stream().filter(Profile::getIsCurrent).findFirst().orElse(null);
        boolean isCurrentProfileCompleteEntranceTest = currentProfile != null && entranceTestProfileRepository.existsByProfileIdAndIsComplete(currentProfile.getId(), true);
        if (currentProfile == null) {
            currentProfile = profiles.get(0);
            currentProfile.setIsCurrent(true);
            profileRepository.save(currentProfile);
        } else if (profiles.size() > 1 && !isCurrentProfileCompleteEntranceTest) {
            // Nếu currentProfile chưa hoàn thành test đầu vào thì chuyển về profile khác đã hoàn thành
            currentProfile.setIsCurrent(false);
            profileRepository.save(currentProfile);

            String currentProfileId = currentProfile.getId();
            currentProfile = profiles.stream()
                    .filter(profile -> !profile.getId().equals(currentProfileId) && entranceTestProfileRepository.existsByProfileIdAndIsComplete(profile.getId(), true))
                    .findFirst()
                    .orElse(null);
            sharedService.setCurrentAndDeselectNonCurrentProfile(userId, Objects.requireNonNull(currentProfile).getId());
            isCurrentProfileCompleteEntranceTest = true;
        }

        PersonalInfoDTO personalInfoDTO = PersonalInfoDTO.builder()
                .userId(userId)
                .phone(isOpenApi ? null : accountInfoDTO.getUserData().getPhone())
                .isCompleteOnboard(true)
                .currentProfile(PersonalProfileDTO.builder()
                        .id(currentProfile.getId())
                        .username(currentProfile.getName())
                        .avatar(currentProfile.getAvatar())
                        .build())
                .isCompleteEntranceTest(isCurrentProfileCompleteEntranceTest)
                .build();

        return new DataResponseDTO<>(CodeDefine.OK, "Lấy thông tin tài khoản thành công", personalInfoDTO);
    }

    public DataResponseDTO<UserProfilesResDTO> getUserProfiles(HttpServletRequest request) throws IOException {
        AccountInfoDTO userDataDTO = getAccountInfo(request, null, false);
        String userId = userDataDTO.getUserData().getUserId();

        List<Profile> profiles = profileRepository.findByUserId(userId);
        if (CollectionUtils.isEmpty(profiles)) {
            return new DataResponseDTO<>(CodeDefine.OK, "Lấy danh sách profile thành công", UserProfilesResDTO.builder()
                    .profiles(new ArrayList<>())
                    .build());
        }

        Set<String> profileIdHasEntranceTest = entranceTestProfileRepository.getListCompletedTestByProfileIds(profiles.stream().map(Profile::getId).collect(Collectors.toList()))
                .stream().map(EntranceTestProfile::getProfileId)
                .collect(Collectors.toSet());

        List<ProfileDetailDTO> profileDetailDTOS = profiles.stream()
                .map(profile -> ProfileDetailDTO.builder()
                        .id(profile.getId())
                        .username(profile.getName())
                        .avatar(profile.getAvatar())
                        .isCurrent(profile.getIsCurrent())
                        .isCompleteOnboard(true)
                        .isCompleteEntranceTest(profileIdHasEntranceTest.contains(profile.getId()))
                        .build())
                .collect(Collectors.toList());

        return new DataResponseDTO<>(CodeDefine.OK, "Lấy danh sách profile thành công",  UserProfilesResDTO.builder()
                .profiles(profileDetailDTOS)
                .build());
    }

    public DataResponseDTO<ProfileDetailDTO> updateCurrentProfile(HttpServletRequest request, SwitchProfileReqDTO reqDTO) throws IOException {
        AccountInfoDTO userDataDTO = getAccountInfo(request, null, false);
        String userId = userDataDTO.getUserData().getUserId();

        List<Profile> profiles = profileRepository.findByUserId(userId);
        if (CollectionUtils.isEmpty(profiles)) {
            return new DataResponseDTO<>(CodeDefine.OK, "Tài khoản chưa khởi tạo profile");
        }

        Profile newCurrentProfile = profiles.stream()
                .filter(profile -> profile.getId().equals(reqDTO.getProfileId()))
                .findFirst()
                .orElse(null);
        if (newCurrentProfile == null) {
            throw new ContentNotFoundException("Profile", reqDTO.getProfileId());
        }

        if (!Objects.equals(newCurrentProfile.getIsCurrent(), Boolean.TRUE)) {
            sharedService.setCurrentAndDeselectNonCurrentProfile(userId, newCurrentProfile.getId());
        }

        return new DataResponseDTO<>(CodeDefine.OK, "Đổi profile thành công", ProfileDetailDTO.builder()
                .id(newCurrentProfile.getId())
                .avatar(newCurrentProfile.getAvatar())
                .username(newCurrentProfile.getName())
                .isCurrent(true)
                .isCompleteOnboard(true)
                .isCompleteEntranceTest(entranceTestProfileRepository.existsByProfileIdAndIsComplete(newCurrentProfile.getId(), true))
                .build());
    }

    public DataResponseDTO<?> getStatisticData(HttpServletRequest request) throws IOException {
        String data = "{\"status\":200,\"message\":\"Lấy dữ liệu cá nhân thành công\",\"data\":{\"daily_progress\":{\"title\":\"Tình hình học tập của <yellow>Lâm Minh</yellow> ngày hôm nay:\",\"message\":\"Thật là tuyệt vời, nhóc nhà bạn đã học được:\",\"time_markers\":[{\"time\":10,\"progress\":1},{\"time\":20,\"progress\":1},{\"time\":30,\"progress\":1},{\"time\":40,\"progress\":0.5},{\"time\":50,\"progress\":0},{\"time\":60,\"progress\":0}]},\"weekly_progress\":{\"header\":\"Tiến độ học tập\",\"title\":\"Tổng quan quá trình học\",\"date_range\":\"Apr 10 - Apr 17\",\"daily_data\":[{\"day\":\"T2\",\"value\":30,\"is_current\":false},{\"day\":\"T3\",\"value\":40,\"is_current\":false},{\"day\":\"T4\",\"value\":35,\"is_current\":false},{\"day\":\"T5\",\"value\":50,\"is_current\":false},{\"day\":\"T6\",\"value\":45,\"is_current\":true},{\"day\":\"S\",\"value\":0,\"is_current\":false},{\"day\":\"S\",\"value\":0,\"is_current\":false}],\"feedback\":{\"message\":\"Còn 10p nữa để hoàn thành mục tiêu ❤\uFE0F\",\"description\":\"Bố mẹ hãy khích lệ bé luyện tập thêm!\"}},\"skills_overview\":{\"header\":\"Sơ đồ kĩ năng\",\"title\":\"Tổng kết kiến thức\",\"metrics\":[{\"icon\":\"https://smedia.stepup.edu.vn/thecoach/misc/chunk1.png\",\"description\":\"Số từ vựng con đã học\",\"value\":0},{\"icon\":\"https://smedia.stepup.edu.vn/thecoach/misc/chunk1.png\",\"description\":\"Số cấu trúc con đã học\",\"value\":0},{\"icon\":\"https://smedia.stepup.edu.vn/thecoach/misc/chunk1.png\",\"description\":\"Trung bình điểm phát âm\",\"value\":0}]}}}";
        return new DataResponseDTO<>(CodeDefine.OK, "Lấy dữ liệu cá nhân thành công", objectMapper.readTree(data));
    }

    public DataResponseDTO<?> getListRobot(HttpServletRequest request) throws IOException {
        AccountInfoDTO userDataDTO = getAccountInfo(request, null, false);
        String userId = userDataDTO.getUserData().getUserId();
        List<RobotUser> robotUsers = robotUserRepository.findByUserIdOrderByIdAsc("abc");
        RobotFirmwareVersion latestVersion = robotFirmwareVersionRepository.getLatestFirmware();

        List<RobotDetailDTO> robots = robotUsers.stream()
                .map(robotUser -> {
                    RobotConfig robotConfig = getRobotConfig(robotUser.getRobotId());
                    RobotDetailDTO robotDetailDTO = RobotDetailDTO.builder()
                            .id(robotUser.getRobotId())
                            .name(robotUser.getName())
                            .currentVersion(robotConfig.getFirmwareVersion())
                            .latestVersion(latestVersion.getVersion())
                            .build();
                    if (!robotConfig.getFirmwareVersion().equals(latestVersion.getVersion())) {
                        CommunicationRequest communicationRequest = communicationRequestRepository.findFirstByRobotIdAndUserIdAndTypeOrderByIdDesc(robotUser.getRobotId(), userId, MqttMessageType.UPDATE_FIRMWARE);
                        if (communicationRequest != null && communicationRequest.getStatus() == CommunicationRequestStatusType.PENDING) {
                            // compare communicationRequest.getCreatedAt() with current time in minutes
                            long diffMinutes = (System.currentTimeMillis() - communicationRequest.getCreatedAt().getTime())/ 60000;
                            if (diffMinutes <= 15) {
                                robotDetailDTO.setUpdating(true);
                            } else {
                                communicationRequest.setStatus(CommunicationRequestStatusType.FAIL);
                                communicationRequestRepository.save(communicationRequest);
                            }
                        }
                    }

                    return robotDetailDTO;
                })
                .collect(Collectors.toList());

        return new DataResponseDTO<>(CodeDefine.OK, "Lấy danh sách robot thành công", RobotsResDTO.builder()
                .robots(robots)
                .build());
    }

    public DataResponseDTO<?> getPersonalSettingConfig(HttpServletRequest request) throws IOException {
        AccountInfoDTO userDataDTO = getAccountInfo(request, null, false);
        String phone = userDataDTO.getUserData().getPhone();

        PersonalSettingDTO personalSettingDTO = PersonalSettingDTO.builder()
                .isEnableDeleteAccount(testPhonesRepository.existsByPhone(phone))
                .build();

        return new DataResponseDTO<>(CodeDefine.OK, "Lấy thông tin thiết lập tài khoản thành công", personalSettingDTO);
    }

    private void convertToRandomPhone(String phone) {
        // Random a String of 6 numbers
        String randomPhone = "0111" + RandomStringUtils.randomNumeric(6);
        while (userRepository.findByPhone(randomPhone) != null) {
            randomPhone = "0111" + RandomStringUtils.randomNumeric(6);
        }

        userRepository.updatePhoneById(randomPhone, phone);
    }

    public DataResponseDTO<?> deleteTestAccount(HttpServletRequest request, String phone, boolean isOpenApi) throws IOException {
        if (!isOpenApi) {
            AccountInfoDTO userDataDTO = getAccountInfo(request, null, false);
            phone = userDataDTO.getUserData().getPhone();
        } else {
            phone = ValidationUtils.validateAndNormalizePhone(phone);
        }

        if (testPhonesRepository.existsByPhone(phone)) {
            convertToRandomPhone(phone);
            return new DataResponseDTO<>(CodeDefine.OK, "Xoá tài khoản thành công!");
        } else {
            throw new PhoneDeletePermissionDenyException();
        }
    }

    private RobotConfig getRobotConfig(String robotId) {
        RobotConfig robotConfig = robotConfigRepository.findByRobotId(robotId);
        if (robotConfig == null) {
            RobotFirmwareVersion latestVersion = robotFirmwareVersionRepository.getLatestFirmware();
            robotConfig = robotConfigRepository.save(RobotConfig.builder()
                    .robotId(robotId)
                    .firmwareVersion(latestVersion.getVersion())
                    .build());
        }

        return robotConfig;
    }
}
