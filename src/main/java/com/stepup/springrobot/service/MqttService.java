package com.stepup.springrobot.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.config.MqttConfig;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.mqtt.MqttMessageDTO;
import com.stepup.springrobot.dto.mqtt.PublishMqttMessageReqDTO;
import com.stepup.springrobot.dto.mqtt.SubscribeMqttTopicReqDTO;
import com.stepup.springrobot.dto.mqtt.UnSubscribeMqttTopicReqDTO;
import com.stepup.springrobot.exception.business.content.ContentNotFoundException;
import com.stepup.springrobot.model.mqtt.MqttACL;
import com.stepup.springrobot.model.mqtt.MqttMessageType;
import com.stepup.springrobot.model.mqtt.MqttUser;
import com.stepup.springrobot.repository.mqtt.MqttACLRepository;
import com.stepup.springrobot.repository.mqtt.MqttUserRepository;
import lombok.extern.log4j.Log4j2;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

@Log4j2
@Service
public class MqttService {
    @Autowired
    @Qualifier("redisTemplateDB2")
    private StringRedisTemplate redisTemplate;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private MqttUserRepository mqttUserRepository;

    @Autowired
    private MqttACLRepository mqttACLRepository;

    @Autowired
    private MqttConfig mqttConfig;

    @Autowired
    private ObjectMapper objectMapper;

    public void sendMessage(PublishMqttMessageReqDTO reqDTO) throws JsonProcessingException {
        mqttConfig.sendMessage(objectMapper.writeValueAsString(reqDTO.getMessage()), reqDTO.getTopic());
    }

    // Subscribe to a dynamic topic
    public void subscribeToTopic(SubscribeMqttTopicReqDTO reqDTO) {
       mqttConfig.addNewSubscription(reqDTO.getTopic());
    }

    // Unsubscribe from a topic
    public void unsubscribeFromTopic(UnSubscribeMqttTopicReqDTO reqDTO) {
        String topic = reqDTO.getTopic(); // Dynamically retrieve the topic from the request DTO
        mqttConfig.removeSubscription(topic);
    }

    public DataResponseDTO<?> saveMqttAclToRedis(String username) {
        MqttUser mqttUser = mqttUserRepository.findByUsername(username);
        if (mqttUser == null) {
            throw new ContentNotFoundException("Mqtt user", username);
        }

        redissonClient.getMapCache("mqtt_acl").put(username, mqttUser.getPassword());

        // Key check authentication
        String authKey = username+":auth";
        String superUserKey = username + ":su";
        String writeACLsKey = username + ":wacls";
        String readACLsKey = username + ":racls";
        String subscribeACLsKey = username + ":sacls";
        String readWriteACLsKey = username + ":rwacls";

        redisTemplate.delete(List.of(authKey, superUserKey, writeACLsKey, readACLsKey, subscribeACLsKey, readWriteACLsKey));

        // Store hashed password under username key
        redisTemplate.opsForValue().set(authKey, mqttUser.getPassword());

        // If user is superuser, set the superuser key
        redisTemplate.opsForValue().set(superUserKey, mqttUser.getIsAdmin() + "");

        List<MqttACL> mqttACLS = mqttACLRepository.findByMqttUserId(mqttUser.getId());
        for (MqttACL mqttACL : mqttACLS) {
            if (mqttACL.getRole().equals(2L)) {
                redisTemplate.opsForSet().add(writeACLsKey, mqttACL.getTopic());
            } else if (mqttACL.getRole().equals(4L)) {
                redisTemplate.opsForSet().add(subscribeACLsKey, mqttACL.getTopic());
                redisTemplate.opsForSet().add(readACLsKey, mqttACL.getTopic());
            } else if (mqttACL.getRole().equals(999L)) {
                redisTemplate.opsForSet().add(readWriteACLsKey, mqttACL.getTopic());
            }
        }

        return new DataResponseDTO<>(CodeDefine.OK, "Lưu mqtt acl thành công");
    }
}
