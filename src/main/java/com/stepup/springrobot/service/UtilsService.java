package com.stepup.springrobot.service;

import com.github.kokorin.jaffree.ffmpeg.FFmpeg;
import com.github.kokorin.jaffree.ffmpeg.FFmpegResult;
import com.github.kokorin.jaffree.ffmpeg.UrlInput;
import com.github.kokorin.jaffree.ffmpeg.UrlOutput;
import io.sentry.Sentry;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.io.FilenameUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Paths;
import java.util.Objects;

@Log4j2
public class UtilsService {
    private static MultipartFile getNewFile(String fileName, MultipartFile currentFile) {
        return new MultipartFile() {
            @Override
            public String getName() {
                return currentFile.getName();
            }

            @Override
            public String getOriginalFilename() {
                return fileName;
            }

            @Override
            public String getContentType() {
                return currentFile.getContentType();
            }

            @Override
            public boolean isEmpty() {
                return currentFile.isEmpty();
            }

            @Override
            public long getSize() {
                return currentFile.getSize();
            }

            @Override
            public byte[] getBytes() throws IOException {
                return currentFile.getBytes();
            }

            @Override
            public InputStream getInputStream() throws IOException {
                return currentFile.getInputStream();
            }

            @Override
            public void transferTo(File file) throws IllegalStateException {
            }
        };
    }

    public static File convertMultiPartFileToFile(MultipartFile multipartFile, Long userId) {
    String newFileName = System.currentTimeMillis() + "_" + userId + "." + FilenameUtils.getExtension(multipartFile.getOriginalFilename());
    multipartFile = getNewFile(newFileName, multipartFile);
    final File file = new File(Objects.requireNonNull(multipartFile.getOriginalFilename()));
    try (final FileOutputStream outputStream = new FileOutputStream(file)) {
        outputStream.write(multipartFile.getBytes());
    } catch (final IOException ex) {
        log.error("Error converting the multi-part file to file= {}", ex.getMessage());
        Sentry.captureException(ex);
    }

    return file;
}

    public static String getWavFilePath(String inputFilePath) {
        String outputFilePath = inputFilePath.replace(".mp3",".wav");
        // Create an FFmpeg instance
        FFmpeg ffmpeg = FFmpeg.atPath()
                .addInput(UrlInput.fromPath(Paths.get(inputFilePath)))
                .addOutput(UrlOutput.toPath(Paths.get(outputFilePath)))
                .setOverwriteOutput(true)
                .setProgressListener(progress -> {
                    System.out.println("Progress: " + progress);
                });

        // Execute the command
        FFmpegResult result = ffmpeg.execute();
        // Check the result
        if (result != null) {
            log.info("Conversion successful!");
        } else {
            log.info("Conversion failed!");
        }

        return outputFilePath;
    }
}
