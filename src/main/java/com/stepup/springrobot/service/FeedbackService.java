package com.stepup.springrobot.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.dto.feedback.Base64FileDTO;
import com.stepup.springrobot.dto.feedback.FeedbackDTO;
import com.stepup.springrobot.dto.feedback.UserFeedbackDTO;
import com.stepup.springrobot.dto.feedback.UserFeedbackReqDTO;
import com.stepup.springrobot.model.feedback.UserFeedback;
import com.stepup.springrobot.repository.feedback.FeedbackRepository;
import com.stepup.springrobot.repository.feedback.UserFeedbackRepository;
import com.stepup.springrobot.security.JwtService;
import com.stepup.springrobot.util.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.server.ResponseStatusException;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FeedbackService extends CommonService {
    private final FeedbackRepository feedbackRepository;
    private final UserFeedbackRepository userFeedbackRepository;

    @Value("${s3.aws.bucket}")
    private String s3Bucket;

    @Autowired
    public FeedbackService(
            ObjectMapper objectMapper, UploadFileToS3 uploadFileToS3, JwtService jwtService, SlackWarningSystemService slackWarningSystemService,
            FeedbackRepository feedbackRepository,
            UserFeedbackRepository userFeedbackRepository) {
        super(objectMapper, uploadFileToS3, jwtService, slackWarningSystemService);
        this.feedbackRepository = feedbackRepository;
        this.userFeedbackRepository = userFeedbackRepository;
    }

    public List<FeedbackDTO> getAllFeedbacks() {
        return feedbackRepository.findAll().stream()
                .map(feedback -> FeedbackDTO.builder()
                        .id(feedback.getId())
                        .title(feedback.getTitle())
                        .type(feedback.getType())
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * Save user feedback with optional file uploads
     *
     * @param request User feedback request DTO
     * @return UserFeedbackDTO with saved data
     */
    public UserFeedbackDTO saveUserFeedback(UserFeedbackReqDTO request) {
        // Handle multiple file uploads
        List<String> imageUrls = new ArrayList<>();

        // Process Base64 encoded files if present in the request
        if (request.getFiles() != null && !request.getFiles().isEmpty()) {
            for (Base64FileDTO fileDTO : request.getFiles()) {
                try {
                    String imageUrl = processBase64File(fileDTO, "web-mvp/feedback");
                    if (imageUrl != null) {
                        imageUrls.add(imageUrl);
                    }
                } catch (Exception e) {
                    log.error("Error processing Base64 file: {}", fileDTO.getFileName(), e);
                    throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR,
                            "Failed to process file " + fileDTO.getFileName() + ": " + e.getMessage());
                }
            }
        }

        // Create and save user feedback
        UserFeedback userFeedback = UserFeedback.builder()
                .description(request.getDescription())
                .images(objectMapper.valueToTree(imageUrls))
                .feedbackId(request.getFeedbackId())
                .accountId(request.getAccountId())
                .rating(request.getRating())
                .build();

        UserFeedback saved = userFeedbackRepository.save(userFeedback);

        // Convert to DTO and return
        return UserFeedbackDTO.builder()
                .id(saved.getId())
                .description(saved.getDescription())
                .images(saved.getImages())
                .feedbackId(saved.getFeedbackId())
                .accountId(saved.getAccountId())
                .rating(request.getRating())
                .build();
    }

    /**
     * Process a Base64 encoded file and upload it to S3
     *
     * @param fileDTO   The Base64FileDTO containing file data
     * @param directory The directory to store the file in (e.g., "feedback", "profile")
     * @return The URL of the uploaded file, or null if the file was empty or invalid
     * @throws IOException If an I/O error occurs during file processing
     * @throws ResponseStatusException If the file type is invalid or upload fails
     */
    private String processBase64File(Base64FileDTO fileDTO, String directory) throws IOException {
        if (fileDTO == null || fileDTO.getBase64Content() == null || fileDTO.getBase64Content().isEmpty()) {
            return null;
        }

        File file = null;
        try {
            // Generate a unique filename
            String fileName = UUID.randomUUID() + "-" + fileDTO.getFileName();

            // Convert Base64 to File
            file = FileUtil.convertBase64ToFile(fileDTO.getBase64Content(), fileName);

            // Validate mime type
            String mimeType = fileDTO.getContentType();
            if (!isValidImageType(mimeType)) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST,
                        "Invalid file type for " + fileDTO.getFileName() + ". Only images are allowed.");
            }

            // Generate object key
            String objectKey = directory + "/" + file.getName();

            log.info("Uploading feedback image: {}, type: {}", fileDTO.getFileName(), mimeType);

            // Upload to S3
            String eTag = uploadFileToS3.putS3ObjectAudio(s3Bucket, objectKey, file, mimeType);
            if (eTag == null) {
                throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR,
                        "Failed to upload image: " + fileDTO.getFileName());
            }

            // Return the URL
            return "https://smedia.stepup.edu.vn/" + objectKey;

        } finally {
            // Clean up temp file
            if (file != null && file.exists()) {
                boolean deleted = file.delete();
                if (!deleted) {
                    log.warn("Failed to delete temporary file: {}", file.getAbsolutePath());
                }
            }
        }
    }

    /**
     * Handle file upload to S3
     *
     * @param file      The file to upload
     * @param directory The directory to store the file in (e.g., "feedback", "profile")
     * @return The URL of the uploaded file
     * @throws ResponseStatusException if the upload fails
     */
    private String handleFileUpload(MultipartFile file, String directory) {
        File tempFile = null;
        try {
            // Validate mime type
            String mimeType = file.getContentType();
            if (!isValidImageType(mimeType)) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid file type. Only images are allowed.");
            }

            // Convert MultipartFile to File with UUID as userId
            tempFile = FileUtil.convertMultiPartToFile(file);

            // Generate object key
            String objectKey = directory + "/" + tempFile.getName();

            log.info("Uploading feedback image: {}, type: {}, size: {}",
                    file.getOriginalFilename(), mimeType, file.getSize());

            // Upload to S3
            String eTag = uploadFileToS3.putS3ObjectAudio(s3Bucket, objectKey, tempFile, mimeType);
            if (eTag == null) {
                throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR,
                        "Failed to upload image: " + file.getOriginalFilename());
            }

            // Return the URL
            return "https://smedia.stepup.edu.vn/" + objectKey;

        } catch (Exception e) {
            log.error("Error uploading file", e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR,
                    "Failed to upload file: " + e.getMessage());
        } finally {
            // Clean up temp file
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    private boolean isValidImageType(String mimeType) {
        if (!StringUtils.hasText(mimeType)) {
            return false;
        }

        return mimeType.startsWith("image/");
    }
}
