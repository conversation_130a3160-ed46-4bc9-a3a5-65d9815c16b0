package com.stepup.springrobot.service.ladipage;

import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.ladipage.LadipageOrder;
import com.stepup.springrobot.dto.ladipage.LadipageWebhookRequest;
import com.stepup.springrobot.service.auth.SMSService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class LadipageWebhookService {

    private final SMSService smsService;

    @Value("${ladipage.target.product.id}")
    private Long targetProductId;

    public DataResponseDTO<Void> handleOrderWebhook(LadipageWebhookRequest request) {
        log.info("Processing Ladipage webhook: {}", request);

        if (!"ORDER_PAYMENT_SUCCESS".equals(request.getEvent())) {
            return new DataResponseDTO<>(200, "Webhook received but no action taken", null);
        }

        LadipageOrder order = request.getPayload().getOrder();

        // Only send SMS for specific product_id
        boolean hasTargetProduct = order.getOrderDetails().stream()
                .anyMatch(detail -> targetProductId.equals(detail.getProductId()));

        if (!hasTargetProduct) {
            return new DataResponseDTO<>(200, "Order does not contain target product", null);
        }

        return smsService.sendPurchaseConfirmation(order.getCustomerPhone());
    }
} 