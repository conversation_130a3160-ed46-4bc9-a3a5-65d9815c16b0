package com.stepup.springrobot.service;

import com.stepup.springrobot.model.chat.GPTCharacter;
import com.theokanning.openai.completion.chat.ChatCompletionRequest;
import com.theokanning.openai.completion.chat.ChatCompletionResult;
import com.theokanning.openai.completion.chat.ChatMessage;
import com.theokanning.openai.service.OpenAiService;
import lombok.extern.log4j.Log4j2;

import java.time.Duration;
import java.util.List;

@Log4j2
public class OpenAIUtil {
    private OpenAIUtil() {
    }

    private static ChatCompletionResult handleSendToOpenAI(String token, List<ChatMessage> chatMessages) {
        OpenAiService openAiService = new OpenAiService(token, Duration.ofSeconds(60));
        String modelName = "gpt-3.5-turbo-0125";
        ChatCompletionRequest request = ChatCompletionRequest.builder()
                .model(modelName)
                .messages(chatMessages)
                .temperature(0.0)
                .maxTokens(1500)
                .n(1)
                .frequencyPenalty(0.0)
                .topP(1.0)
                .presencePenalty(0.0)
                .build();
        ChatCompletionResult chatCompletionResult = openAiService.createChatCompletion(request);
        log.info("Gửi request tới OpenAI thành công, model {}, request: {}, response: {}", modelName, request, chatCompletionResult);
        return chatCompletionResult;
    }

    public static ChatMessage getChatResponse(String token, List<ChatMessage> chatMessages) {
        ChatCompletionResult chatCompletionResult = handleSendToOpenAI(token, chatMessages);
        return chatCompletionResult.getChoices().get(0).getMessage();
    }

    public static ChatMessage buildChatMessage(GPTCharacter role, String content) {
        ChatMessage chatMessage = new ChatMessage();
        chatMessage.setContent(content);
        chatMessage.setRole(role.getCharacter());
        return chatMessage;
    }
}
