package com.stepup.springrobot.service.data;

import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.jackson2.JacksonFactory;
import com.google.api.services.sheets.v4.Sheets;
import com.google.api.services.sheets.v4.model.ValueRange;
import com.stepup.springrobot.config.ConfigUtil;
import com.stepup.springrobot.service.SlackWarningSystemService;
import lombok.extern.log4j.Log4j2;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.interceptor.TransactionInterceptor;

import java.util.List;
import java.util.function.Consumer;

import static com.stepup.springrobot.config.ImportDataUtils.getCredentials;

@Log4j2
@Service
public class ImportCommonService {
    protected static final String APPLICATION_NAME = "Google Sheets API Java";

    protected static final JsonFactory JSON_FACTORY = JacksonFactory.getDefaultInstance();

    @Value("${google_sheets_learn_data}")
    protected String SHEET_LEARN_DATA;

    @Autowired
    protected RedissonClient redissonClient;

    @Autowired
    private SlackWarningSystemService slackWarningSystemService;

    protected void processSheet(final String sheetId, final String range, Consumer<List<List<Object>>> processor, boolean isRequireData) throws Exception {
        try {
            // Build a new authorized API client service.
            NetHttpTransport HTTP_TRANSPORT = GoogleNetHttpTransport.newTrustedTransport();
            Sheets service = new Sheets.Builder(HTTP_TRANSPORT, JSON_FACTORY, getCredentials(HTTP_TRANSPORT, JSON_FACTORY)).setApplicationName(APPLICATION_NAME).build();
            ValueRange response = service.spreadsheets().values().get(sheetId, range).execute();

            List<List<Object>> rows = response.getValues();
            if (isRequireData && (rows == null || rows.isEmpty())) {
                log.info("=====No data found in google sheets {} =====", range);
                throw new Exception("No data found in google sheets " + range);
            }

            log.info("=======> syncing data: {}", rows != null ? rows.size() : 0);
            processor.accept(rows);
        } catch (Exception e) {
            log.error("Throw exception when synced google sheets =============== {} {}", range, e);
            slackWarningSystemService.sendWarningSystemToSlack("Lỗi import sheet: " + range + " error: " + e.getMessage(), ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
            TransactionInterceptor.currentTransactionStatus().setRollbackOnly();
            throw e;
        }
    }

    protected List<List<Object>> getSheetData(String sheetId, String range) throws Exception {
        NetHttpTransport HTTP_TRANSPORT = GoogleNetHttpTransport.newTrustedTransport();
        Sheets service = new Sheets.Builder(HTTP_TRANSPORT, JSON_FACTORY, getCredentials(HTTP_TRANSPORT, JSON_FACTORY)).setApplicationName(APPLICATION_NAME).build();
        ValueRange response = service.spreadsheets().values().get(sheetId, range).execute();

        List<List<Object>> rows = response.getValues();
        if (rows == null || rows.isEmpty()) {
            log.info("=====No data found in google sheets {} =====", range);
            throw new Exception("No data found in google sheets " + range);
        }

        log.info("=======> syncing data: " + rows.size());

        return rows;
    }
}
