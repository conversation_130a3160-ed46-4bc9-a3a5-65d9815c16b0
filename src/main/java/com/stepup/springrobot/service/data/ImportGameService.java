package com.stepup.springrobot.service.data;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.model.game.GameAlphaBlastWord;
import com.stepup.springrobot.model.game.GameEchoTowerFloor;
import com.stepup.springrobot.model.game.GameEchoTowerWord;
import com.stepup.springrobot.model.game.GameWordRaceWord;
import com.stepup.springrobot.repository.game.GameAlphaBlastWordRepository;
import com.stepup.springrobot.repository.game.GameEchoTowerFloorRepository;
import com.stepup.springrobot.repository.game.GameEchoTowerWordRepository;
import com.stepup.springrobot.repository.game.GameWordRaceWordRepository;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionInterceptor;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

import static com.stepup.springrobot.config.ImportDataUtils.getRequiredField;
import static com.stepup.springrobot.config.ImportDataUtils.trimData;
import static com.stepup.springrobot.config.LambdaExceptionUtil.rethrowConsumer;

@Log4j2
@Service
public class ImportGameService extends ImportCommonService {
    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private GameEchoTowerFloorRepository gameEchoTowerFloorRepository;

    @Autowired
    private GameEchoTowerWordRepository gameEchoTowerWordRepository;

    @Autowired
    private GameWordRaceWordRepository gameWordRaceWordRepository;

    @Autowired
    private GameAlphaBlastWordRepository gameAlphaBlastWordRepository;

    @Transactional(rollbackFor = Exception.class)
    public void initEchoTowerData() throws Exception {
        try {
            gameEchoTowerFloorRepository.truncateTable();
            processSheet(SHEET_LEARN_DATA, "echo_tower_floor!A2:C", processEchoTowerFloorSheet(), true);
            gameEchoTowerWordRepository.truncateTable();
            processSheet(SHEET_LEARN_DATA, "echo_tower_word!A2:D", processEchoTowerWordSheet(), true);
        } catch (Exception e) {
            log.error("Throw exception when synced google echo_tower_floor sheets =============== {0}", e);
            TransactionInterceptor.currentTransactionStatus().setRollbackOnly();
            throw e;
        }
    }

    private Consumer<List<List<Object>>> processEchoTowerFloorSheet() throws Exception {
        class EchoTowerFloorColumn {
            public static final int ID = 0;
            public static final int ORDER = 1;
            public static final int NAME = 2;

            private EchoTowerFloorColumn() {
            }
        }

        return rethrowConsumer(rows -> {
            List<GameEchoTowerFloor> gameEchoTowerFloors = new ArrayList<>();
            for (List<Object> value : rows) {
                if (value == null || value.isEmpty()) {
                    continue;
                }

                long id = Long.parseLong(getRequiredField(value, EchoTowerFloorColumn.ID));
                double order = Double.parseDouble(trimData(getRequiredField(value, EchoTowerFloorColumn.ORDER)));
                String name = trimData(getRequiredField(value, EchoTowerFloorColumn.NAME));

                gameEchoTowerFloors.add(GameEchoTowerFloor.builder()
                        .id(id)
                        .order(order)
                        .name(name)
                        .build());
            }

            gameEchoTowerFloorRepository.saveAll(gameEchoTowerFloors);
        });
    }

    private Consumer<List<List<Object>>> processEchoTowerWordSheet() throws Exception {
        class EchoTowerWordColumn {
            public static final int ID = 0;
            public static final int FLOOR_ID = 1;
            public static final int ORDER = 2;
            public static final int WORD = 3;

            private EchoTowerWordColumn() {
            }
        }

        return rethrowConsumer(rows -> {
            List<GameEchoTowerWord> gameEchoTowerWords = new ArrayList<>();
            for (List<Object> value : rows) {
                if (value == null || value.isEmpty()) {
                    continue;
                }

                long id = Long.parseLong(getRequiredField(value, EchoTowerWordColumn.ID));
                long floorId = Long.parseLong(getRequiredField(value, EchoTowerWordColumn.FLOOR_ID));
                double order = Double.parseDouble(trimData(getRequiredField(value, EchoTowerWordColumn.ORDER)));
                String word = trimData(getRequiredField(value, EchoTowerWordColumn.WORD));

                gameEchoTowerWords.add(GameEchoTowerWord.builder()
                        .id(id)
                        .floorId(floorId)
                        .order(order)
                        .word(word)
                        .build());
            }

            gameEchoTowerWordRepository.saveAll(gameEchoTowerWords);
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void initGameWordRaceData() throws Exception {
        try {
            gameWordRaceWordRepository.truncateTable();
            processSheet(SHEET_LEARN_DATA, "word_race!A2:F", processWordRaceSheet(), true);
        } catch (Exception e) {
            log.error("Throw exception when synced google echo_tower_floor sheets =============== {0}", e);
            TransactionInterceptor.currentTransactionStatus().setRollbackOnly();
            throw e;
        }
    }

    private Consumer<List<List<Object>>> processWordRaceSheet() throws Exception {
        class EchoTowerFloorColumn {
            public static final int ID = 0;
            public static final int WORD = 1;
            public static final int PART_ONE = 2;
            public static final int PART_TWO = 3;
            public static final int PART_THREE = 4;
            public static final int IS_POWER_UP = 5;

            private EchoTowerFloorColumn() {
            }
        }

        return rethrowConsumer(rows -> {
            List<GameWordRaceWord> gameWordRaceWords = new ArrayList<>();
            for (int i = 0; i< rows.size(); i++ ) {
                try {
                    List<Object> value = rows.get(i);
                    if (value == null || value.isEmpty()) {
                        continue;
                    }

                    long id = Long.parseLong(getRequiredField(value, EchoTowerFloorColumn.ID));
                    String word = trimData(getRequiredField(value, EchoTowerFloorColumn.WORD));
                    String partOne = trimData(getRequiredField(value, EchoTowerFloorColumn.PART_ONE));
                    String partTwo = trimData(getRequiredField(value, EchoTowerFloorColumn.PART_TWO));
                    String partThree = trimData(getRequiredField(value, EchoTowerFloorColumn.PART_THREE));
                    boolean isPowerUp = Boolean.parseBoolean(getRequiredField(value, EchoTowerFloorColumn.IS_POWER_UP));

                    gameWordRaceWords.add(GameWordRaceWord.builder()
                            .id(id)
                            .word(word)
                            .partOne(partOne)
                            .partTwo(partTwo)
                            .partThree(partThree)
                            .isPowerUp(isPowerUp)
                            .build());
                } catch (Exception e) {
                    throw new Exception("Error at row " + i + ". " + e.getMessage());
                }
            }

            gameWordRaceWordRepository.saveAll(gameWordRaceWords);
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void initGameAlphaBlastData() throws Exception {
        try {
            gameAlphaBlastWordRepository.truncateTable();
            processSheet(SHEET_LEARN_DATA, "alpha_blast!A2:B", processAlphaBlastSheet(), true);
        } catch (Exception e) {
            log.error("Throw exception when synced google alpha_blast sheets =============== {0}", e);
            TransactionInterceptor.currentTransactionStatus().setRollbackOnly();
            throw e;
        }
    }

    private Consumer<List<List<Object>>> processAlphaBlastSheet() throws Exception {
        class AlphaBlastColumn {
            public static final int ID = 0;
            public static final int WORD = 1;

            private AlphaBlastColumn() {
            }
        }

        return rethrowConsumer(rows -> {
            List<GameAlphaBlastWord> gameAlphaBlastWords = new ArrayList<>();
            for (int i = 0; i< rows.size(); i++ ) {
                try {
                    List<Object> value = rows.get(i);
                    if (value == null || value.isEmpty()) {
                        continue;
                    }

                    long id = Long.parseLong(getRequiredField(value, AlphaBlastColumn.ID));
                    String word = trimData(getRequiredField(value, AlphaBlastColumn.WORD));

                    gameAlphaBlastWords.add(GameAlphaBlastWord.builder()
                            .id(id)
                            .word(word)
                            .build());
                } catch (Exception e) {
                    throw new Exception("Error at row " + i + ". " + e.getMessage());
                }
            }

            gameAlphaBlastWordRepository.saveAll(gameAlphaBlastWords);
        });
    }
}
