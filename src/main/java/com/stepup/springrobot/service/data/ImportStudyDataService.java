package com.stepup.springrobot.service.data;

import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.services.sheets.v4.Sheets;
import com.google.api.services.sheets.v4.model.ValueRange;
import com.stepup.springrobot.model.study.StudyLesson;
import com.stepup.springrobot.model.study.StudyLessonType;
import com.stepup.springrobot.model.study.StudyTopic;
import com.stepup.springrobot.model.study.StudyUnit;
import com.stepup.springrobot.repository.study.StudyLessonRepository;
import com.stepup.springrobot.repository.study.StudyTopicRepository;
import com.stepup.springrobot.repository.study.StudyUnitRepository;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionInterceptor;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.stepup.springrobot.config.ImportDataUtils.*;

@Log4j2
@Service
public class ImportStudyDataService extends ImportCommonService {
    @Autowired
    private StudyUnitRepository studyUnitRepository;

    @Autowired
    private StudyTopicRepository studyTopicRepository;

    @Autowired
    private StudyLessonRepository studyLessonRepository;

    @Transactional(rollbackFor = Exception.class)
    public void initStudyUnitData() throws Exception {
        try {
            studyUnitRepository.truncateTable();
            Map<Long, StudyUnit> studyUnitHashMap = getStudyUnitFromSheet(SHEET_LEARN_DATA,"study_unit_dev!A2:C");
            Map<Long, StudyTopic> studyTopicHashMap = getStudyTopicFromSheet(SHEET_LEARN_DATA,"study_topic_dev!A2:F");
            Map<Long, StudyLesson> studyLessonHashMap = getStudyLessonFromSheet(SHEET_LEARN_DATA,"study_lesson_dev!A2:J");
            // Group topic by unit
            Map<Long, List<StudyTopic>> topicsMapByUnit = studyTopicHashMap.values().stream()
                    .collect(Collectors.groupingBy(StudyTopic::getUnitId));
            for (StudyUnit unit : studyUnitHashMap.values()) {
                if (CollectionUtils.isEmpty(topicsMapByUnit.get(unit.getId()))) {
                    throw new Exception("Không tồn tại topic trong unit " + unit.getId());
                }
            }

            studyUnitRepository.saveAll(studyUnitHashMap.values());

            studyTopicRepository.truncateTable();
            // Group lessons by topic
            Map<Long, List<StudyLesson>> lessonsMapByTopic = studyLessonHashMap.values().stream()
                    .collect(Collectors.groupingBy(StudyLesson::getTopicId));
            for (StudyTopic topic : studyTopicHashMap.values()) {
                if (CollectionUtils.isEmpty(lessonsMapByTopic.get(topic.getId()))) {
                    throw new Exception("Không tồn tại lesson trong topic " + topic.getId());
                }

                if (!studyUnitHashMap.containsKey(topic.getUnitId())) {
                    throw new Exception("Không tồn tại unit " + topic.getUnitId() + " cho topic " + topic.getId());
                }
            }

            studyTopicRepository.saveAll(studyTopicHashMap.values());

            studyLessonRepository.truncateTable();
            for (StudyLesson lesson : studyLessonHashMap.values()) {
                if (!studyTopicHashMap.containsKey(lesson.getTopicId())) {
                    throw new Exception("Không tồn tại topic " + lesson.getTopicId() + " cho lesson " + lesson.getId());
                }
            }

            studyLessonRepository.saveAll(studyLessonHashMap.values());

        } catch (Exception e) {
            log.error("Throw exception when synced google study unit sheets =============== {0}", e);
            TransactionInterceptor.currentTransactionStatus().setRollbackOnly();
            throw e;
        }
    }

    private Map<Long, StudyUnit> getStudyUnitFromSheet(String sheetId, String range) throws Exception {
        class StudyUnitColumn {
            public static final int ID = 0;
            public static final int NAME = 1;
            public static final int ORDER = 2;

            private StudyUnitColumn() {
            }
        }

        List<List<Object>> rows = getSheetData(sheetId, range);
        Map<Long, StudyUnit> studyUnitHashMap = new HashMap<>();
        for (int i = 0; i < rows.size(); i++) {
            List<Object> value = rows.get(i);
            if (value == null || value.isEmpty()) {
                continue;
            }

            try {
                Long unitId = Long.parseLong(getRequiredField(value, StudyUnitColumn.ID));
                String name = trimData(getRequiredField(value, StudyUnitColumn.NAME));
                Double order = Double.parseDouble(getRequiredField(value, StudyUnitColumn.ORDER));

                studyUnitHashMap.put(unitId, StudyUnit.builder()
                        .id(unitId)
                        .name(name)
                        .order(order)
                        .build());
            } catch (Exception e) {
                int currentRow = i + 1;
                throw new Exception("Study Unit: Error at row " + currentRow + ". " + e.getMessage());
            }
        }

        return studyUnitHashMap;
    }

    private Map<Long, StudyTopic> getStudyTopicFromSheet(String sheetId, String range) throws Exception {
        class StudyTopicColumn {
            public static final int ID = 0;
            public static final int UNIT_ID = 1;
            public static final int ORDER = 2;
            public static final int TOPIC_NAME = 3;
            public static final int THUMBNAIL = 4;
            public static final int DURATION = 5;

            private StudyTopicColumn() {
            }
        }

        List<List<Object>> rows = getSheetData(sheetId, range);
        Map<Long, StudyTopic> studyTopicHashMap = new HashMap<>();
        for (int i = 0; i < rows.size(); i++) {
            List<Object> value = rows.get(i);
            if (value == null || value.isEmpty()) {
                continue;
            }

            try {
                Long id = Long.parseLong(getRequiredField(value, StudyTopicColumn.ID));
                Long unitId = Long.parseLong(getRequiredField(value, StudyTopicColumn.UNIT_ID));
                Double order = Double.parseDouble(getRequiredField(value, StudyTopicColumn.ORDER));
                String name = trimData(getRequiredField(value, StudyTopicColumn.TOPIC_NAME));
                String thumbnail = trimData(getRequiredField(value, StudyTopicColumn.THUMBNAIL));
                Integer duration = Integer.parseInt(trimData(getRequiredField(value, StudyTopicColumn.DURATION)));

                studyTopicHashMap.put(id, StudyTopic.builder()
                        .id(id)
                        .name(name)
                        .order(order)
                        .unitId(unitId)
                        .thumbnail(thumbnail)
                        .duration(duration)
                        .build());
            } catch (Exception e) {
                int currentRow = i + 1;
                throw new Exception("Study topic: Error at row " + currentRow + ". " + e.getMessage());
            }
        }

        return studyTopicHashMap;
    }

    private Map<Long, StudyLesson> getStudyLessonFromSheet(String sheetId, String range) throws Exception {
        class StudyLessonColumn {
            public static final int ID = 0;
            public static final int TOPIC_ID = 1;
            public static final int ORDER = 2;
            public static final int NAME = 3;
            public static final int ICON = 4;
            public static final int TYPE = 5;
            public static final int BOT_ID = 6;
            public static final int THUMBNAIL = 7;
            public static final int TITLE = 8;
            public static final int DESCRIPTION = 9;

            private StudyLessonColumn() {
            }
        }

        List<List<Object>> rows = getSheetData(sheetId, range);
        Map<Long, StudyLesson> studyLessonHashMap = new HashMap<>();
        for (int i = 0; i < rows.size(); i++) {
            List<Object> value = rows.get(i);
            if (value == null || value.isEmpty()) {
                continue;
            }

            try {
                Long id = Long.parseLong(getRequiredField(value, StudyLessonColumn.ID));
                Long topicId = Long.parseLong(getRequiredField(value, StudyLessonColumn.TOPIC_ID));
                Double order = Double.parseDouble(getRequiredField(value, StudyLessonColumn.ORDER));
                String name = trimData(getRequiredField(value, StudyLessonColumn.NAME));
                String icon = trimData(getRequiredField(value, StudyLessonColumn.ICON));
                StudyLessonType lessonType = StudyLessonType.from(trimData(getRequiredField(value, StudyLessonColumn.TYPE)));
                Long botId = Long.parseLong(getRequiredField(value, StudyLessonColumn.BOT_ID));
                String thumbnail = trimData(getRequiredField(value, StudyLessonColumn.THUMBNAIL));
                String title = trimData(getRequiredField(value, StudyLessonColumn.TITLE));
                String description = trimData(getRequiredField(value, StudyLessonColumn.DESCRIPTION));

                studyLessonHashMap.put(id, StudyLesson.builder()
                        .id(id)
                        .topicId(topicId)
                        .order(order)
                        .name(name)
                        .icon(icon)
                        .type(lessonType)
                        .botId(botId)
                        .thumbnail(thumbnail)
                        .title(title)
                        .description(description)
                        .build());
            } catch (Exception e) {
                int currentRow = i + 1;
                throw new Exception("Study Lesson: Error at row " + currentRow + ". " + e.getMessage());
            }
        }

        return studyLessonHashMap;
    }

}
