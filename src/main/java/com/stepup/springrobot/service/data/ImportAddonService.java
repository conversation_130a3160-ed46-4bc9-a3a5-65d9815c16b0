package com.stepup.springrobot.service.data;

import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.model.addon.Song;
import com.stepup.springrobot.model.addon.Story;
import com.stepup.springrobot.model.chat.*;
import com.stepup.springrobot.repository.addon.SongRepository;
import com.stepup.springrobot.repository.addon.StoryRepository;
import com.stepup.springrobot.repository.chat.ListeningEmotionRepository;
import com.stepup.springrobot.repository.chat.ServoActionRepository;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionInterceptor;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;

import static com.stepup.springrobot.config.ImportDataUtils.*;
import static com.stepup.springrobot.config.LambdaExceptionUtil.rethrowConsumer;
import static com.stepup.springrobot.config.LambdaExceptionUtil.rethrowFunction;

@Log4j2
@Service
public class ImportAddonService extends ImportCommonService {
    @Autowired
    private SongRepository songRepository;

    @Autowired
    private StoryRepository storyRepository;

    @Autowired
    private ServoActionRepository servoActionRepository;

    @Autowired
    private ListeningEmotionRepository listeningEmotionRepository;

    private static class ServoActionColumn {
        public static final int ID = 0;
        public static final int EMOTION_TYPE = 1;
        public static final int PART_TYPE = 2;
        public static final int REPETITION = 3;
        public static final int START_TIME = 4;
        public static final int ANGLE = 5;
        public static final int TIME = 6;
    }

    @Transactional(rollbackFor = Exception.class)
    public void initMusicData() throws Exception {
        try {
            songRepository.truncateTable();
            processSheet(SHEET_LEARN_DATA, "music!A2:F", processMusicSheet(), true);
        } catch (Exception e) {
            log.error("Throw exception when synced google music sheets =============== {0}", e);
            TransactionInterceptor.currentTransactionStatus().setRollbackOnly();
            throw e;
        }
    }

    private Consumer<List<List<Object>>> processMusicSheet() throws Exception {
        class MusicColumn {
            public static final int ID = 0;
            public static final int ORDER = 1;
            public static final int TITLE = 2;
            public static final int ARTIST = 3;
            public static final int THUMBNAIL = 4;
            public static final int URL = 5;

            private MusicColumn() {
            }
        }

        return rethrowConsumer(rows -> {
            List<Song> songList = new ArrayList<>();
            for (int i = 0; i < rows.size(); i++) {
                List<Object> value = rows.get(i);
                if (value == null || value.isEmpty()) {
                    continue;
                }

                try {
                    Long id = Long.parseLong(getRequiredField(value, MusicColumn.ID));
                    Double order = Double.parseDouble(getRequiredField(value, MusicColumn.ORDER));
                    String title = trimData(getRequiredField(value, MusicColumn.TITLE));
                    String artist = trimData(getRequiredField(value, MusicColumn.ARTIST));
                    String thumbnail = trimData(getRequiredField(value, MusicColumn.THUMBNAIL));
                    String url = trimData(getRequiredField(value, MusicColumn.URL));

                    songList.add(Song.builder()
                            .id(id)
                            .order(order)
                            .title(title)
                            .artist(artist)
                            .thumbnail(thumbnail)
                            .url(url)
                            .build());
                } catch (Exception e) {
                    int currentRow = i + 1;
                    throw new Exception("Song: Error at row " + currentRow + ". " + e.getMessage());
                }
            }

            songRepository.saveAll(songList);
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void initStoryData() throws Exception {
        try {
            storyRepository.truncateTable(); // Assuming you have this method in StoryRepository
            processSheet(SHEET_LEARN_DATA, "story!A2:G", processStorySheet(), true);
        } catch (Exception e) {
            log.error("Throw exception when synced google story sheets =============== {0}", e);
            TransactionInterceptor.currentTransactionStatus().setRollbackOnly();
            throw e;
        }
    }

    private Consumer<List<List<Object>>> processStorySheet() throws Exception {
        class StoryColumn {
            public static final int ID = 0;
            public static final int ORDER = 1;
            public static final int TITLE = 2;
            public static final int AUTHOR = 3;
            public static final int DESCRIPTION = 4;
            public static final int THUMBNAIL = 5;
            public static final int URL = 6;

            private StoryColumn() {
            }
        }

        return rethrowConsumer(rows -> {
            List<Story> storyList = new ArrayList<>();
            for (int i = 0; i < rows.size(); i++) {
                List<Object> value = rows.get(i);
                if (value == null || value.isEmpty()) {
                    continue;
                }

                try {
                    Long id = Long.parseLong(getRequiredField(value, StoryColumn.ID));
                    Double order = Double.parseDouble(getRequiredField(value, StoryColumn.ORDER));
                    String title = trimData(getRequiredField(value, StoryColumn.TITLE));
                    String author = trimData(getRequiredField(value, StoryColumn.AUTHOR));
                    String description = trimData(getRequiredField(value, StoryColumn.DESCRIPTION));
                    String thumbnail = trimData(getRequiredField(value, StoryColumn.THUMBNAIL));
                    String url = trimData(getRequiredField(value, StoryColumn.URL));

                    storyList.add(Story.builder()
                            .id(id)
                            .order(order)
                            .title(title)
                            .author(author)
                            .description(description)
                            .thumbnail(thumbnail)
                            .url(url)
                            .build());
                } catch (Exception e) {
                    int currentRow = i + 1;
                    throw new Exception("Story: Error at row " + currentRow + ". " + e.getMessage());
                }
            }

            storyRepository.saveAll(storyList);
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void initServoData() throws Exception {
        try {
            servoActionRepository.truncateTable();
            processSheet(SHEET_LEARN_DATA, "servo_action!A2:G", processServoSheet(), true);
            redissonClient.getMapCache(CodeDefine.REDIS_KEY_SERVO_BY_EMOTION).delete();
        } catch (Exception e) {
            log.error("Throw exception when synced google servo sheets =============== {0}", e);
            TransactionInterceptor.currentTransactionStatus().setRollbackOnly();
            throw e;
        }
    }

    private Consumer<List<List<Object>>> processServoSheet() throws Exception {
        return rethrowConsumer(rows -> {
            ArrayNode currentPartDetails = JsonNodeFactory.instance.arrayNode();
            ArrayNode currentParts = JsonNodeFactory.instance.arrayNode();
            List<ServoAction> servoActions = new ArrayList<>();
            for (int rowIndex = 0; rowIndex < rows.size(); rowIndex++) {
                List<Object> value = rows.get(rowIndex);
                if (value == null || value.isEmpty()) {
                    continue;
                }

                try {
                    ObjectNode partDetail = getPartDetailToJsonNode().apply(value);
                    if (partDetail != null) {
                        currentPartDetails.add(partDetail);
                    } else {
                        ObjectNode part = getPartToJsonNode(currentPartDetails).apply(value);
                        currentPartDetails = JsonNodeFactory.instance.arrayNode();
                        if (part != null) {
                            currentParts.add(part);
                        } else {
                            Long id = Long.parseLong(getRequiredField(value, ServoActionColumn.ID));
                            String emotionType = getRequiredField(value, ServoActionColumn.EMOTION_TYPE);

                            ObjectNode data = JsonNodeFactory.instance.objectNode();
                            data.set("parts", currentParts);
                            currentParts = JsonNodeFactory.instance.arrayNode();

                            servoActions.add(ServoAction.builder()
                                    .id(id)
                                    .type(emotionType)
                                    .data(data).build());
                        }
                    }
                } catch (Exception e) {
                    log.error("Error in sheet at row {}: {}", rowIndex + 2, e.getMessage());
                    throw e;
                }
            }

            servoActionRepository.saveAll(servoActions);
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void initListeningEmotionData() throws Exception {
        try {
            listeningEmotionRepository.truncateTable();
            processSheet(SHEET_LEARN_DATA, "listening_emotion!A2:G", processListeningEmotionSheet(), true);
        } catch (Exception e) {
            log.error("Throw exception when synced google listening_emotion sheets =============== {0}", e);
            TransactionInterceptor.currentTransactionStatus().setRollbackOnly();
            throw e;
        }
    }

    private Consumer<List<List<Object>>> processListeningEmotionSheet() throws Exception {
        return rethrowConsumer(rows -> {
            ArrayNode currentPartDetails = JsonNodeFactory.instance.arrayNode();
            ArrayNode currentParts = JsonNodeFactory.instance.arrayNode();
            List<ListeningEmotion> listeningEmotions = new ArrayList<>();
            for (int rowIndex = 0; rowIndex < rows.size(); rowIndex++) {
                List<Object> value = rows.get(rowIndex);
                if (value == null || value.isEmpty()) {
                    continue;
                }

                try {
                    ObjectNode partDetail = getPartDetailToJsonNode().apply(value);
                    if (partDetail != null) {
                        currentPartDetails.add(partDetail);
                    } else {
                        ObjectNode part = getPartToJsonNode(currentPartDetails).apply(value);
                        currentPartDetails = JsonNodeFactory.instance.arrayNode();
                        if (part != null) {
                            currentParts.add(part);
                        } else {
                            Long id = Long.parseLong(getRequiredField(value, ServoActionColumn.ID));
                            String emotionType = getRequiredField(value, ServoActionColumn.EMOTION_TYPE);

                            ObjectNode data = JsonNodeFactory.instance.objectNode();
                            data.set("parts", currentParts);
                            currentParts = JsonNodeFactory.instance.arrayNode();

                            listeningEmotions.add(ListeningEmotion.builder()
                                    .id(id)
                                    .type(emotionType)
                                    .data(data).build());
                        }
                    }
                } catch (Exception e) {
                    log.error("Error in sheet at row {}: {}", rowIndex + 2, e.getMessage());
                    throw e;
                }
            }

            listeningEmotionRepository.saveAll(listeningEmotions);
        });
    }

    private Function<List<Object>, ObjectNode> getPartDetailToJsonNode() {
        return rethrowFunction(values -> {
            String angleStr = getNullableField(values, ServoActionColumn.ANGLE);
            if (StringUtils.isEmpty(angleStr)) {
                return null;
            }

            int angle = Integer.parseInt(angleStr);
            Double time = Double.parseDouble(getRequiredField(values, ServoActionColumn.TIME)) ;

            ObjectNode taskDetail = JsonNodeFactory.instance.objectNode();
            taskDetail.put("angle", angle);
            taskDetail.put("time", time);
            return taskDetail;
        });
    }

    private Function<List<Object>, ObjectNode> getPartToJsonNode(ArrayNode currentPartDetails) {
        return rethrowFunction(values -> {
            String partTypeStr = getNullableField(values, ServoActionColumn.PART_TYPE);
            if (StringUtils.isEmpty(partTypeStr)) {
                return null;
            }

            ServoPartType servoPartType = ServoPartType.from(partTypeStr);
            if (servoPartType == null) {
                throw new Exception("Invalid part type: " + partTypeStr);
            }

            int repetition = Integer.parseInt(getRequiredField(values, ServoActionColumn.REPETITION));
            int startTime = Integer.parseInt(getRequiredField(values, ServoActionColumn.START_TIME));

            ObjectNode part = JsonNodeFactory.instance.objectNode();
            part.put("part_type", servoPartType.getType());
            part.put("repetition", repetition);
            part.put("start_time", startTime);
            part.set("steps", currentPartDetails);
            return part;
        });
    }
}
