package com.stepup.springrobot.service.communication;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.chat.ConversationLogReqDTO;
import com.stepup.springrobot.dto.communication.MqttUpdateResultReqDTO;
import com.stepup.springrobot.dto.communication.RobotVolumeReqDTO;
import com.stepup.springrobot.dto.communication.WifiConnectResultDTO;
import com.stepup.springrobot.dto.communication.WifiScanResultReqDTO;
import com.stepup.springrobot.dto.mqtt.MqttAuthenReqDTO;
import com.stepup.springrobot.dto.mqtt.MqttAuthenResDTO;
import com.stepup.springrobot.dto.mqtt.MqttAuthorizeReqDTO;
import com.stepup.springrobot.dto.mqtt.MqttAuthorizeResDTO;
import com.stepup.springrobot.exception.business.content.ContentNotFoundException;
import com.stepup.springrobot.exception.business.request.ConflictEntityException;
import com.stepup.springrobot.exception.business.request.InvalidFormatException;
import com.stepup.springrobot.exception.business.user.InvalidTokenException;
import com.stepup.springrobot.model.alarm.AlarmScheduleExecution;
import com.stepup.springrobot.model.chat.RobotUserConversation;
import com.stepup.springrobot.model.communication.CommunicationRequest;
import com.stepup.springrobot.model.communication.CommunicationRequestStatusType;
import com.stepup.springrobot.model.mqtt.*;
import com.stepup.springrobot.model.robot.RobotConfig;
import com.stepup.springrobot.model.robot.RobotFirmwareVersion;
import com.stepup.springrobot.repository.alarm.AlarmScheduleExecutionRepository;
import com.stepup.springrobot.repository.chat.RobotUserConversationRepository;
import com.stepup.springrobot.repository.communication.CommunicationRequestRepository;
import com.stepup.springrobot.repository.game.RobotFirmwareVersionRepository;
import com.stepup.springrobot.repository.mqtt.MqttACLRepository;
import com.stepup.springrobot.repository.mqtt.MqttUserRepository;
import com.stepup.springrobot.repository.robot.RobotConfigRepository;
import com.stepup.springrobot.repository.robot.RobotUserRepository;
import com.stepup.springrobot.security.JwtService;
import com.stepup.springrobot.service.CommonService;
import com.stepup.springrobot.service.SlackWarningSystemService;
import com.stepup.springrobot.service.UploadFileToS3;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Log4j2
@Service
public class RobotDeviceService extends CommonService {
    @Value("${mqtt_user_auth_token}")
    private String mqttUserAuthToken;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private CommunicationRequestRepository communicationRequestRepository;

    @Autowired
    private RobotUserRepository robotUserRepository;

    @Autowired
    private RobotFirmwareVersionRepository robotFirmwareVersionRepository;

    @Autowired
    private MqttUserRepository mqttUserRepository;

    @Autowired
    private MqttACLRepository mqttACLRepository;

    @Autowired
    private RobotConfigRepository robotConfigRepository;

    @Autowired
    private RobotUserConversationRepository robotUserConversationRepository;

    @Autowired
    private AlarmScheduleExecutionRepository alarmScheduleExecutionRepository;

    @Autowired
    private PasswordEncoder bcryptEncoder;

    protected RobotDeviceService(ObjectMapper objectMapper, UploadFileToS3 uploadFileToS3, JwtService jwtService, SlackWarningSystemService slackWarningSystemService) {
        super(objectMapper, uploadFileToS3, jwtService, slackWarningSystemService);
    }

    public DataResponseDTO<?> saveWifiScanResult(HttpServletRequest request, WifiScanResultReqDTO resultReqDTO) throws JsonProcessingException {
        CommunicationRequest communicationRequest = communicationRequestRepository.findById(resultReqDTO.getRequestId()).orElse(null);
        if (communicationRequest == null) {
            throw new ContentNotFoundException("request_id", resultReqDTO.getRequestId());
        } else if (MqttMessageType.SCAN_WIFI != communicationRequest.getType()) {
            throw new InvalidFormatException("Request type không phải SCAN_WIFI");
        }

        communicationRequest.setReceivedData(objectMapper.writeValueAsString(resultReqDTO));
        communicationRequestRepository.save(communicationRequest);
        return new DataResponseDTO<>(CodeDefine.OK, "Lưu kết quả scan wifi thành công");
    }

    public DataResponseDTO<?> saveWifiConnectResult(HttpServletRequest request, WifiConnectResultDTO resultReqDTO) throws JsonProcessingException {
        CommunicationRequest communicationRequest = communicationRequestRepository.findById(resultReqDTO.getRequestId()).orElse(null);
        if (communicationRequest == null) {
            throw new ContentNotFoundException("request_id", resultReqDTO.getRequestId());
        } else if (MqttMessageType.CONNECT_WIFI != communicationRequest.getType()) {
            throw new InvalidFormatException("Request type không phải CONNECT_WIFI");
        }

        communicationRequest.setReceivedData(objectMapper.writeValueAsString(resultReqDTO));
        communicationRequestRepository.save(communicationRequest);
        return new DataResponseDTO<>(CodeDefine.OK, "Lưu kết quả connect wifi thành công");
    }

    public DataResponseDTO<?> saveUpdateFirmwareResult(HttpServletRequest request, MqttUpdateResultReqDTO resultReqDTO) throws JsonProcessingException {
        CommunicationRequest communicationRequest = communicationRequestRepository.findById(resultReqDTO.getRequestId()).orElse(null);
        if (communicationRequest == null) {
            throw new ContentNotFoundException("request_id", resultReqDTO.getRequestId());
        } else if (MqttMessageType.UPDATE_FIRMWARE != communicationRequest.getType()) {
            throw new InvalidFormatException("Request type không phải UPDATE_FIRMWARE");
        }

        if (resultReqDTO.getStatus() == CommunicationRequestStatusType.SUCCESS) {
            RobotConfig robotConfig = robotConfigRepository.findByRobotId(communicationRequest.getRobotId());
            RobotFirmwareVersion robotFirmwareVersion = robotFirmwareVersionRepository.getLatestFirmware();
            if (robotConfig == null) {
                robotConfig = RobotConfig.builder()
                        .firmwareVersion(robotFirmwareVersion.getVersion())
                        .robotId(communicationRequest.getRobotId())
                        .build();
            } else {
                robotConfig.setFirmwareVersion(robotFirmwareVersion.getVersion());
            }

            robotConfigRepository.save(robotConfig);
        }

        communicationRequest.setReceivedData(objectMapper.writeValueAsString(resultReqDTO));
        communicationRequest.setStatus(resultReqDTO.getStatus());
        communicationRequestRepository.save(communicationRequest);
        return new DataResponseDTO<>(CodeDefine.OK, "Lưu kết quả update firmware thành công");
    }

    public DataResponseDTO<?> saveMqttRequestResult(HttpServletRequest request, MqttUpdateResultReqDTO resultReqDTO) throws JsonProcessingException {
        CommunicationRequest communicationRequest = communicationRequestRepository.findById(resultReqDTO.getRequestId()).orElse(null);
        if (communicationRequest == null) {
            throw new ContentNotFoundException("request_id", resultReqDTO.getRequestId());
        }

        if (communicationRequest.getStatus() != null && communicationRequest.getStatus() != CommunicationRequestStatusType.PENDING) {
            throw new ConflictEntityException("kết quả mqtt trigger");
        }

        communicationRequest.setReceivedData(objectMapper.writeValueAsString(resultReqDTO));
        communicationRequest.setStatus(resultReqDTO.getStatus());
        communicationRequestRepository.save(communicationRequest);
        return new DataResponseDTO<>(CodeDefine.OK, "Lưu kết quả mqtt trigger thành công");
    }

    public DataResponseDTO<?> saveAlarmResult(HttpServletRequest request, MqttUpdateResultReqDTO resultReqDTO) throws JsonProcessingException {
        AlarmScheduleExecution alarmScheduleExecution = alarmScheduleExecutionRepository.findById(resultReqDTO.getRequestId()).orElse(null);
        if (alarmScheduleExecution == null) {
            throw new ContentNotFoundException("request_id", resultReqDTO.getRequestId());
        }

        if (alarmScheduleExecution.getStatus() != null && alarmScheduleExecution.getStatus() != CommunicationRequestStatusType.PENDING) {
            throw new ConflictEntityException("kết quả mqtt trigger");
        }

        alarmScheduleExecution.setReceivedData(objectMapper.writeValueAsString(resultReqDTO));
        alarmScheduleExecution.setStatus(resultReqDTO.getStatus());
        alarmScheduleExecutionRepository.save(alarmScheduleExecution);
        return new DataResponseDTO<>(CodeDefine.OK, "Lưu kết quả alarm thành công");
    }

    public MqttAuthenResDTO mqttAuthenticate(MqttAuthenReqDTO reqDTO) throws JsonProcessingException {
        if (StringUtils.isEmpty(reqDTO.getToken()) || !mqttUserAuthToken.equals(reqDTO.getToken())) {
            throw new InvalidTokenException();
        }

        String username = reqDTO.getUsername();
        MqttUser mqttUser = mqttUserRepository.findByUsername(username);
        MqttAuthenResDTO mqttAuthenResDTO;
        if (mqttUser == null
                || Boolean.FALSE.equals(mqttUser.getIsActive())
                || !bcryptEncoder.matches(reqDTO.getPassword(), mqttUser.getPassword())) {
            mqttAuthenResDTO = MqttAuthenResDTO.builder()
                    .result(MqttPermissionType.deny)
                    .build();
        } else {
            mqttAuthenResDTO = MqttAuthenResDTO.builder()
                    .result(MqttPermissionType.allow)
                    .isSuperUser(mqttUser.getIsAdmin())
                    .build();
        }

        log.info("=====> MQTT authen - request: {}, response: {}", objectMapper.writeValueAsString(reqDTO), objectMapper.writeValueAsString(mqttAuthenResDTO));
        return mqttAuthenResDTO;
    }

    public MqttAuthorizeResDTO mqttAuthorize(MqttAuthorizeReqDTO reqDTO) throws JsonProcessingException {
        if (StringUtils.isEmpty(reqDTO.getToken()) || !mqttUserAuthToken.equals(reqDTO.getToken())) {
            throw new InvalidTokenException();
        }

        String username = reqDTO.getUsername();
        MqttUser mqttUser = mqttUserRepository.findByUsername(username);
        MqttActionType actionType = MqttActionType.from(reqDTO.getAction());
        MqttPermissionType permissionType = MqttPermissionType.deny;
        if (mqttUser != null && Boolean.TRUE.equals(mqttUser.getIsActive())) {
            List<MqttACL> mqttACLs = mqttACLRepository.findByMqttUserIdAndTopic(mqttUser.getId(), reqDTO.getTopic());
            log.info("=====> mqttACLs: {}",  objectMapper.writeValueAsString(mqttACLs));
            if (!CollectionUtils.isEmpty(mqttACLs)
                    && (mqttACLs.stream().anyMatch(acl -> acl.getPermission() == MqttPermissionType.allow && acl.getAction() == MqttActionType.all)
                    || mqttACLs.stream().anyMatch(acl -> acl.getPermission() == MqttPermissionType.allow && acl.getAction() == actionType))) {
                permissionType = MqttPermissionType.allow;
            }
        }

        MqttAuthorizeResDTO mqttAuthenResDTO = MqttAuthorizeResDTO.builder()
                .result(permissionType)
                .build();
        log.info("=====> MQTT author - request: {}, response: {}", objectMapper.writeValueAsString(reqDTO), objectMapper.writeValueAsString(mqttAuthenResDTO));
        return mqttAuthenResDTO;
    }

    public DataResponseDTO<?> saveConversationLog(ConversationLogReqDTO logReqDTO) throws JsonProcessingException {
        RobotUserConversation robotUserConversation = robotUserConversationRepository.findBySocketSessionId(logReqDTO.getSocketSessionId());
        if (robotUserConversation != null) {
            if (robotUserConversation.getLog() == null) {
                ArrayNode arrayNode = JsonNodeFactory.instance.arrayNode();
                arrayNode.add(logReqDTO.getLog());
                robotUserConversation.setLog(objectMapper.writeValueAsString(arrayNode));
            } else {
                ArrayNode arrayNode = (ArrayNode) objectMapper.readTree(robotUserConversation.getLog());
                arrayNode.add(logReqDTO.getLog());
                robotUserConversation.setLog(objectMapper.writeValueAsString(arrayNode));
            }

            robotUserConversationRepository.save(robotUserConversation);
        }

        return new DataResponseDTO<>(CodeDefine.OK, "Lưu kết log hội thoại thành công thành công");
    }

    public DataResponseDTO<?> saveRobotVolumeSetting(RobotVolumeReqDTO reqDTO) {
        RobotConfig robotConfig = robotConfigRepository.findByRobotId(reqDTO.getRobotId());
        if (robotConfig == null) {
            RobotFirmwareVersion latestVersion = robotFirmwareVersionRepository.getLatestFirmware();
            robotConfig = robotConfigRepository.save(RobotConfig.builder()
                    .firmwareVersion(latestVersion.getVersion())
                    .robotId(reqDTO.getRobotId())
                    .volume(reqDTO.getValue())
                    .build());
        } else {
            robotConfig.setVolume(reqDTO.getValue());
        }

        robotConfigRepository.save(robotConfig);
        return new DataResponseDTO<>(CodeDefine.OK, "Lưu setting volume thành công");
    }

    public DataResponseDTO<?> saveRobotScreenBrightnessSetting(RobotVolumeReqDTO reqDTO) {
        RobotConfig robotConfig = robotConfigRepository.findByRobotId(reqDTO.getRobotId());
        if (robotConfig == null) {
            RobotFirmwareVersion latestVersion = robotFirmwareVersionRepository.getLatestFirmware();
            robotConfig = robotConfigRepository.save(RobotConfig.builder()
                    .firmwareVersion(latestVersion.getVersion())
                    .robotId(reqDTO.getRobotId())
                    .screenBrightness(reqDTO.getValue())
                    .build());
        } else {
            robotConfig.setScreenBrightness(reqDTO.getValue());
        }

        robotConfigRepository.save(robotConfig);
        return new DataResponseDTO<>(CodeDefine.OK, "Lưu setting độ sáng thành công");
    }
}
