package com.stepup.springrobot.service.communication;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.auth.AccountInfoDTO;
import com.stepup.springrobot.dto.setting.ChangeSettingReqDTO;
import com.stepup.springrobot.dto.setting.SettingDataDTO;
import com.stepup.springrobot.exception.business.request.InvalidFormatException;
import com.stepup.springrobot.model.mqtt.MqttMessageType;
import com.stepup.springrobot.model.robot.RobotConfig;
import com.stepup.springrobot.model.robot.RobotFirmwareVersion;
import com.stepup.springrobot.model.robot.RobotUser;
import com.stepup.springrobot.model.robot.ScreenBackgroundType;
import com.stepup.springrobot.repository.game.RobotFirmwareVersionRepository;
import com.stepup.springrobot.repository.robot.RobotConfigRepository;
import com.stepup.springrobot.repository.robot.RobotUserRepository;
import com.stepup.springrobot.security.JwtService;
import com.stepup.springrobot.service.CommonService;
import com.stepup.springrobot.service.MqttService;
import com.stepup.springrobot.service.SlackWarningSystemService;
import com.stepup.springrobot.service.UploadFileToS3;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

@Log4j2
@Service
public class SettingService extends CommonService {
    @Autowired
    private RobotFirmwareVersionRepository robotFirmwareVersionRepository;

    @Autowired
    private RobotUserRepository robotUserRepository;

    @Autowired
    private RobotConfigRepository robotConfigRepository;

    @Autowired
    private MqttService mqttService;

    @Autowired
    private ObjectMapper objectMapper;

    protected SettingService(ObjectMapper objectMapper, UploadFileToS3 uploadFileToS3, JwtService jwtService, SlackWarningSystemService slackWarningSystemService) {
        super(objectMapper, uploadFileToS3, jwtService, slackWarningSystemService);
    }

    public DataResponseDTO<?> getSettingData(HttpServletRequest request, MqttMessageType type) throws IOException {
        AccountInfoDTO userDataDTO = getAccountInfo(request, null, false);
        String userId = userDataDTO.getUserData().getUserId();
        RobotUser robotUser = robotUserRepository.findFirstByUserIdOrderByIdDesc(userId);
        if (robotUser == null) {
            throw new InvalidFormatException("Tài khoản của bạn chưa được tích hợp với thiết bị robot");
        }

        RobotConfig robotConfig = robotConfigRepository.findByRobotId(robotUser.getRobotId());
        if (robotConfig == null) {
            RobotFirmwareVersion latestVersion = robotFirmwareVersionRepository.getLatestFirmware();
            robotConfig = robotConfigRepository.save(RobotConfig.builder()
                    .firmwareVersion(latestVersion.getVersion())
                    .robotId(robotUser.getRobotId())
                    .screenBackground(ScreenBackgroundType.BLACK)
                    .build());
        }

        SettingDataDTO settingDataDTO = SettingDataDTO.builder()
                .type(type)
                .build();
        ObjectNode data = JsonNodeFactory.instance.objectNode();
        if (type == MqttMessageType.VOLUME) {
            data.put("value", robotConfig.getVolume());
            ObjectNode range = JsonNodeFactory.instance.objectNode();
            range.put("min", 1);
            range.put("max", 100);
            data.set("range", range);
        } else if (type == MqttMessageType.SCREEN_BRIGHTNESS) {
            data.put("value", robotConfig.getScreenBrightness());
            ObjectNode range = JsonNodeFactory.instance.objectNode();
            range.put("min", 1);
            range.put("max", 100);
            data.set("range", range);
        } else if (type == MqttMessageType.SCREEN_BACKGROUND) {
            data.put("value", robotConfig.getScreenBackground().getFeature());
            ArrayNode options = JsonNodeFactory.instance.arrayNode();
            options.add("BLACK");
            options.add("DARK_BLUE");
            data.set("options", options);
        }

        settingDataDTO.setData(data);

        return new DataResponseDTO<>(CodeDefine.OK, "Lấy dữ liệu setting thành công", settingDataDTO);
    }

    public DataResponseDTO<?> updateSettingData(HttpServletRequest request, ChangeSettingReqDTO reqDTO) throws IOException {
        AccountInfoDTO userDataDTO = getAccountInfo(request, null, false);
        String userId = userDataDTO.getUserData().getUserId();
        RobotUser robotUser = robotUserRepository.findFirstByUserIdOrderByIdDesc(userId);
        if (robotUser == null) {
            throw new InvalidFormatException("Tài khoản của bạn chưa được tích hợp với thiết bị robot");
        }

        RobotConfig robotConfig = robotConfigRepository.findByRobotId(robotUser.getRobotId());
        if (robotConfig == null) {
            RobotFirmwareVersion latestVersion = robotFirmwareVersionRepository.getLatestFirmware();
            robotConfig = robotConfigRepository.save(RobotConfig.builder()
                    .firmwareVersion(latestVersion.getVersion())
                    .robotId(robotUser.getRobotId())
                    .screenBackground(ScreenBackgroundType.BLACK)
                    .build());
        }

        Object value = reqDTO.getValue();
        MqttMessageType type = reqDTO.getType();
        if (type == MqttMessageType.VOLUME) {
            if (value instanceof Integer) {
                robotConfig.setVolume((int) value);
            } else {
                throw new InvalidFormatException("Sai định dạng giá trị volume");
            }
        } else if (type == MqttMessageType.SCREEN_BRIGHTNESS) {
            if (value instanceof Integer) {
                robotConfig.setScreenBrightness((int) value);
            } else {
                throw new InvalidFormatException("Sai định dạng giá trị độ sáng");
            }
        } else if (type == MqttMessageType.SCREEN_BACKGROUND) {
            ScreenBackgroundType backgroundType = ScreenBackgroundType.from(value.toString());
            if (backgroundType == null) {
                throw new InvalidFormatException("Không tồn tại background " + value);
            }

            robotConfig.setScreenBackground(backgroundType);
        }

        robotConfigRepository.save(robotConfig);

        return new DataResponseDTO<>(CodeDefine.OK, "Cập nhật dữ liệu setting thành công");
    }
}
