package com.stepup.springrobot.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.common.DateUtils;
import com.stepup.springrobot.config.ConfigUtil;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.chat.*;
import com.stepup.springrobot.dto.ielts.*;
import com.stepup.springrobot.dto.llm.LlmInitConversationResDTO;
import com.stepup.springrobot.dto.llm.LlmTextDTO;
import com.stepup.springrobot.dto.llm_conversation.LLMChatResDTO;
import com.stepup.springrobot.model.CommonConfig;
import com.stepup.springrobot.model.InternalTextToSpeechVoice;
import com.stepup.springrobot.model.chat.*;
import com.stepup.springrobot.repository.CommonConfigRepository;
import com.stepup.springrobot.repository.chat.*;
import com.stepup.springrobot.security.JwtService;
import io.sentry.Sentry;
import lombok.extern.log4j.Log4j2;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.server.ResponseStatusException;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Log4j2
@Service
public class AIRobotConversationService extends CommonService {
    @Value("${tts_speed}")
    private Double ttsSpeed;

    @Value("${ielts_conversation_host}")
    private String ieltsConversationHost;

    @Value("${ielts_conversation_user_id}")
    private String ieltsConversationUserId;

    @Value("${ielts_conversation_lesson_id}")
    private String ieltsConversationLessonId;

    @Value("${web_mvp_host}")
    private String webMvpHost;

    @Value("${is_use_llm_conversation}")
    private boolean isUseLLMConversation;

    @Value("${default_bot_ids}")
    private String defaultLLMBotIds;

    @Value("${fast_response_host}")
    private String fastResponseHost;

    @Value("${fast_response_uri}")
    private String fastResponseUri;

    @Value("${test_gifs}")
    private String testGifs;

    @Value("${test_bot_id}")
    private Long testBotId;

    @Value("${stress_test_robot_id}")
    private String stressTestRobotIds;

    private final Random random = new Random();

    @Autowired
    private RobotUserConversationRepository robotUserConversationRepository;

    @Autowired
    private RobotUserConversationRecordHistoryRepository aiUserConversationRecordHistoryRepository;

    @Autowired
    private RobotUserConversationLLMBotMapRepository robotUserConversationLLMBotMapRepository;

    @Autowired
    private ServoActionRepository servoActionRepository;

    @Autowired
    private ListeningEmotionRepository listeningEmotionRepository;

    @Autowired
    private RobotConversationSTTHandlerRepository robotConversationSTTHandlerRepository;

    @Autowired
    private RecognizeService recognizeService;

    @Autowired
    private SlackWarningSystemService slackWarningSystemService;

    @Autowired
    private LLMConversationService llmConversationService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private CommonConfigRepository commonConfigRepository;

    @Autowired
    private SharedService sharedService;

    private final ScheduledExecutorService quickService = Executors.newScheduledThreadPool(50); // Creates a thread pool that reuses fixed number of threads(as specified by noOfThreads in this case).

    protected AIRobotConversationService(ObjectMapper objectMapper, UploadFileToS3 uploadFileToS3, JwtService jwtService, SlackWarningSystemService slackWarningSystemService) {
        super(objectMapper, uploadFileToS3, jwtService, slackWarningSystemService);
    }

    private void uploadOnionUserFileToS3(File file, Integer today) {
        uploadFileToS3(CodeDefine.ONION_USER_MEDIA_IN_BUCKET_STORAGE_S3 + File.separator + today, file, null, "audio/wav");
        if (file.exists()) {
            file.delete();
        }
    }

    public RobotConversationMsgResDTO sendCustomUserSentence(String userId, String text, String socketSessionId, String ip, Long botId, StringBuilder logMessage, STTHandlerType asrType, String userAudio, boolean isWebMvp) {
        boolean isInit = false;
        try {
            RobotUserConversation aiUserConversation = findConversationBySocketSessionId(socketSessionId);

            if (aiUserConversation == null) {
                isInit = true;
                aiUserConversation = createConversation(userId, socketSessionId, ip, botId, asrType, isWebMvp);
            }

            RobotConversationMsgResDTO robotConversationMsgResDTO = sendUserMessage(userId, text, userAudio, aiUserConversation, logMessage, true);

            if (isInit) {
                List<String> gifs = aiUserConversation.getGifs() != null
                        ? objectMapper.readValue(aiUserConversation.getGifs(), new TypeReference<>() {
                })
                        : new ArrayList<>();
                if (aiUserConversation.getBotId().equals(testBotId) && CollectionUtils.isEmpty(gifs)) {
                    gifs = List.of(testGifs.split(","));
                }

                gifs = gifs.stream().filter(gif -> gif.endsWith(".gif")).collect(Collectors.toList());
                robotConversationMsgResDTO.setGifs(gifs);
            }

            List<AIRobotConversationResDTO> resDTOS = robotConversationMsgResDTO.getMessages();
            List<RobotResponseMessageDTO> responseMessageDTOS = resDTOS.stream()
                    .map(resDTO -> RobotResponseMessageDTO.builder()
                            .text(resDTO.getText())
                            .audio(resDTO.getMp3())
                            .emotion(resDTO.getEmotion())
                            .servo(resDTO.getServo())
                            .animations(resDTO.getAnimations())
                            .servoData(resDTO.getServoData())
                            .media(resDTO.getMedia())
                            .emotions(resDTO.getEmotions())
                            .build())
                    .collect(Collectors.toList());
            robotConversationMsgResDTO.setResponseMessages(responseMessageDTOS);

            return robotConversationMsgResDTO;
        } catch (Exception e) {
            log.error("Lỗi khi chat với AI: {}", e.getMessage(), e);
            slackWarningSystemService.sendWarningSystemToSlack("(Robot) - Lỗi khi chat với AI: " + e.getMessage(), ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
            return RobotConversationMsgResDTO.builder()
                    .messages(List.of(AIRobotConversationResDTO.builder()
                            .text("Hi, nice to meet you. How are you right now?")
                            .mp3("https://smedia.stepup.edu.vn/audio/onion/tts/en-US-Neural2-H/acb2c754e05c1b1b114a1db797fd341a.mp3?v=1720670155976")
                            .build()))
                    .gifs(isInit ? List.of(testGifs.split(",")) : null)
                    .build();
        }
    }

    public String getUserAudioUrl(String fileName) {
        return ConfigUtil.INSTANCE.getCdnDomain() + File.separator + CodeDefine.ONION_USER_MEDIA_IN_BUCKET_STORAGE_S3 + File.separator + DateUtils.getToday() + File.separator + fileName;
    }

    public void saveUserAnswer(String fileName, String socketSessionId, String text, String speechToTextResponse) {
        quickService.submit(() -> {
            RobotUserConversation aiUserConversation = findConversationBySocketSessionId(socketSessionId);
            if (aiUserConversation != null) {
                saveConversationRecord(AIConversationRecordHistoryDTO.builder()
                        .aiUserConversationId(aiUserConversation.getId())
                        .gptCharacter(GPTCharacter.user)
                        .character(AICharacter.USER)
                        .content(text)
                        .audio(getUserAudioUrl(fileName))
                        .voiceData(speechToTextResponse)
                        .build());
            }

        });
    }

    public RobotConversationMsgResDTO getRobotResponse(String userId, String text, File file, String socketSessionId, String speechToTextResponse, String ip, Long botId, StringBuilder logMessage, STTHandlerType asrType, boolean isNewLogic) {
        try {
            String fileName = file.getName();
            Instant start = Instant.now();
            uploadOnionUserFileToS3(file, DateUtils.getToday());
            logMessage.append(", upload audio: ").append(Duration.between(start, Instant.now()).toMillis()).append("ms");
            String userAudio = getUserAudioUrl(fileName);

            RobotUserConversation aiUserConversation = findConversationBySocketSessionId(socketSessionId);
            if (aiUserConversation == null) {
                aiUserConversation = createConversation(userId, socketSessionId, ip, botId, asrType, false);
            }

            RobotConversationMsgResDTO robotConversationMsgResDTO = sendUserMessage(userId, text, userAudio, aiUserConversation, logMessage, isNewLogic);
            if (aiUserConversation.getBotId().equals(testBotId)) {
                String data = "{\"text\":\"YAY! Tớ tên là Pika, và tớ đến từ sao Hỏa. Nhiệm vụ của tớ chính là kết bạn với người Trái Đất và cùng khám phá thế giới này! And you know what? Cậu là người đầu tiên tớ gặp sau khi hạ cánh. Cậu có thể trở thành người bạn Trái Đất đầu tiên của tớ không?\",\"audio\":\"http://tts-file-mgc-52.hacknao.edu.vn/data/tts_female_linh_v1/cf877183b0a29a4a9ba0285a3a5ecd3e_3_1.0.mp3\",\"mp3\":\"http://tts-file-mgc-52.hacknao.edu.vn/data/tts_female_linh_v1/cf877183b0a29a4a9ba0285a3a5ecd3e_3_1.0.mp3\",\"emotion\":\"HAPPY\",\"servo\":1,\"animations\":[],\"media\":null,\"emotions\":[{\"duration\":4100,\"type\":\"PLANNING\",\"text\":null,\"servo_data\":{\"parts\":[{\"steps\":[{\"time\":300,\"angle\":90}],\"part_type\":\"HEAD\",\"repetition\":2,\"start_time\":100},{\"steps\":[{\"time\":500,\"angle\":90}],\"part_type\":\"BASE\",\"repetition\":1,\"start_time\":300},{\"steps\":[{\"time\":500,\"angle\":60}],\"part_type\":\"LEFT_HAND\",\"repetition\":1,\"start_time\":100},{\"steps\":[{\"time\":500,\"angle\":60}],\"part_type\":\"RIGHT_HAND\",\"repetition\":1,\"start_time\":100}]}},{\"duration\":6800,\"type\":\"TEASING\",\"text\":null,\"servo_data\":{\"parts\":[{\"steps\":[{\"time\":300,\"angle\":90}],\"part_type\":\"HEAD\",\"repetition\":2,\"start_time\":100},{\"steps\":[{\"time\":500,\"angle\":90}],\"part_type\":\"BASE\",\"repetition\":1,\"start_time\":300},{\"steps\":[{\"time\":500,\"angle\":60}],\"part_type\":\"LEFT_HAND\",\"repetition\":1,\"start_time\":100},{\"steps\":[{\"time\":700,\"angle\":150},{\"time\":3000,\"angle\":150},{\"time\":500,\"angle\":60}],\"part_type\":\"RIGHT_HAND\",\"repetition\":1,\"start_time\":600}]}}],\"servo_data\":{\"parts\":[{\"steps\":[{\"time\":500,\"angle\":90}],\"part_type\":\"HEAD\",\"repetition\":2,\"start_time\":100},{\"steps\":[{\"time\":500,\"angle\":90}],\"part_type\":\"BASE\",\"repetition\":1,\"start_time\":300},{\"steps\":[{\"time\":500,\"angle\":150},{\"time\":1000,\"angle\":150},{\"time\":500,\"angle\":60}],\"part_type\":\"LEFT_HAND\",\"repetition\":1,\"start_time\":100},{\"steps\":[{\"time\":500,\"angle\":150},{\"time\":1000,\"angle\":150},{\"time\":500,\"angle\":60}],\"part_type\":\"RIGHT_HAND\",\"repetition\":1,\"start_time\":100}]}}";
                AIRobotConversationResDTO aiRobotConversationResDTO = objectMapper.readValue(data, AIRobotConversationResDTO.class);
                List<AIRobotConversationResDTO> messages = new ArrayList<>();
                messages.add(aiRobotConversationResDTO);
                messages.add(robotConversationMsgResDTO.getMessages().get(0));
                robotConversationMsgResDTO.setMessages(messages);
            } else if (Arrays.asList(stressTestRobotIds.split(",")).contains(userId)) {
                List<CommonConfig> commonConfig = commonConfigRepository.findAll();
                if (!CollectionUtils.isEmpty(commonConfig)) {
                    CommonConfig config = commonConfig.get(0);
                    if (!StringUtils.isEmpty(config.getStressTestResponse())) {
                        List<AIRobotConversationResDTO> aiRobotConversationResDTO = objectMapper.readValue(config.getStressTestResponse(), new TypeReference<>() {
                        });
                        List<AIRobotConversationResDTO> messages = new ArrayList<>();
                        messages.add(aiRobotConversationResDTO.get(random.nextInt(aiRobotConversationResDTO.size())));
                        robotConversationMsgResDTO.setMessages(messages);
                        robotConversationMsgResDTO.getMessages().forEach(message -> message.setStatus("CHAT"));
                    }
                }
            }

            List<AIRobotConversationResDTO> resDTOS = robotConversationMsgResDTO.getMessages();
            List<RobotResponseMessageDTO> responseMessageDTOS = resDTOS.stream()
                    .map(resDTO -> RobotResponseMessageDTO.builder()
                            .text(resDTO.getText())
                            .audio(resDTO.getMp3())
                            .emotion(resDTO.getEmotion())
                            .servo(resDTO.getServo())
                            .animations(resDTO.getAnimations())
                            .servoData(resDTO.getServoData())
                            .media(resDTO.getMedia())
                            .emotions(resDTO.getEmotions())
                            .build())
                    .collect(Collectors.toList());
            robotConversationMsgResDTO.setResponseMessages(responseMessageDTOS);

            return robotConversationMsgResDTO;
        } catch (Exception e) {
            log.error("Lỗi khi chat với AI: {}", e.getMessage(), e);
            slackWarningSystemService.sendWarningSystemToSlack("(Robot) - Lỗi khi chat với AI: " + e.getMessage(), ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
            List<String> gifs = List.of(testGifs.split(","));
            return RobotConversationMsgResDTO.builder()
                    .messages(List.of(AIRobotConversationResDTO.builder()
                            .text("Hi, nice to meet you. How are you right now?")
                            .mp3("https://smedia.stepup.edu.vn/audio/onion/tts/en-US-Neural2-H/acb2c754e05c1b1b114a1db797fd341a.mp3?v=1720670155976")
                            .media(MessageMediaDTO.builder()
                                    .url(gifs.get(random.nextInt(gifs.size())))
                                    .type(MediaType.IMAGE)
                                    .build())
                            .build()))
                    .build();
        }
    }

    private RobotUserConversation findConversationBySocketSessionId(String socketSessionId) {
        RMapCache<String, String> conversationMapCache = redissonClient.getMapCache(CodeDefine.REDIS_KEY_CONVERSATION_BY_SOCKET_SESSION);
        if (conversationMapCache.containsKey(socketSessionId)) {
            try {
                return objectMapper.readValue(conversationMapCache.get(socketSessionId), new TypeReference<>() {
                });
            } catch (Exception e) {
                log.error("Lỗi lấy conversation theo socketSession từ Redis: {}", e.getMessage());
                conversationMapCache.removeAsync(socketSessionId);
            }
        }

        RobotUserConversation aiUserConversation = robotUserConversationRepository.findBySocketSessionId(socketSessionId);
        if (aiUserConversation != null) {
            try {
                conversationMapCache.putAsync(socketSessionId, objectMapper.writeValueAsString(aiUserConversation), 30, TimeUnit.MINUTES);
            } catch (Exception e) {
                log.error("Lỗi lưu conversation theo socketSession vào Redis: {}", e.getMessage());
                conversationMapCache.removeAsync(socketSessionId);
            }
        }

        return aiUserConversation;
    }

    private RobotUserConversation createConversation(String userId, String socketSessionId, String ip, Long botId, STTHandlerType asrType, boolean isWebMvp) throws IOException {
        BotSourceType botType;

        if (botId == null) {
            List<RobotUserConversationLLMBotMap> robotUserConversationLLMBotMap = robotUserConversationLLMBotMapRepository.findByUserId(userId);
            if (!CollectionUtils.isEmpty(robotUserConversationLLMBotMap)) {
                List<Long> botIds = robotUserConversationLLMBotMap.stream().map(RobotUserConversationLLMBotMap::getBotId).collect(Collectors.toList());
                botId = botIds.get(random.nextInt(botIds.size()));
            } else {
                String[] botIds = defaultLLMBotIds.split(",");
                botId = Long.parseLong(botIds[random.nextInt(botIds.length)]);
            }
        }

        botType = BotSourceType.LLM;
        String thirdPartyConversationId = UUID.randomUUID() + "_" + userId;
        LlmInitConversationResDTO llmInitConversationResDTO = llmConversationService.initConversation(thirdPartyConversationId, userId, botId);
        RobotUserConversation robotUserConversation = robotUserConversationRepository.save(RobotUserConversation.builder()
                .userId(userId)
                .robotId(userId)
                .conversationType(ConversationType.AI_COACH)
                .socketSessionId(socketSessionId)
                .externalConversationId(thirdPartyConversationId)
                .botId(botId)
                .botType(botType)
                .ip(ip)
                .asrType(asrType)
                .gifs(objectMapper.writeValueAsString(llmInitConversationResDTO.getGifs()))
                .build());

        RMapCache<String, String> conversationMapCache = redissonClient.getMapCache(CodeDefine.REDIS_KEY_CONVERSATION_BY_SOCKET_SESSION);
        try {
            conversationMapCache.putAsync(socketSessionId, objectMapper.writeValueAsString(robotUserConversation), 30, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("Lỗi lưu conversation theo socketSession vào Redis: {}", e.getMessage());
            conversationMapCache.removeAsync(socketSessionId);
        }


        if (isWebMvp) {
            updateConversationPhone(robotUserConversation.getId(), userId, robotUserConversation.getSocketSessionId());
        }

        return robotUserConversation;
    }

    private void saveConversationRecord(AIConversationRecordHistoryDTO aiConversationRecordHistoryDTO) {
        RobotUserConversationRecordHistory aiUserConversationRecordHistory = RobotUserConversationRecordHistory.builder()
                .robotUserConversationId(aiConversationRecordHistoryDTO.getAiUserConversationId())
                .gptCharacter(aiConversationRecordHistoryDTO.getGptCharacter())
                .character(aiConversationRecordHistoryDTO.getCharacter())
                .content(aiConversationRecordHistoryDTO.getContent())
                .audio(aiConversationRecordHistoryDTO.getAudio())
                .isFinishMessage(aiConversationRecordHistoryDTO.getIsFinishMessage())
                .responseTime(aiConversationRecordHistoryDTO.getResponseTime())
                .voiceData(aiConversationRecordHistoryDTO.getVoiceData())
                .emotion(aiConversationRecordHistoryDTO.getEmotion())
                .image(aiConversationRecordHistoryDTO.getImage())
                .video(aiConversationRecordHistoryDTO.getVideo())
                .data(aiConversationRecordHistoryDTO.getData())
                .servoData(aiConversationRecordHistoryDTO.getServoData())
                .robotType(aiConversationRecordHistoryDTO.getRobotType())
                .emotions(aiConversationRecordHistoryDTO.getEmotions())
                .listeningAnimation(aiConversationRecordHistoryDTO.getListeningAnimation())
                .language(aiConversationRecordHistoryDTO.getLanguage())
                .textViewer(aiConversationRecordHistoryDTO.getTextViewer())
                .volume(aiConversationRecordHistoryDTO.getVolume())
                .build();

        aiUserConversationRecordHistoryRepository.save(aiUserConversationRecordHistory);
    }

    private String getBotAudio(String textToSpeech, Double voiceSpeed, Integer volume) {
        String botAudio = recognizeService.convertTextToSpeech(textToSpeech, InternalTextToSpeechVoice.getDefaultVoice(), "robot", "mp3", voiceSpeed, volume);
        if (botAudio == null) {
            botAudio = recognizeService.convertTextToSpeech(textToSpeech, InternalTextToSpeechVoice.getDefaultVoice(), "robot", "mp3", voiceSpeed, volume);
        }

        botAudio = botAudio.replace("https", "http");
        return botAudio;
    }

    private RobotConversationMsgResDTO sendUserMessage(String userId, String userMessage, String userAudio, RobotUserConversation aiUserConversation, StringBuilder logMessage, boolean isNewLogic) throws IOException {
        // Check kí tự cuối của user message
        userMessage = handleUserMessage(userMessage);

        boolean isFakeResponseImage = random.nextBoolean();
        boolean isFakeButton = isFakeResponseImage && random.nextBoolean();

        Long conversationId = aiUserConversation.getId();
        // Check limit bot response
        LLMResultDTO llmResponse = getLLMResponse(userMessage, userAudio, aiUserConversation, logMessage, isNewLogic);
        List<AIRobotConversationResDTO> messages = new ArrayList<>();

        for (int i = 0; i < llmResponse.getText().size(); i++) {
            LLMResultTextDTO textResDTO = llmResponse.getText().get(i);
            log.info("Test response=========: {}", objectMapper.writeValueAsString(llmResponse));
            String response;
            if (isFakeResponseImage && aiUserConversation.getBotId().equals(testBotId)) {
                if (isFakeButton) {
                    response = "Chơi tiếp nè, cậu lắng nghe tớ nhé: Monkey. Which one is the monkey? Nhanh lên nào! Bấm phím Left or right";
                } else {
                    response = "Chơi tiếp nè, cậu lắng nghe tớ nhé: Monkey. Which one is the monkey? Nhanh lên nào! Hãy nói Number 1 or Number 2";
                }
            } else {
                response = textResDTO.getResponse();
            }

            Instant start = Instant.now();        // export audio
            boolean isGenerateBotAudio = !StringUtils.isEmpty(textResDTO.getResponse());
            String botAudio = isGenerateBotAudio ? getBotAudio(response, textResDTO.getVoiceSpeed(), textResDTO.getVolume()) : null;

            logMessage.append(", tts: ").append(Duration.between(start, Instant.now()).toMillis()).append("ms");

            quickService.submit(() -> {
                try {
                    saveConversationRecord(AIConversationRecordHistoryDTO.builder()
                            .aiUserConversationId(conversationId)
                            .gptCharacter(GPTCharacter.assistant)
                            .character(AICharacter.BOT_RESPONSE_CONVERSATION)
                            .content(response)
                            .audio(isGenerateBotAudio ? botAudio : textResDTO.getAudio())
                            .image(textResDTO.getImage())
                            .video(textResDTO.getVideo())
                            .robotType(llmResponse.getRobotType())
                            .emotions(objectMapper.writeValueAsString(textResDTO.getEmotions()))
                            .listeningAnimation(objectMapper.writeValueAsString(llmResponse.getListeningAnimation()))
                            .language(llmResponse.getLanguage())
                            .textViewer(textResDTO.getTextViewer())
                            .volume(textResDTO.getVolume())
                            .build());
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            });

            start = Instant.now();
            List<WhisperAudioCMUDTO> animations = !isGenerateBotAudio
                    ? new ArrayList<>()
                    : getSpeechAnimationForSentence(AIConversationDetailMessageResDTO.builder()
                            .content(response)
                            .audio(botAudio)
                            .isLastSentence(true)
                            .build(),
                    userId, true);
            logMessage.append(", animations: ")
                    .append(Duration.between(start, Instant.now()).toMillis()).append("ms")
                    .append(", conversation_id: ")
                    .append(conversationId);

            MessageMediaDTO messageMediaDTO = null;
            if (!StringUtils.isEmpty(textResDTO.getImage())) {
                messageMediaDTO = MessageMediaDTO.builder()
                        .url(textResDTO.getImage())
                        .type(MediaType.IMAGE)
                        .build();
            } else if (!StringUtils.isEmpty(textResDTO.getVideo())) {
                messageMediaDTO = MessageMediaDTO.builder()
                        .url(textResDTO.getVideo())
                        .type(MediaType.VIDEO)
                        .build();
            }

            if (aiUserConversation.getBotId().equals(testBotId)) {
                if (isFakeResponseImage) {
                    messageMediaDTO = MessageMediaDTO.builder()
                            .url("https://smedia.stepup.edu.vn/robot/image/rabitormonkey.jpg")
                            .type(MediaType.IMAGE)
                            .build();
                } else {
                    List<String> gifs = List.of(testGifs.split(","));
                    messageMediaDTO = MessageMediaDTO.builder()
                            .url(gifs.get(random.nextInt(gifs.size())))
                            .type(MediaType.IMAGE)
                            .build();
                }
            }

            List<LlmMoodResDTO> llmMoodResDTOS = getEmotionArraysBySentence(textResDTO.getEmotions(), "HAPPY", textResDTO.getTextViewer());

            messages.add(AIRobotConversationResDTO.builder()
                    .text(response)
                    .mp3(isGenerateBotAudio ? botAudio : textResDTO.getAudio())
                    .animations(animations)
                    .status(llmResponse.getStatus())
                    .media(messageMediaDTO)
                    .conversationId(conversationId)
                    .emotions(objectMapper.valueToTree(llmMoodResDTOS))
                    .build());
        }

        RobotListeningEmotionDTO listeningEmotionDTO = !Objects.equals(llmResponse.getStatus(), "ACTION") && llmResponse.getListeningAnimation() != null
                ? RobotListeningEmotionDTO.builder()
                .emotion(llmResponse.getListeningAnimation().getMoodName())
                .servoData(getListeningEmotionByEmotion(llmResponse.getListeningAnimation().getServoName()))
                .build() : null;
        ListeningMediaDTO listeningMediaDTO;
        if (!StringUtils.isEmpty(llmResponse.getListeningAudio()) || !StringUtils.isEmpty(llmResponse.getListeningImage())) {
            listeningMediaDTO = ListeningMediaDTO.builder()
                    .audio(StringUtils.isEmpty(llmResponse.getListeningAudio()) ? null : llmResponse.getListeningAudio())
                    .image(StringUtils.isEmpty(llmResponse.getListeningImage()) ? null : llmResponse.getListeningImage())
                    .build();
            if (listeningEmotionDTO == null) {
                listeningEmotionDTO = new RobotListeningEmotionDTO();
            }

            listeningEmotionDTO.setMedia(listeningMediaDTO);
        }

        AnswerModeType answerModeType = llmResponse.getAnswerMode() != null ? llmResponse.getAnswerMode() : AnswerModeType.RECORDING;
        if (aiUserConversation.getBotId().equals(testBotId) && isFakeResponseImage) {
            if (listeningEmotionDTO == null) {
                listeningEmotionDTO = RobotListeningEmotionDTO.builder()
                        .emotion("HAPPY")
                        .servoData(getListeningEmotionByEmotion("HAPPY"))
                        .build();
            }

            if (isFakeButton) {
                answerModeType = AnswerModeType.BUTTON_2;
                listeningEmotionDTO.setMedia(ListeningMediaDTO.builder()
                        .audio("https://tts-file-mgc-42.hacknao.edu.vn/data/tts_female_linh_v1/4edbc97ecd371d801d648069aa195641_3_1.mp3")
                        .image("https://smedia.stepup.edu.vn/robot/image/rabitormonkey.jpg")
                        .build());
            } else {
                listeningEmotionDTO.setMedia(ListeningMediaDTO.builder()
                        .audio("https://smedia.stepup.edu.vn/robot/upload/background2_cut.mp3")
                        .image("https://smedia.stepup.edu.vn/robot/upload/gif/listening_gif.gif")
                        .build());
            }
        }

        return RobotConversationMsgResDTO.builder()
                .messages(messages)
                .listeningEmotion(listeningEmotionDTO)
                .language(llmResponse.getLanguage())
                .answerMode(answerModeType)
                .build();
    }

    private JsonNode getListeningEmotionByEmotion(String emotionType) {
        List<ListeningEmotion> allServoActions = listeningEmotionRepository.findByType(emotionType);
        if (!CollectionUtils.isEmpty(allServoActions)) {
            ListeningEmotion listeningEmotion = allServoActions.get(random.nextInt(allServoActions.size()));
            return listeningEmotion.getData();
        } else {
            slackWarningSystemService.sendWarningSystemToSlack("Không tìm thấy listening emotion cho type: " + emotionType, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
            return null;
        }
    }

    private JsonNode getServoDataByEmotion(String emotionType) {
        List<ServoAction> allServoActions = new ArrayList<>();
        RMapCache<String, String> servoMapCache = redissonClient.getMapCache(CodeDefine.REDIS_KEY_SERVO_BY_EMOTION);
        if (servoMapCache.containsKey(emotionType)) {
            try {
                allServoActions = objectMapper.readValue(servoMapCache.get(emotionType), new TypeReference<>() {
                });
            } catch (Exception e) {
                log.error("Lỗi lấy servo animations từ Redis: {}", e.getMessage());
                servoMapCache.removeAsync(emotionType);
            }
        }

        if (CollectionUtils.isEmpty(allServoActions)) {
            allServoActions = servoActionRepository.findByType(emotionType);
            if (!CollectionUtils.isEmpty(allServoActions)) {
                try {
                    servoMapCache.putAsync(emotionType, objectMapper.writeValueAsString(allServoActions), 365, TimeUnit.DAYS);
                } catch (Exception e) {
                    log.error("Lỗi lưu servo animations vào Redis: {}", e.getMessage());
                    servoMapCache.removeAsync(emotionType);
                }
            }
        }

        if (!CollectionUtils.isEmpty(allServoActions)) {
            ServoAction servoAction = allServoActions.get(random.nextInt(allServoActions.size()));
            return servoAction.getData();
        } else {
            slackWarningSystemService.sendWarningSystemToSlack("Không tìm thấy servo data cho emotion: " + emotionType, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
            return null;
        }
    }

    private String handleTextToSpeechInput(String input) {
        // remove ' in response
        return input.replace("'", "");
    }

    private LLMResultDTO getLLMResponse(String userMessage, String userAudio, RobotUserConversation aiUserConversation, StringBuilder logMessage, boolean isNewLogic) throws IOException {
        Instant start = Instant.now();
        LLMChatResDTO llmChatResDTO;
        LLMChatResDTO frisLllmChatResDTO;
        try {
            frisLllmChatResDTO = llmConversationService.getConversationResponse(aiUserConversation.getId(), aiUserConversation.getExternalConversationId(), userMessage, userAudio);
            logMessage.append(", bot LLM 1: ").append(Duration.between(start, Instant.now()).toMillis()).append("ms");
        } catch (Exception e) {
            frisLllmChatResDTO = llmConversationService.getConversationResponse(aiUserConversation.getId(), aiUserConversation.getExternalConversationId(), userMessage, userAudio);
            logMessage.append(", bot LLM 2: ").append(Duration.between(start, Instant.now()).toMillis()).append("ms");
        }

        llmChatResDTO = objectMapper.readValue(objectMapper.writeValueAsString(frisLllmChatResDTO), new TypeReference<>() {
        });

        if (Objects.equals(frisLllmChatResDTO.getStatus(), "ACTION")) {
            if (!isNewLogic) {
                LLMChatResDTO nextLLMChatResDTO;
                start = Instant.now();
                try {
                    nextLLMChatResDTO = llmConversationService.getConversationResponse(aiUserConversation.getId(), aiUserConversation.getExternalConversationId(), "ACTION", userAudio);
                    logMessage.append(", bot LLM 1: ").append(Duration.between(start, Instant.now()).toMillis()).append("ms");
                } catch (Exception e) {
                    nextLLMChatResDTO = llmConversationService.getConversationResponse(aiUserConversation.getId(), aiUserConversation.getExternalConversationId(), "ACTION", userAudio);
                    logMessage.append(", bot LLM 2: ").append(Duration.between(start, Instant.now()).toMillis()).append("ms");
                }

                llmChatResDTO = objectMapper.readValue(objectMapper.writeValueAsString(nextLLMChatResDTO), new TypeReference<>() {
                });
                List<LlmTextDTO> text = frisLllmChatResDTO.getText();
                text.addAll(nextLLMChatResDTO.getText());
                llmChatResDTO.setText(text);
            }
        }

        List<LLMResultTextDTO> llmResultTextDTOS = new ArrayList<>();
        for (LlmTextDTO textDTO : llmChatResDTO.getText()) {
            if (StringUtils.isEmpty(textDTO.getText()) && StringUtils.isEmpty(textDTO.getAudio())) {
                log.error("Link ảnh hoặc link audio không được trống, conversation_id,: {}, response: {}", aiUserConversation.getId(), objectMapper.writeValueAsString(textDTO));
                slackWarningSystemService.sendWarningSystemToSlack("Link ảnh hoặc link audio không được trống, robot_id,: " + aiUserConversation.getUserId() + ",bot_id: " + aiUserConversation.getBotId() + ", msg: " + objectMapper.writeValueAsString(textDTO), ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
                throw new RuntimeException("Link ảnh hoặc link audio không được trống, bot_id: " + aiUserConversation.getBotId() + ", response:" + objectMapper.writeValueAsString(textDTO));
            }

            String image;
            if (!StringUtils.isEmpty(textDTO.getImage())) {
                image = textDTO.getImage();
                if (!image.startsWith("http")) {
                    log.error("Link ảnh không hợp lệ, conversation_id,: {}, response: {}", aiUserConversation.getId(), objectMapper.writeValueAsString(textDTO));
                    slackWarningSystemService.sendWarningSystemToSlack("Link ảnh không hợp lệ, robot_id,: " + aiUserConversation.getUserId() + ",bot_id: " + aiUserConversation.getBotId() + ", url: " + image, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
                    image = null;
                }
            } else {
                image = null;
            }

            String video;
            if (!StringUtils.isEmpty(textDTO.getVideo())) {
                video = textDTO.getVideo();
                if (!video.startsWith("http")) {
                    log.error("Link video không hợp lệ, conversation_id,: {}, response: {}", aiUserConversation.getId(), objectMapper.writeValueAsString(textDTO));
                    slackWarningSystemService.sendWarningSystemToSlack("Link video không hợp lệ, robot_id,: " + aiUserConversation.getUserId() + ", bot_id: " + aiUserConversation.getBotId() + ", url: " + video, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
                    video = null;
                }
            } else {
                video = null;
            }

            Double voiceSpeed = textDTO.getVoiceSpeed() != null ? textDTO.getVoiceSpeed() : ttsSpeed;

            List<LlmMoodDetailDTO> moods = textDTO.getMoods();

            String textViewer = StringUtils.isEmpty(textDTO.getTextViewer()) ? null : textDTO.getTextViewer();
            if (!CollectionUtils.isEmpty(moods) && (textViewer == null || aiUserConversation.getPhone() == null)) {
                moods.removeIf(llmMoodDetailDTO -> llmMoodDetailDTO.getMoodName().equalsIgnoreCase("TEXT"));
            }

            llmResultTextDTOS.add(LLMResultTextDTO.builder()
                    .response(textDTO.getText())
                    .audio(textDTO.getAudio())
                    .emotions(moods)
                    .image(image)
                    .video(video)
                    .voiceSpeed(voiceSpeed)
                    .textViewer(textViewer)
                    .volume(textDTO.getVolume())
                    .build());
        }

        String language = !StringUtils.isEmpty(llmChatResDTO.getLanguage()) && (llmChatResDTO.getLanguage().equalsIgnoreCase("vi") || llmChatResDTO.getLanguage().equalsIgnoreCase("en"))
                ? llmChatResDTO.getLanguage()
                : null;

        return LLMResultDTO.builder()
                .text(llmResultTextDTOS)
                .status(llmChatResDTO.getStatus())
                .robotType(llmChatResDTO.getRobotType())
                .listeningAnimation(CollectionUtils.isEmpty(llmChatResDTO.getListeningAnimations()) ? null : llmChatResDTO.getListeningAnimations().get(0))
                .language(language)
                .answerMode(llmChatResDTO.getAnswerMode())
                .listeningAudio(llmChatResDTO.getAudioListening())
                .listeningImage(llmChatResDTO.getImageListening())
                .build();
    }

    private String getIeltsResponse(RobotUserConversation aiUserConversation, String userMessage) {
        ChatResDTO chatResDTO;
        if (aiUserConversation.getExternalConversationId() == null) { // Chưa khởi tạo hội thoại
            chatResDTO = initIeltsConversation(ieltsConversationUserId, ieltsConversationLessonId);
            aiUserConversation.setExternalConversationId(chatResDTO.getSessionId());
            robotUserConversationRepository.save(aiUserConversation);
        } else {
            chatResDTO = getIeltsConversationResponse(ieltsConversationUserId, aiUserConversation.getExternalConversationId(), userMessage);
        }

        return chatResDTO.getMessages().stream()
                .filter(e -> e.getMessageType().equals("text"))
                .map(ChatMessageResDTO::getMessage)
                .map(Object::toString)
                .collect(Collectors.joining(" "));
    }

    /**
     * Check last character of user message is "." or "?". If not, add "." to end of user message
     */
    private String handleUserMessage(String userMessage) {
        if (!StringUtils.isEmpty(userMessage)) {
            String lastCharacter = userMessage.substring(userMessage.length() - 1);
            if (!lastCharacter.equals(".") && !lastCharacter.equals("?")) {
                userMessage = userMessage + ".";
            }
        }

        return userMessage;
    }

    public DataResponseDTO<?> test(HttpServletRequest request, AIRobotConversationReqDTO reqDTO) {
        // Hexadecimal string (sample, replace with your actual hex string)
        String base64String = reqDTO.getVoiceData();

        // Specify the path to save the decoded audio file
        String filePath = "decoded_audio.wav";

        convertFileFromBase64(base64String, filePath);

        return new DataResponseDTO<>(CodeDefine.OK, "Thành công");
    }

    private File convertFileFromBase64(String base64String, String fileName) {
        byte[] audioBytes = Base64.getDecoder().decode(base64String);
        File file = new File(fileName);
        try (FileOutputStream fos = new FileOutputStream(file)) {
            fos.write(audioBytes);
        } catch (IOException e) {
            e.printStackTrace();
        }

        return file;
    }

    public DataResponseDTO<?> getAnimationsData(AIConversationDetailMessageResDTO message, Boolean isConvertWavFile) {
        return new DataResponseDTO<>(CodeDefine.OK, "Thành công", getSpeechAnimationForSentence(message, "1L", isConvertWavFile));
    }

    private ChatResDTO initIeltsConversation(String userId, String lessonId) {
        try {
            OkHttpClient client = new OkHttpClient.Builder()
                    .connectTimeout(20, TimeUnit.SECONDS)
                    .writeTimeout(20, TimeUnit.SECONDS)
                    .readTimeout(20, TimeUnit.SECONDS)
                    .build();

            InitSessionReqDTO chatReqDTO = InitSessionReqDTO.builder()
                    .userId(userId)
                    .lessonId(lessonId)
                    .build();

            Request request = new Request.Builder()
                    .url(ieltsConversationHost + "/iesv/api/v1/open/ielts/sessions")
                    .post(RequestBody.create(okhttp3.MediaType.parse("application/json"), objectMapper.writeValueAsString(chatReqDTO)))
                    .build();

            Response response = client.newCall(request).execute();
            DataResponseDTO<ChatResDTO> chatResDTO = objectMapper.readValue(response.body().string(), new TypeReference<>() {
            });
            log.info("======= Ielts response: {}", objectMapper.writeValueAsString(chatResDTO));
            return chatResDTO.getData();
        } catch (Exception e) {
            String msgSlack = "Lỗi khởi tạo ielts conversation:  " + ieltsConversationHost + "/iesv/api/v1/open/ielts/sessions" + " error: " + e.getMessage();
            slackWarningSystemService.sendWarningSystemToSlack(msgSlack, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
            Sentry.captureException(e);
            return null;
        }
    }

    private ChatResDTO getIeltsConversationResponse(String userId, String sessionId, String userMessage) {
        try {
            OkHttpClient client = new OkHttpClient.Builder()
                    .connectTimeout(20, TimeUnit.SECONDS)
                    .writeTimeout(20, TimeUnit.SECONDS)
                    .readTimeout(20, TimeUnit.SECONDS)
                    .build();

            ChatReqDTO chatReqDTO = ChatReqDTO.builder()
                    .userId(userId)
                    .sessionId(sessionId)
                    .data(ChatMessageReqDTO.builder()
                            .messageType("text")
                            .userMessage(userMessage)
                            .build())
                    .build();

            Request request = new Request.Builder()
                    .url(ieltsConversationHost + "/iesv/api/v1/open/ielts/conversations/text-messages")
                    .post(RequestBody.create(okhttp3.MediaType.parse("application/json"), objectMapper.writeValueAsString(chatReqDTO)))
                    .build();

            Response response = client.newCall(request).execute();
            DataResponseDTO<ChatResDTO> chatResDTO = objectMapper.readValue(response.body().string(), new TypeReference<>() {
            });
            log.info("======= Ielts response: {}", objectMapper.writeValueAsString(chatResDTO));
            return chatResDTO.getData();
        } catch (Exception e) {
            String msgSlack = "Lỗi lấy phản hồi ielts conversation:  " + ieltsConversationHost + "/iesv/api/v1/open/ielts/conversations/text-messages" + " error: " + e.getMessage();
            slackWarningSystemService.sendWarningSystemToSlack(msgSlack, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
            Sentry.captureException(e);
            return null;
        }
    }

    public List<RobotResponseMessageDTO> getStallingMessage(String sessionId, String userAnswer) {
        // Get random from stallingDatas
        AIRobotConversationResDTO stallingData = handleGetFastResponse(sessionId, userAnswer);
        if (stallingData == null) {
            stallingData = getFastResponseBackup();
        }

        List<AIRobotConversationResDTO> resDTOS = List.of(stallingData);
        return resDTOS.stream()
                .map(resDTO -> RobotResponseMessageDTO.builder()
                        .text(resDTO.getText())
                        .audio(resDTO.getMp3())
                        .servo(resDTO.getServo())
                        .animations(getAnimationForStallingMessage(resDTO.getText(), resDTO.getMp3()))
                        .conversationId(resDTO.getConversationId())
                        .build())
                .collect(Collectors.toList());
    }

    private AIRobotConversationResDTO handleGetFastResponse(String sessionId, String answer) {
        RobotUserConversation aiUserConversation = findConversationBySocketSessionId(sessionId);
        if (aiUserConversation == null) {
            return null;
        }

        RobotUserConversationRecordHistory recordHistory = aiUserConversationRecordHistoryRepository.findFirstByRobotUserConversationIdAndCharacterOrderByIdDesc(aiUserConversation.getId(), AICharacter.BOT_RESPONSE_CONVERSATION);
        if (recordHistory == null) {
            return null;
        }

        FastResponseReqDTO chatReqDTO = FastResponseReqDTO.builder()
                .robot(recordHistory.getContent())
                .userAnswer(answer)
                .robotType(recordHistory.getRobotType())
                .build();
        FastResponseResDTO fastResponseResDTO = getFastResponseFromLLM(aiUserConversation.getId(), chatReqDTO);
        String text;
        if (fastResponseResDTO == null) {
            List<String> fastResponses = List.of("That's so cool", "Great, let me process it!", "Great effort! Let’s see!", "Let me check!", "Let me take a look!");
            text = fastResponses.get(random.nextInt(fastResponses.size()));
        } else {
            text = fastResponseResDTO.getFastResponse();
        }

        String audio = getBotAudio(text, ttsSpeed, null);
        saveFastResponseRequest(aiUserConversation.getId(), text, audio, chatReqDTO, fastResponseResDTO); // saveFastResponseRequest
        return AIRobotConversationResDTO.builder()
                .text(text)
                .mp3(audio)
                .conversationId(aiUserConversation.getId())
                .build();
    }

    private void saveFastResponseRequest(Long conversationId, String text, String audio, FastResponseReqDTO chatReqDTO, FastResponseResDTO fastResponseResDTO) {
        quickService.submit(() -> {
            ObjectNode apiData = JsonNodeFactory.instance.objectNode();
            apiData.set("input", objectMapper.valueToTree(chatReqDTO));
            apiData.set("output", objectMapper.valueToTree(fastResponseResDTO));

            try {
                saveConversationRecord(AIConversationRecordHistoryDTO.builder()
                        .aiUserConversationId(conversationId)
                        .gptCharacter(GPTCharacter.assistant)
                        .character(AICharacter.FAST_RESPONSE)
                        .content(text)
                        .audio(audio)
                        .data(objectMapper.writeValueAsString(apiData))
                        .build());
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        });
    }

    private AIRobotConversationResDTO getFastResponseBackup() {
        List<AIRobotConversationResDTO> stallingDatas = new ArrayList<>();
        stallingDatas.add(AIRobotConversationResDTO.builder()
                .text("Hmm~ câu trả lời thú vị đấy")
                .mp3("http://tts-file-mgc-42.hacknao.edu.vn/data/tts_female_linh_v1/c820d4ee647f79240b0bfe144852bc2c_3_1.mp3")
                .build());
        stallingDatas.add(AIRobotConversationResDTO.builder()
                .text("Wow, thú vị quá")
                .mp3("http://tts-file-mgc-42.hacknao.edu.vn/data/tts_female_linh_v1/0cf06b81adbd24e7c481aaa34fc38321_3_1.mp3")
                .build());
        stallingDatas.add(AIRobotConversationResDTO.builder()
                .text("Ồ thật vậy sao")
                .mp3("http://tts-file-mgc-42.hacknao.edu.vn/data/tts_female_linh_v1/a92f0cbf187a27ecc5815a64b5c4eb6c_3_1.mp3")
                .build());

        // Get random from stallingDatas
        return stallingDatas.get(random.nextInt(stallingDatas.size()));
    }

    private FastResponseResDTO getFastResponseFromLLM(Long conversationId, FastResponseReqDTO chatReqDTO) {
        try {
            Instant start = Instant.now();
            OkHttpClient client = new OkHttpClient.Builder()
                    .connectTimeout(20, TimeUnit.SECONDS)
                    .writeTimeout(20, TimeUnit.SECONDS)
                    .readTimeout(20, TimeUnit.SECONDS)
                    .build();

            Request request = new Request.Builder()
                    .url(fastResponseHost + fastResponseUri)
                    .post(RequestBody.create(okhttp3.MediaType.parse("application/json"), objectMapper.writeValueAsString(chatReqDTO)))
                    .build();

            Response response = client.newCall(request).execute();
            FastResponseResDTO chatResDTO = objectMapper.readValue(response.body().string(), new TypeReference<>() {
            });

            ObjectNode data = JsonNodeFactory.instance.objectNode();
            data.set("request", objectMapper.valueToTree(chatReqDTO));
            data.set("response", objectMapper.valueToTree(chatResDTO));
            sharedService.saveConversationLog(conversationId, ConversationLogType.FAST_RESPONSE, objectMapper.writeValueAsString(data), Duration.between(start, Instant.now()).toMillis());
            log.info("======= Fast response - time: {} ms, request: {}, response: {}", Duration.between(start, Instant.now()).toMillis(), objectMapper.writeValueAsString(chatReqDTO), objectMapper.writeValueAsString(chatResDTO));
            return chatResDTO;
        } catch (Exception e) {
            String msgSlack = "Lỗi lấy phản hồi fast response:  " + fastResponseHost + fastResponseUri + " error: " + e.getMessage();
            slackWarningSystemService.sendWarningSystemToSlack(msgSlack, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
            Sentry.captureException(e);
            return null;
        }
    }

    private List<WhisperAudioCMUDTO> getAnimationForStallingMessage(String text, String audio) {
        RMapCache<String, String> lessonDetailMapCache = redissonClient.getMapCache("stalling_sentence_animations");
        List<WhisperAudioCMUDTO> animations;
        if (lessonDetailMapCache.containsKey(text)) {
            try {
                return objectMapper.readValue(lessonDetailMapCache.get(text), new TypeReference<>() {
                });
            } catch (Exception e) {
                log.error("Lỗi lấy animations từ Redis: {}", e.getMessage());
                lessonDetailMapCache.removeAsync(text);
            }
        }

        animations = getSpeechAnimationForSentence(AIConversationDetailMessageResDTO.builder()
                        .content(text)
                        .audio(audio)
                        .isLastSentence(true)
                        .build(),
                "stalling_user", true);

        try {
            lessonDetailMapCache.putAsync(text, objectMapper.writeValueAsString(animations), 365, TimeUnit.DAYS);
        } catch (Exception e) {
            log.error("Lỗi lưu animations vào Redis: {}", e.getMessage());
            lessonDetailMapCache.removeAsync(text);
        }

        return animations;
    }

    public DataResponseDTO<?> saveDataReportConversation(ReportDataConversationDTO dataReport) {
        RobotUserConversationRecordHistory robotUserConversationRecordHistory = aiUserConversationRecordHistoryRepository.findById(dataReport.getMessageId()).orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Message not found"));
        robotUserConversationRecordHistory.setDataReport(dataReport.getMessage());
        aiUserConversationRecordHistoryRepository.save(robotUserConversationRecordHistory);
        return new DataResponseDTO<>(CodeDefine.OK, "Gửi report thành công", null);
    }

    public Page<RobotUserConversationRecordHistory> findAllByDataReportNotNull(PageRequest pageRequest) {
        return aiUserConversationRecordHistoryRepository.findAllByDataReportNotNull(pageRequest);
    }

    public RobotConversationSTTHandler getTextToSpeechHandlerByType(STTHandlerType sttHandlerType) {
        RobotConversationSTTHandler robotConversationSTTHandler = robotConversationSTTHandlerRepository.findByAsrType(sttHandlerType);
        if (robotConversationSTTHandler == null) {
            slackWarningSystemService.sendWarningSystemToSlack("Không tìm thấy data model ASR: " + sttHandlerType, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
            return null;
        }

        return robotConversationSTTHandler;
    }

    public DeepGramModelDTO getDeepGramModelInfo() {
        RobotConversationSTTHandler robotConversationSTTHandler = getTextToSpeechHandlerByType(STTHandlerType.DEEP_GRAM);
        if (robotConversationSTTHandler == null || robotConversationSTTHandler.getData() == null) {
            return null;
        }

        return objectMapper.convertValue(robotConversationSTTHandler.getData(), new TypeReference<>() {
        });
    }

    private void updateConversationPhone(Long conversationId, String userId, String socketSessionId) {
        quickService.submit(() -> {
            String phone = getMVPUserPhoneNumber(userId);
            robotUserConversationRepository.updatePhone(conversationId, phone);
            RMapCache<String, String> conversationMapCache = redissonClient.getMapCache(CodeDefine.REDIS_KEY_CONVERSATION_BY_SOCKET_SESSION);
            conversationMapCache.remove(socketSessionId);
        });
    }

    private String getMVPUserPhoneNumber(String userId) {
        try {
            OkHttpClient client = new OkHttpClient.Builder()
                    .connectTimeout(20, TimeUnit.SECONDS)
                    .writeTimeout(20, TimeUnit.SECONDS)
                    .readTimeout(20, TimeUnit.SECONDS)
                    .build();

            Request request = new Request.Builder()
                    .url(webMvpHost + "/api/v1/accounts/phone?" + "user_id=" + userId)
                    .get()
                    .build();

            Response response = client.newCall(request).execute();
            JsonNode chatResDTO = objectMapper.readValue(response.body().string(), new TypeReference<>() {
            });
            log.info("======= MVP response: {}", objectMapper.writeValueAsString(chatResDTO));
            return chatResDTO.get("data").get("phone").asText();
        } catch (Exception e) {
            log.error("Error when get response MVP conversation", e);
            return null;
        }
    }

    private List<LlmMoodResDTO> getEmotionArraysBySentence(List<LlmMoodDetailDTO> emotions, String singleEmotion, String textViewer) {
        if (CollectionUtils.isEmpty(emotions)) {
            emotions = new ArrayList<>();
            emotions.add(LlmMoodDetailDTO.builder()
                    .moodName(singleEmotion)
                    .servoName(singleEmotion)
                    .duration(200000)
                    .build());
        }

        return emotions.stream()
                .map(emotion -> {
                    if (emotion.getMoodName().equalsIgnoreCase("TEXT")) {
                        return LlmMoodResDTO.builder()
                                .type("TEXT")
                                .text(textViewer)
                                .duration(emotion.getDuration())
                                .build();
                    }

                    return LlmMoodResDTO.builder()
                            .duration(emotion.getDuration())
                            .type(emotion.getMoodName())
                            .servoData(getServoDataByEmotion(emotion.getServoName()))
                            .build();
                }).collect(Collectors.toList());
    }

    public void generateConversationSummary(String socketSessionId) {
        ConversationIdAndUserIdDTO conversationData = robotUserConversationRepository.findConversationIdAndUserIdBySocketSessionId(socketSessionId);

        if (conversationData == null) {
            log.warn("No conversation found for socketSessionId: {}", socketSessionId);
            return;
        }

        String result = llmConversationService.generateConversationSummary(conversationData);
        robotUserConversationRepository.updateConversationSummary(conversationData.getExternalId(), result);
    }
}
