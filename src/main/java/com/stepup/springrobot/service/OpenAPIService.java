package com.stepup.springrobot.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.config.ConfigUtil;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.chat.ConversationReactionReqDTO;
import com.stepup.springrobot.exception.business.content.ContentNotFoundException;
import com.stepup.springrobot.exception.business.request.InvalidFormatException;
import com.stepup.springrobot.model.chat.AICharacter;
import com.stepup.springrobot.model.chat.RobotUserConversation;
import com.stepup.springrobot.model.chat.RobotUserConversationRecordHistory;
import com.stepup.springrobot.repository.chat.RobotUserConversationRecordHistoryRepository;
import com.stepup.springrobot.repository.chat.RobotUserConversationRepository;
import com.stepup.springrobot.security.JwtService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.time.Instant;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service
public class OpenAPIService extends CommonService {
    @Autowired
    private RobotUserConversationRepository robotUserConversationRepository;

    @Autowired
    private RobotUserConversationRecordHistoryRepository robotUserConversationRecordHistoryRepository;

    protected OpenAPIService(ObjectMapper objectMapper, UploadFileToS3 uploadFileToS3, JwtService jwtService,
                             SlackWarningSystemService slackWarningSystemService) {
        super(objectMapper, uploadFileToS3, jwtService, slackWarningSystemService);
    }

    public DataResponseDTO<?> uploadVideoBySocketSessionId(String socketSessionId, MultipartFile multipartFile) {
        try {
            RobotUserConversation conversation = robotUserConversationRepository.findBySocketSessionId(socketSessionId);
            if (conversation == null) {
                throw new ContentNotFoundException("conversation with socket session", socketSessionId);
            }

            if (conversation.getVideo() != null) {
                throw new InvalidFormatException("Hội thoại đã tồn tại video");
            }

            String folder = CodeDefine.UPLOAD_VIDEO_IN_BUCKET_STORAGE_S3;
            File file = convertMultiPartFileToFile(multipartFile, 9999L, true);

            if (file.length() == 0 && file.exists()) {
                file.delete();
                log.error("File to be uploaded is empty");
            }

            log.info("Start upload file to S3 {}", file.length());

            // String objectKey = folder + File.separator + file.getName();
            String objectKey = folder + "/" + file.getName();
            String tag = uploadFileToS3.putS3ObjectWithoutType(ConfigUtil.INSTANCE.getS3Bucket(), objectKey, file);
            file.delete();

            if (tag != null) {
                log.info("Upload file to S3 success: {}", tag);
            } else {
                log.error("Upload file to S3 fail");
            }

            String url = ConfigUtil.INSTANCE.getCdnDomain() + "/" + objectKey + "?v1=" + Instant.now().toEpochMilli();
            conversation.setVideo(url);
            log.info("=========== Upload video for socket session: {}, url: {}", socketSessionId, url);
            robotUserConversationRepository.save(conversation);
            return new DataResponseDTO<>(CodeDefine.OK, "Lưu video thành công", url);
        } catch (Exception e) {
            log.info("=========== Error save video: {}", e.getMessage());
          throw e;
        } finally {
            log.error("=========== Save video for socket session: {}, file size: {}", socketSessionId, multipartFile.getSize());
        }
    }

    public DataResponseDTO<?> saveConversationLikeReaction(ConversationReactionReqDTO reqDTO) {
        String socketSessionId = reqDTO.getSocketSessionId();
        RobotUserConversation conversation = robotUserConversationRepository.findBySocketSessionId(socketSessionId);
        if (conversation == null) {
            throw new ContentNotFoundException("conversation with socket session", socketSessionId);
        }

        if (conversation.getVideo() != null) {
            throw new InvalidFormatException("Hội thoại đã tồn tại video");
        }

        RobotUserConversationRecordHistory robotUserConversationRecordHistory = robotUserConversationRecordHistoryRepository.findFirstByRobotUserConversationIdAndCharacterInOrderByIdDesc(conversation.getId(), List.of(AICharacter.FAST_RESPONSE, AICharacter.BOT_RESPONSE_CONVERSATION));

        if (reqDTO.isLike()) {
            robotUserConversationRecordHistory.setLikes(robotUserConversationRecordHistory.getLikes() + 1);
        } else {
            robotUserConversationRecordHistory.setDislikes(robotUserConversationRecordHistory.getDislikes() + 1);
        }

        robotUserConversationRecordHistoryRepository.save(robotUserConversationRecordHistory);
        return new DataResponseDTO<>(CodeDefine.OK, "Lưu reaction thành công");
    }
}
