package com.stepup.springrobot.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.dto.community.*;
import com.stepup.springrobot.exception.base.BaseException;
import com.stepup.springrobot.exception.base.ErrorCode;
import com.stepup.springrobot.model.community.Comment;
import com.stepup.springrobot.model.community.CommentLike;
import com.stepup.springrobot.model.community.CommunityFeature;
import com.stepup.springrobot.model.community.FeatureLike;
import com.stepup.springrobot.model.user.User;
import com.stepup.springrobot.repository.auth.UserRepository;
import com.stepup.springrobot.repository.community.CommentLikeRepository;
import com.stepup.springrobot.repository.community.CommentRepository;
import com.stepup.springrobot.repository.community.CommunityFeatureRepository;
import com.stepup.springrobot.repository.community.FeatureLikeRepository;
import com.stepup.springrobot.security.JwtService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityNotFoundException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class CommunityService extends CommonService {
    private final CommunityFeatureRepository communityFeatureRepository;

    private final CommentRepository commentRepository;

    private final FeatureLikeRepository featureLikeRepository;

    private final CommentLikeRepository commentLikeRepository;

    private final UserRepository userRepository;

    @Autowired
    public CommunityService(ObjectMapper objectMapper, UploadFileToS3 uploadFileToS3,
                            JwtService jwtService, SlackWarningSystemService slackWarningSystemService,
                            CommunityFeatureRepository communityFeatureRepository, CommentRepository commentRepository,
                            FeatureLikeRepository featureLikeRepository, CommentLikeRepository commentLikeRepository,
                            UserRepository userRepository) {
        super(objectMapper, uploadFileToS3, jwtService, slackWarningSystemService);
        this.communityFeatureRepository = communityFeatureRepository;
        this.commentRepository = commentRepository;
        this.featureLikeRepository = featureLikeRepository;
        this.commentLikeRepository = commentLikeRepository;
        this.userRepository = userRepository;
    }

    public FeatureResDto getAllFeatures(String userId, int page) {
        int size = 10;
        page = Math.max(1, page);
        Pageable pageable = PageRequest.of(page - 1, size, Sort.by("createdAt").descending());

        Page<CommunityFeature> features = communityFeatureRepository.findAll(pageable);

        // If user is authenticated, fetch all likes for this user for the current page's features in one query
        Map<Long, Boolean> userLikes = Collections.emptyMap();
        if (userId != null) {
            List<Long> featureIds = features.getContent().stream()
                    .map(CommunityFeature::getId)
                    .collect(Collectors.toList());

            // This would require a new repository method
            userLikes = featureLikeRepository.findUserLikesForFeatures(featureIds, userId.toString())
                    .stream()
                    .collect(Collectors.toMap(
                            FeatureLike::getFeatureId,
                            like -> true
                    ));
        }

        // Fetch comment counts for all features in one query
        Map<Long, Long> commentCounts = commentRepository.countByFeatureIdIn(
                        features.getContent().stream()
                                .map(CommunityFeature::getId)
                                .collect(Collectors.toList()))
                .stream()
                .collect(Collectors.toMap(
                        result -> (Long) result[0],
                        result -> (Long) result[1]
                ));

        // Map to DTOs with the pre-fetched data
        final Map<Long, Boolean> finalUserLikes = userLikes;
        final Map<Long, Long> finalCommentCounts = commentCounts;

        Page<FeatureDto> featureDtos = features.map(feature ->
                FeatureDto.builder()
                        .id(feature.getId())
                        .title(feature.getTitle())
                        .description(feature.getDescription())
                        .likes(feature.getLikes())
                        .createdAt(feature.getCreatedAt())
                        .userLiked(finalUserLikes.getOrDefault(feature.getId(), false))
                        .comments(finalCommentCounts.getOrDefault(feature.getId(), 0L))
                        .build());

        return FeatureResDto.builder()
                .features(featureDtos.getContent())
                .totalPage(featureDtos.getTotalPages())
                .currentPage(page)
                .build();
    }

    @Transactional
    public FeatureDto addFeature(FeatureReqDto featureDto, String userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new EntityNotFoundException("User not found with ID: " + userId));

        // Create feature with audit information
        CommunityFeature feature = CommunityFeature.builder()
                .title(featureDto.getTitle())
                .description(featureDto.getDescription())
                .likes(0)
                .build();

        CommunityFeature savedFeature = communityFeatureRepository.save(feature);

        return mapToFeatureDto(savedFeature, UUID.fromString(userId));
    }

    @Transactional
    public void likeFeature(Long featureId, UUID userId) {
        CommunityFeature feature = communityFeatureRepository.findById(featureId)
                .orElseThrow(() -> new EntityNotFoundException("Feature not found with ID: " + featureId));

        User user = userRepository.findById(userId.toString())
                .orElseThrow(() -> new EntityNotFoundException("User not found with ID: " + userId));

        FeatureLike existingLike = featureLikeRepository.findByFeatureIdAndUserId(feature.getId(), user.getId());

        if (existingLike != null) {
            // Unlike if already liked
            featureLikeRepository.delete(existingLike);

            // Use atomic operation to avoid race conditions
            communityFeatureRepository.decrementLikes(featureId);
        } else {
            // Like if not already liked
            FeatureLike like = FeatureLike.builder()
                    .featureId(feature.getId())
                    .userId(user.getId())
                    .build();
            featureLikeRepository.save(like);

            // Use atomic operation to avoid race conditions
            communityFeatureRepository.incrementLikes(featureId);
        }
    }

    @Transactional
    public CommentDto addComment(Long featureId, CommentReqDto commentDto, UUID userId) {
        if (org.springframework.util.StringUtils.isEmpty(commentDto.getContent())) {
            throw new BaseException(ErrorCode.MISSING_PARAM, "nội dung bình luận");
        }

        CommunityFeature feature = communityFeatureRepository.findById(featureId)
                .orElseThrow(() -> new BaseException(ErrorCode.CONTENT_NOT_FOUND, "feature", featureId));

        User user = userRepository.findById(userId.toString())
                .orElseThrow(() -> new BaseException(ErrorCode.USER_NOT_FOUND));

        Comment comment = Comment.builder()
                .content(commentDto.getContent())
                .likes(0)
                .featureId(feature.getId())
                .userId(user.getId())
                .build();

        Comment savedComment = commentRepository.save(comment);

        // Since the current user is the comment author in this case, we can pass the same user object
        return mapToCommentDto(savedComment, user, user);
    }

    /**
     * Like or unlike a comment
     *
     * @param commentId The ID of the comment to like/unlike
     * @param userId    The ID of the user performing the action
     */
    @Transactional
    public void likeComment(Long commentId, UUID userId) {
        // Find comment with proper exception handling
        Comment comment = commentRepository.findById(commentId)
                .orElseThrow(() -> new BaseException(ErrorCode.CONTENT_NOT_FOUND, "comment", commentId));

        // Find user with proper exception handling
        User user = userRepository.findById(userId.toString())
                .orElseThrow(() -> new BaseException(ErrorCode.USER_NOT_FOUND));

        CommentLike existingLike = commentLikeRepository.findByCommentIdAndUserId(comment.getId(), user.getId());

        if (existingLike != null) {
            // Unlike if already liked
            commentLikeRepository.delete(existingLike);

            // Use atomic operation to avoid race conditions
            commentRepository.decrementLikes(commentId);
        } else {
            // Like if not already liked
            CommentLike like = CommentLike.builder()
                    .commentId(comment.getId())
                    .userId(user.getId())
                    .build();
            commentLikeRepository.save(like);

            // Use atomic operation to avoid race conditions
            commentRepository.incrementLikes(commentId);
        }

        // No need to save the comment entity as the like count is updated atomically
    }

    /**
     * Get paginated comments for a specific feature
     *
     * @param featureId The ID of the feature to get comments for
     * @param userId    The ID of the current user (can be null for anonymous users)
     * @param page      The page number (1-based)
     * @return A CommentResDto containing the comments and pagination information
     */
    public CommentResDto getCommentsByFeatureId(Long featureId, UUID userId, int page) {
        page = Math.max(1, page);

        int size = 10;

        communityFeatureRepository.findById(featureId)
                .orElseThrow(() -> new BaseException(ErrorCode.CONTENT_NOT_FOUND, "feature", featureId));

        Pageable pageable = PageRequest.of(page - 1, size, Sort.by("createdAt").descending());
        Page<Comment> comments = commentRepository.findByFeatureId(featureId, pageable);

        // If there are no comments, return an empty result
        if (comments.isEmpty()) {
            return CommentResDto.builder()
                    .comments(Collections.emptyList())
                    .currentPage(page)
                    .totalPage(0)
                    .build();
        }

        // Get user account if userId is provided
        User userAccount = null;
        if (userId != null) {
            userAccount = userRepository.findById(userId.toString()).orElse(null);
        }

        // Collect all comment user IDs to fetch in a single query
        List<String> commentUserIds = comments.getContent().stream()
                .map(Comment::getUserId)
                .distinct()
                .collect(Collectors.toList());

        // Fetch all comment authors in a single query
        Map<String, User> commentAuthors = Collections.emptyMap();
        if (!commentUserIds.isEmpty()) {
            commentAuthors = userRepository.findAllById(commentUserIds).stream()
                    .collect(Collectors.toMap(User::getId, user -> user));
        }

        // Map comments to DTOs with user like status
        final User finalUserAccount = userAccount;
        final Map<String, User> finalCommentAuthors = commentAuthors;

        Page<CommentDto> commentDtoPage = comments.map(comment -> {
            // Get the comment author from the pre-fetched map
            User commentAuthor = finalCommentAuthors.get(comment.getUserId());

            // Map to DTO with like status
            return mapToCommentDto(comment, commentAuthor, finalUserAccount);
        });

        return CommentResDto.builder()
                .comments(commentDtoPage.getContent())
                .currentPage(page)
                .totalPage(commentDtoPage.getTotalPages())
                .build();
    }

    /**
     * Maps a CommunityFeature entity to a FeatureDto with user-specific information
     *
     * @param feature The feature entity to map
     * @param userId  The ID of the current user (can be null for anonymous users)
     * @return A FeatureDto with all necessary information
     */
    private FeatureDto mapToFeatureDto(CommunityFeature feature, UUID userId) {
        if (feature == null) {
            return null;
        }

        boolean userLiked = false;

        if (userId != null) {
            // Directly check if the user liked the feature without loading the entire user entity
            userLiked = featureLikeRepository.findByFeatureIdAndUserId(feature.getId(), userId.toString()) != null;
        }

        return FeatureDto.builder()
                .id(feature.getId())
                .title(feature.getTitle())
                .description(feature.getDescription())
                .likes(feature.getLikes())
                .createdAt(feature.getCreatedAt())
                .userLiked(userLiked)
                .comments(commentRepository.countByFeatureId(feature.getId()))
                .build();
    }

    /**
     * Maps a Comment entity to a CommentDto with user-specific information
     *
     * @param comment       The comment entity to map
     * @param commentAuthor The user who authored the comment (can be null)
     * @param currentUser   The current user viewing the comment (can be null for anonymous users)
     * @return A CommentDto with all necessary information
     */
    private CommentDto mapToCommentDto(Comment comment, User commentAuthor, User currentUser) {
        if (comment == null) {
            return null;
        }

        boolean userLiked = false;

        if (currentUser != null) {
            userLiked = commentLikeRepository.findByCommentIdAndUserId(comment.getId(), currentUser.getId()) != null;
        }

        String authorName = commentAuthor != null ? commentAuthor.getPhone() : "Unknown User";

        return CommentDto.builder()
                .id(comment.getId())
                .content(comment.getContent())
                .likes(comment.getLikes())
                .featureId(comment.getFeatureId())
                .accountName(authorName)
                .createdAt(comment.getCreatedAt())
                .userLiked(userLiked)
                .build();
    }
}