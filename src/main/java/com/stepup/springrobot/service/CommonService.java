package com.stepup.springrobot.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.common.DateUtils;
import com.stepup.springrobot.config.ConfigUtil;
import com.stepup.springrobot.dto.UserBookCustomDTO;
import com.stepup.springrobot.dto.UserDataDTO;
import com.stepup.springrobot.dto.auth.AccountInfoDTO;
import com.stepup.springrobot.dto.chat.AIConversationDetailMessageResDTO;
import com.stepup.springrobot.dto.chat.WhisperAudioCMUDTO;
import com.stepup.springrobot.dto.chat.WhisperLipSyncDTO;
import com.stepup.springrobot.exception.business.user.AnonymousAccessException;
import com.stepup.springrobot.security.JwtService;
import io.jsonwebtoken.Claims;
import io.sentry.Sentry;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.MultipartBodyBuilder;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigInteger;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;

@Slf4j
@Service
public abstract class CommonService {
    protected final ObjectMapper objectMapper;

    protected final UploadFileToS3 uploadFileToS3;

    private final JwtService jwtService;

    @Value("${lipsync_host_name}")
    private String lipSyncHostName;

    @Value("${lipsync_uri}")
    private String lipSyncUri;

    @Value("${lipsync_token}")
    private String lipSyncToken;

    private SlackWarningSystemService slackWarningSystemService;

    @Autowired
    protected CommonService(ObjectMapper objectMapper, UploadFileToS3 uploadFileToS3, JwtService jwtService, SlackWarningSystemService slackWarningSystemService) {
        this.objectMapper = objectMapper;
        this.uploadFileToS3 = uploadFileToS3;
        this.jwtService = jwtService;
        this.slackWarningSystemService = slackWarningSystemService;
    }

    protected UserDataDTO getDataUserHeader(HttpServletRequest request) throws IOException {
        String authHeader = request.getHeader("Authorization");
        String token = authHeader.substring(7);
        final Claims claims = jwtService.extractAllClaims(token);
        Map<String, Object> claimsMap = objectMapper.readValue(objectMapper.writeValueAsString(claims), new TypeReference<>() {
        });
        return UserDataDTO.builder()
                .userId(claimsMap.get("user_id").toString())
                .phone(claimsMap.get("phone").toString())
                .jti(claimsMap.get("jti").toString())
                .build();
    }

    public AccountInfoDTO getAccountInfo(HttpServletRequest request, String deviceId, boolean isOpenApi) throws IOException {
        UserDataDTO userDataDTO = null;
        if (!isOpenApi) {
            userDataDTO = getDataUserHeader(request);
            // chỉ tồn tại user_id hoặc device_id, ko tồn tại song song
            deviceId = null;
        } else if (StringUtils.isEmpty(deviceId)) {
            throw new AnonymousAccessException();
        }

        return AccountInfoDTO.builder()
                .userData(userDataDTO)
                .deviceId(deviceId)
                .build();
    }

    public String encrypt(Object object) throws JsonProcessingException {
        if (object == null) return null;

        String inputString = objectMapper.writeValueAsString(object);
        String keyXorEncode = "1StepUp1VietNam@";
        char[] keys = keyXorEncode.toCharArray();
        char[] inputChars = inputString.toCharArray();

        char[] outputChars = new char[inputChars.length];
        for (int i = 0; i < inputChars.length; i++) {
            int index = i >= keyXorEncode.length() ? keyXorEncode.length() - 1 : i;
            outputChars[i] = (char) (inputChars[i] ^ keys[index]);
        }

        return Base64.getEncoder().encodeToString(new String(outputChars).getBytes(StandardCharsets.UTF_8));
    }

    /**
     * Check app_version (version1 là số cần kiểm tra)
     */
    protected int compareAppVersions(String version1, String version2) {
        if (version1 == null) {
            return -1;
        }

        if (version1.contains(",")) {
            version1 = version1.split(",")[0];
        }

        String[] arr1 = version1.split("\\.");
        String[] arr2 = version2.split("\\.");

        for (int i = 0; i < Math.max(arr1.length, arr2.length); i++) {
            // check if arr1[i] is valid number
            if (i < arr1.length && !StringUtils.isNumeric(arr1[i])) {
                return -1;
            }

            int num1 = i < arr1.length ? Integer.parseInt(arr1[i]) : 0;
            int num2 = i < arr2.length ? Integer.parseInt(arr2[i]) : 0;
            if (num1 < num2) {
                return -1;
            } else if (num1 > num2) {
                return 1;
            }
        }

        return 0;
    }

    protected String getRequestAppVersion(HttpServletRequest request) {
        return request.getParameter("app_v");
    }

    protected boolean isExistProductBought(UserDataDTO userDataDTO) {
        if (userDataDTO == null) {
            return false;
        }

        List<UserBookCustomDTO> products = objectMapper.convertValue(userDataDTO.getProduct(), new TypeReference<>() {
        });
        var userBookCustom = products.parallelStream()
                .filter(userBookCustomDTO ->
                        userBookCustomDTO.getBook().equals(CodeDefine.PRODUCT_TF22)
                                && userBookCustomDTO.getExpireDate() != null
                                && DateUtils.getDateFromString(userBookCustomDTO.getExpireDate()).getTime() > new Date().getTime()
                                && !userBookCustomDTO.isTrial())
                .findAny().orElse(null);
        return userBookCustom != null;
    }

    /**
     * Bởi vì client luôn gửi lên file test.wav, phía sau hàm này có function xoá file.
     * việc này làm cho khi nhiều user dùng chức năng sẽ dẫn tới request sau khi get file bị null
     * Do vậy, cần đổi tên file khi upload lên.
     *
     * @param multipartFile file upload
     * @param userId user_id
     * @return file
     */
    protected File convertMultiPartFileToFile(MultipartFile multipartFile, Object userId, boolean isFormatName) {
        String newFileName = isFormatName
                ? System.currentTimeMillis() + "_" + userId + "." + FilenameUtils.getExtension(multipartFile.getOriginalFilename())
                : multipartFile.getOriginalFilename();
        multipartFile = getNewFile(newFileName, multipartFile);

        final File file = new File(Objects.requireNonNull(multipartFile.getOriginalFilename()));
        try (final FileOutputStream outputStream = new FileOutputStream(file)) {
            outputStream.write(multipartFile.getBytes());
        } catch (final IOException ex) {
            log.error("Error converting the multi-part file to file= {}", ex.getMessage());
            Sentry.captureException(ex);
            return null;
        }
        return file;
    }

    protected File convertMultiPartFileToFileWithName(MultipartFile multipartFile, String fileName) {
        multipartFile = getNewFile(fileName, multipartFile);

        final File file = new File(Objects.requireNonNull(multipartFile.getOriginalFilename()));
        try (final FileOutputStream outputStream = new FileOutputStream(file)) {
            outputStream.write(multipartFile.getBytes());
        } catch (final IOException ex) {
            log.error("Error converting the multi-part file to file= {}", ex.getMessage());
            Sentry.captureException(ex);
            return null;
        }
        return file;
    }

    private MultipartFile getNewFile(String fileName, MultipartFile currentFile) {
        return new MultipartFile() {
            @Override
            public String getName() {
                return currentFile.getName();
            }

            @Override
            public String getOriginalFilename() {
                return fileName;
            }

            @Override
            public String getContentType() {
                return currentFile.getContentType();
            }

            @Override
            public boolean isEmpty() {
                return currentFile.isEmpty();
            }

            @Override
            public long getSize() {
                return currentFile.getSize();
            }

            @Override
            public byte[] getBytes() throws IOException {
                return currentFile.getBytes();
            }

            @Override
            public InputStream getInputStream() throws IOException {
                return currentFile.getInputStream();
            }

            @Override
            public void transferTo(File file) throws IllegalStateException {
            }
        };
    }

    protected boolean uploadFileToS3(String folder, File file, String uploadFileName, String format) {
        if (file.length() == 0 && file.exists()) {
            file.delete();
            log.error("File to be uploaded is empty");
            return false;
        }

        log.info("Start upload file to S3 {}", file.length());
        String objectKey;
        if (uploadFileName != null) {
            objectKey = folder + File.separator + uploadFileName;
        } else {
            objectKey = folder + File.separator + file.getName();
        }

        String tag = uploadFileToS3.putS3ObjectAudio(ConfigUtil.INSTANCE.getS3Bucket(), objectKey, file, format);

        if (tag != null) {
            log.info("Upload file to S3 success: {}", tag);
            return true;
        } else {
            log.error("Upload file to S3 fail");
            return false;
        }
    }

    protected String trimToken(String token) {
        if (!StringUtils.isEmpty(token)) {
            token = "***" + token.substring(token.length() - 10);
        }

        return token;
    }

    protected String generateMD5Hash(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] messageDigest = md.digest(input.getBytes());

            BigInteger no = new BigInteger(1, messageDigest);
            StringBuilder hashText = new StringBuilder(no.toString(16));

            while (hashText.length() < 32) {
                hashText.insert(0, "0");
            }

            return hashText.toString();
        } catch (NoSuchAlgorithmException e) {
            String message = "Lỗi khi sinh MD5 hash: " + e.getMessage() + " ===== input: " + input;
            throw new RuntimeException(message);
        }
    }

    protected File downloadFileFromURL(URL url, String userId) throws IOException {
        String fileName = userId + "_" + FilenameUtils.getName(url.getPath());
        File file = new File(fileName);
        FileUtils.copyURLToFile(url, file);
        return file;
    }

    protected List<WhisperAudioCMUDTO> getSpeechAnimationForSentence(AIConversationDetailMessageResDTO message, String userId, boolean isConvertToWavFile) {
        try {
            File audioFile = downloadFileFromURL(new URL(message.getAudio()), userId);
            WhisperLipSyncDTO whisperLipSyncDTO;
            if (isConvertToWavFile) {
                File wavFile = new File(UtilsService.getWavFilePath(audioFile.getPath()));
                try {
                    whisperLipSyncDTO = getAudioLipSyncData(wavFile, message.getContent());
                    wavFile.delete();
                    audioFile.delete();
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            } else {
                try {
                    whisperLipSyncDTO = getAudioLipSyncData(audioFile, message.getContent());
                    audioFile.delete();
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            }


            WhisperLipSyncDTO whisperAudioResDTO = WhisperLipSyncDTO.builder()
                    .text(message.getContent())
                    .mouthCues(whisperLipSyncDTO.getMouthCues())
                    .build();

            List<WhisperAudioCMUDTO> mouthCues = whisperAudioResDTO.getMouthCues();
            mouthCues.forEach(whisperAudioCMUDTO -> {
                whisperAudioCMUDTO.setCmu("expression");
                whisperAudioCMUDTO.setAnimation("trigger_mouth_" + whisperAudioCMUDTO.getAnimationId());
            });

            if (message.isLastSentence()) {
                mouthCues.add(WhisperAudioCMUDTO.builder()
                        .cmu("expression")
                        .start(mouthCues.get(mouthCues.size() - 1).getEnd())
                        .end(mouthCues.get(mouthCues.size() - 1).getEnd())
                        .animation("trigger_idle")
                        .build());
            }

            // return CollectionUtils.isEmpty(whisperAudioResDTO.getMouthCues()) ? new ArrayList<>() : whisperAudioResDTO.getMouthCues();
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("Error getting speech animation for sentence: {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    private WhisperLipSyncDTO getAudioLipSyncData(File file, String text) throws JsonProcessingException {
        try {
            WebClient client3 = WebClient
                    .builder()
                    .baseUrl(lipSyncHostName)
                    .build();

            MultipartBodyBuilder builder = new MultipartBodyBuilder();
            builder.part("audioData", new FileSystemResource(file));
            builder.part("textData", text);
            builder.part("secret", lipSyncToken);
            Mono<WhisperLipSyncDTO> dataDTOMono = client3.post().uri(lipSyncUri)
                    .header(HttpHeaders.CONTENT_TYPE, MediaType.MULTIPART_FORM_DATA_VALUE)
                    .body(BodyInserters.fromMultipartData(builder.build()))
                    .retrieve().bodyToMono(WhisperLipSyncDTO.class);
            WhisperLipSyncDTO whisperLipSyncDTO = Objects.requireNonNull(dataDTOMono.block());
            log.info("Lấy animation text thành công: {}, result: {}", text, objectMapper.writeValueAsString(whisperLipSyncDTO));
            return whisperLipSyncDTO;
        } catch (Exception e) {
            slackWarningSystemService.sendWarningSystemToSlack("Lỗi lấy animation: " + e.getMessage(), ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
            throw e;
        }
    }
}
