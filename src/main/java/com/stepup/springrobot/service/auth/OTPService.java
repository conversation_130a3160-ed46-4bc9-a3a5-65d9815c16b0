package com.stepup.springrobot.service.auth;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.config.ConfigUtil;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.UserDataDTO;
import com.stepup.springrobot.dto.auth.ZaloAuthenticationConfigReqDTO;
import com.stepup.springrobot.dto.auth.ZaloOTPDetailReqDTO;
import com.stepup.springrobot.dto.auth.ZaloOTPReqDTO;
import com.stepup.springrobot.model.auth.ZaloAuthenticationConfig;
import com.stepup.springrobot.repository.auth.ZaloAuthenticationConfigRepository;
import com.stepup.springrobot.security.JwtService;
import com.stepup.springrobot.service.CommonService;
import com.stepup.springrobot.service.SlackWarningSystemService;
import com.stepup.springrobot.service.UploadFileToS3;
import lombok.extern.log4j.Log4j2;
import okhttp3.*;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.UUID;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Log4j2
@Service
public class OTPService extends CommonService {
    private final OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .build();

    @Value("${zns.template.id}")
    private String znsTemplateId;

    @Value("${fns.template.id}")
    private String fnsTemplateId;

    @Value("${zns.app.id}")
    private String znsAppId;

    @Value("${zns.app.secret_key}")
    private String znsAppSecretKey;

    @Value("${fns.app.id}")
    private String ZALO_OTP_FNS_APP_ID;

    @Value("${fns.app.secret_key}")
    private String ZALO_OTP_FNS_SECRET_KEY;

    @Value("${hacknao_token}")
    private String HACKNAO_TOKEN;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ZaloAuthenticationConfigRepository zaloAuthenticationConfigRepository;

    @Autowired
    private SlackWarningSystemService slackWarningSystemService;

    @Autowired
    private RedissonClient redissonClient;


    private final ScheduledExecutorService quickService = Executors.newScheduledThreadPool(20);

    protected OTPService(ObjectMapper objectMapper, UploadFileToS3 uploadFileToS3, JwtService jwtService, SlackWarningSystemService slackWarningSystemService) {
        super(objectMapper, uploadFileToS3, jwtService, slackWarningSystemService);
    }

    private String normalizeNationPhoneNumber(String phone) {
        phone = phone.trim();
        if (phone.charAt(0) == '0') {
            return "84" + phone.substring(1);
        }

        if (phone.charAt(0) == '+') {
            return phone.substring(1);
        }

        return phone;
    }

    private boolean refreshToken() throws JsonProcessingException {
        ZaloAuthenticationConfig zaloAuthenticationConfig = getZaloAuthenticationConfig();
        RequestBody formBody = new FormBody.Builder()
                .add("grant_type", "refresh_token")
                .add("app_id", znsAppId)
                .add("refresh_token", zaloAuthenticationConfig.getRefreshToken())
                .build();

        Request request = new Request.Builder()
                .addHeader("secret_key", znsAppSecretKey)
                .url("https://oauth.zaloapp.com/v4/oa/access_token")
                .post(formBody)
                .build();

        try {
            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                String message = "Lỗi khi gửi request refresh token Zalo ==== response: " + objectMapper.writeValueAsString(response);
                log.error(message);
                slackWarningSystemService.sendWarningSystemToSlack(message, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
                return false;
            }

            JsonNode responseJson = objectMapper.readTree(response.body().string());
            response.close();

            if (responseJson.get("refresh_token") != null && responseJson.get("access_token") != null) {
                zaloAuthenticationConfig.setAccessToken(responseJson.get("access_token").asText());
                zaloAuthenticationConfig.setRefreshToken(responseJson.get("refresh_token").asText());
                zaloAuthenticationConfigRepository.saveAndFlush(zaloAuthenticationConfig);
                redissonClient.getBucket(CodeDefine.REDIS_KEY_ZALO_AUTHENTICATION_CONFIG).delete();
                return true;
            }

            quickService.submit(this::alertInstructionToGetAccessToken);
            return false;
        } catch (IOException e) {
            String message = "Có lỗi xảy ra khi gửi request refresh token Zalo! === " + e.getMessage();
            log.error(message);
            return false;
        }
    }

    private void alertInstructionToGetAccessToken() {
        try {
            String codeVerifier = genZNSCodeVerifier();
            String codeChallenge = genZNSCodeChallenge(codeVerifier);
            String message = "Code verifier: " + codeVerifier + "\n" + "Code challenge: " + codeChallenge + "\n" + "Hướng dẫn tạo access token: https://stepup-english.atlassian.net/wiki/spaces/TCT/pages/384565257/C+ch+t+o+m+i+token+Zalo";
            slackWarningSystemService.sendWarningSystemToSlack(message, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
        } catch (NoSuchAlgorithmException e) {
            String message = "@all Có lỗi xảy ra khi tạo code verifier và code challenge! === " + e.getMessage();
            log.error(message);
            slackWarningSystemService.sendWarningSystemToSlack(message, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
        }
    }

    private String genZNSCodeVerifier() {
        SecureRandom sr = new SecureRandom();
        byte[] code = new byte[32];
        sr.nextBytes(code);
        return Base64.getUrlEncoder().withoutPadding().encodeToString(code);
    }

    private String genZNSCodeChallenge(String codeVerifier) throws NoSuchAlgorithmException {
        byte[] bytes = codeVerifier.getBytes(StandardCharsets.US_ASCII);
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        md.update(bytes, 0, bytes.length);
        byte[] digest = md.digest();
        return Base64.getUrlEncoder().withoutPadding().encodeToString(digest);
    }

    private ZaloAuthenticationConfig getZaloAuthenticationConfig() throws JsonProcessingException {
        // Cho vào redis
        RBucket<String> zaloAuthenticationConfigBucket = redissonClient.getBucket(CodeDefine.REDIS_KEY_ZALO_AUTHENTICATION_CONFIG);
        if (zaloAuthenticationConfigBucket.isExists()) {
            return objectMapper.readValue(zaloAuthenticationConfigBucket.get(), ZaloAuthenticationConfig.class);
        }

        ZaloAuthenticationConfig zaloAuthenticationConfig = zaloAuthenticationConfigRepository.getZaloAuthenticationConfig();
        if (zaloAuthenticationConfig == null) {
            String message = "@all Zalo authentication config is null";
            log.error(message);
            slackWarningSystemService.sendWarningSystemToSlack(message, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
            throw new RuntimeException(message);
        } else {
            zaloAuthenticationConfigBucket.set(objectMapper.writeValueAsString(zaloAuthenticationConfig), CodeDefine.TTL_KEY_ZALO_AUTHENTICATION_CONFIG, TimeUnit.DAYS);
        }

        return zaloAuthenticationConfig;
    }

    private boolean processCheckZaloQuotaResponse(JsonNode response) {
        try {
            int errorStatus = response.get("error").asInt();
            if (errorStatus == 0) {
                int remainingQuota = response.get("data").get("remainingQuota").asInt();
                return remainingQuota > 10;
            }

            String message;
            if (errorStatus == -124) {
                if (refreshToken()) {
                    ZaloAuthenticationConfig zaloAuthenticationConfig = getZaloAuthenticationConfig();
                    Request request = new Request.Builder()
                            .addHeader("access_token", zaloAuthenticationConfig.getAccessToken())
                            .url("https://business.openapi.zalo.me/message/quota")
                            .build();

                    Response secondResponse = client.newCall(request).execute();
                    if (!secondResponse.isSuccessful()) {
                        message = "Có lỗi xảy ra khi check Zalo quota, trả về lỗi! === response: " + objectMapper.writeValueAsString(secondResponse);
                        log.error(message);
                        slackWarningSystemService.sendWarningSystemToSlack(message, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
                        return false;
                    }

                    response = objectMapper.readTree(secondResponse.body().string());
                    secondResponse.close();
                    errorStatus = response.get("error").asInt();
                    if (errorStatus == 0) {
                        int remainingQuota = response.get("data").get("remainingQuota").asInt();
                        return remainingQuota > 10;
                    }

                    message = "Có lỗi xảy ra khi check Zalo quota, trả về lỗi! === response: " + objectMapper.writeValueAsString(secondResponse);
                    log.error(message);
                    slackWarningSystemService.sendWarningSystemToSlack(message, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
                    return false;
                }

                return false;
            }

            message = "Có lỗi xảy ra khi check Zalo quota! === response: " + objectMapper.writeValueAsString(response);
            log.error(message);
            slackWarningSystemService.sendWarningSystemToSlack(message, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
            return false;
        } catch (Exception e) {
            String message = "Có lỗi xảy ra khi check Zalo quota! === " + e.getMessage();
            log.error(message, e);
            slackWarningSystemService.sendWarningSystemToSlack(message, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
            return false;
        }
    }

    /**
     * Chi tiết mã lỗi coi tại <a href="https://developers.zalo.me/docs/api/zalo-notification-service-api/phu-luc/bang-ma-loi-post-5233">Bảng mã lỗi ZNS</a>
     */
    private boolean processSendingZaloOTPResponse(ZaloOTPReqDTO zaloOTPReqDTO, JsonNode response, String ip) throws IOException {
        int errorStatus = response.get("error").asInt();
        if (errorStatus == 0) {
            return true;
        }

        String phoneNumber = zaloOTPReqDTO.getPhone();
        if (errorStatus == -124) {
            if (refreshToken()) {
                ZaloAuthenticationConfig zaloAuthenticationConfig = getZaloAuthenticationConfig();
                Request request = new Request.Builder()
                        .addHeader("access_token", zaloAuthenticationConfig.getAccessToken())
                        .url("https://business.openapi.zalo.me/message/template")
                        .post(RequestBody.create(okhttp3.MediaType.parse("application/json"), objectMapper.writeValueAsString(zaloOTPReqDTO)))
                        .build();

                Response secondResponse = client.newCall(request).execute();
                if (!secondResponse.isSuccessful()) {
                    String message = "Có lỗi xảy ra khi gửi Zalo OTP, trả về lỗi! === response: " + objectMapper.writeValueAsString(secondResponse) + " === ip: " + ip + " === phone: " + phoneNumber;
                    log.error(message);
                    slackWarningSystemService.sendWarningSystemToSlack(message, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
                    return false;
                }

                response = objectMapper.readTree(secondResponse.body().string());
                secondResponse.close();
                errorStatus = response.get("error").asInt();
                if (errorStatus == 0) {
                    return true;
                }

                String message = "Có lỗi xảy ra khi gửi Zalo OTP, trả về lỗi! === response: " + objectMapper.writeValueAsString(secondResponse) + " === ip: " + ip + " === phone: " + phoneNumber;
                log.error(message);
                if (errorStatus != CodeDefine.ERROR_NOT_EXISTED && errorStatus != CodeDefine.ERROR_PHONE_NUMBER_INVALID) {
                    slackWarningSystemService.sendWarningSystemToSlack(message, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
                }

                return false;
            }

            String message = "Có lỗi xảy ra khi refresh token Zalo!" + " === ip: " + ip + " === phone: " + phoneNumber;
            log.error(message);
            slackWarningSystemService.sendWarningSystemToSlack(message, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
            return false;
        }

        String message = "Có lỗi xảy ra khi gửi Zalo OTP! === response: " + objectMapper.writeValueAsString(response) + " === ip: " + ip + " === phone: " + phoneNumber;
        log.error(message);
        if (errorStatus != CodeDefine.ERROR_NOT_EXISTED && errorStatus != CodeDefine.ERROR_PHONE_NUMBER_INVALID) {
            slackWarningSystemService.sendWarningSystemToSlack(message, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
        }

        return false;
    }

    @Transactional
    public boolean sendZnsOtpToUser(String phoneNumber, String otp, String ip) {
        ZaloOTPReqDTO zaloOTPReqDTO = ZaloOTPReqDTO.builder()
                .phone(normalizeNationPhoneNumber(phoneNumber))
                .templateId(znsTemplateId)
                .zaloOTPDetailReqDTO(ZaloOTPDetailReqDTO.builder()
                        .otp(otp)
                        .build())
                .trackingId(UUID.randomUUID().toString().replace("-", ""))
                .build();
        try {
            ZaloAuthenticationConfig zaloAuthenticationConfig = getZaloAuthenticationConfig();
            Request clientRequest = new Request.Builder()
                    .addHeader("access_token", zaloAuthenticationConfig.getAccessToken())
                    .url("https://business.openapi.zalo.me/message/template")
                    .post(RequestBody.create(okhttp3.MediaType.parse("application/json"), objectMapper.writeValueAsString(zaloOTPReqDTO)))
                    .build();

            try (Response response = client.newCall(clientRequest).execute()) {
                if (!response.isSuccessful()) {
                    String message = "Có lỗi xảy ra khi gửi Zalo OTP, vui lòng kiểm tra lại! === response: " + objectMapper.writeValueAsString(response) + " === ip: " + ip + " === phone: " + phoneNumber;
                    log.error(message);
                    slackWarningSystemService.sendWarningSystemToSlack(message, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
                    return false;
                }

                JsonNode responseJson = objectMapper.readTree(response.body().string());
                return processSendingZaloOTPResponse(zaloOTPReqDTO, responseJson, ip);
            }
        } catch (IOException e) {
            String message = "Có lỗi xảy ra khi gửi Zalo OTP, vui lòng kiểm tra lại! === " + e.getMessage() + " === ip: " + ip + " === phone: " + phoneNumber;
            log.error(message);
            slackWarningSystemService.sendWarningSystemToSlack(message, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
            return false;
        }
    }

    @Transactional
    public boolean sendFnsOtpToUser(String phoneNumber, String otp, String ip) {
        ZaloOTPReqDTO zaloOTPReqDTO = ZaloOTPReqDTO.builder()
                .phone(normalizeNationPhoneNumber(phoneNumber))
                .templateId(fnsTemplateId)
                .zaloOTPDetailReqDTO(ZaloOTPDetailReqDTO.builder()
                        .otp(otp)
                        .build())
                .build();
        try {
            Request clientRequest = new Request.Builder()
                    .addHeader("app-id", ZALO_OTP_FNS_APP_ID)
                    .addHeader("secret-key", ZALO_OTP_FNS_SECRET_KEY)
                    .url("https://api-fns.fpt.work/api/send-message")
                    .post(RequestBody.create(okhttp3.MediaType.parse("application/json"), objectMapper.writeValueAsString(zaloOTPReqDTO)))
                    .build();

            try (Response response = client.newCall(clientRequest).execute()) {
                if (!response.isSuccessful()) {
                    String message = "Có lỗi xảy ra khi gửi Zalo OTP FNS, vui lòng kiểm tra lại! === response: " + response + " === ip: " + ip + " === phone: " + phoneNumber;
                    log.error(message);
                    slackWarningSystemService.sendWarningSystemToSlack(message, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
                    return false;
                }

                JsonNode responseJson = objectMapper.readTree(response.body().string());

                int responseCode = responseJson.get("code").asInt();
                if (responseCode == 1) {
                    return true;
                }
            }
        } catch (IOException e) {
            String message = "Có lỗi xảy ra khi gửi Zalo OTP FNS, vui lòng kiểm tra lại! === " + e.getMessage() + " === ip: " + ip + " === phone: " + phoneNumber;
            log.error(message);
            slackWarningSystemService.sendWarningSystemToSlack(message, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
            return false;
        }

        return false;
    }

    @Transactional
    public boolean isAvailableZNSQuota() {
        try {
            ZaloAuthenticationConfig zaloAuthenticationConfig = getZaloAuthenticationConfig();
            Request request = new Request.Builder()
                    .addHeader("access_token", zaloAuthenticationConfig.getAccessToken())
                    .url("https://business.openapi.zalo.me/message/quota")
                    .build();

            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    String message = "Lỗi khi gửi request check quota tới Zalo ==== response: " + objectMapper.writeValueAsString(response);
                    log.error(message);
                    slackWarningSystemService.sendWarningSystemToSlack(message, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
                    return false;
                }

                JsonNode responseJson = objectMapper.readTree(response.body().string());

                return processCheckZaloQuotaResponse(responseJson);
            }
        } catch (Exception e) {
            String message = "Lỗi khi gửi request check quota tới Zalo! === " + e.getMessage();
            log.error(message, e);
            return false;
        }
    }

    @Transactional
    public boolean isAvailableFNSQuota() {
        try {
            Request request = new Request.Builder()
                    .addHeader("app-id", ZALO_OTP_FNS_APP_ID)
                    .addHeader("secret-key", ZALO_OTP_FNS_SECRET_KEY)
                    .url("https://api-fns.fpt.work/api/get-quota")
                    .build();

            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    String message = "Lỗi khi gửi request check quota tới Zalo FNS ==== response: " + response;
                    log.error(message);
                    slackWarningSystemService.sendWarningSystemToSlack(message, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
                    return false;
                }

                JsonNode responseJson = objectMapper.readTree(response.body().string());
                int responseCode = responseJson.get("code").asInt();
                if (responseCode == 1) {
                    int remainingQuota = responseJson.get("data").get("remaining_quota").asInt();
                    return remainingQuota > 0;
                }

                return false;
            }
        } catch (Exception e) {
            String message = "Lỗi khi gửi request check quota tới Zalo FNS! === " + e.getMessage();
            log.error(message);
            return false;
        }
    }
}
