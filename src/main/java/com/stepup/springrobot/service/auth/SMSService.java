package com.stepup.springrobot.service.auth;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.common.DateUtils;
import com.stepup.springrobot.common.Protocol;
import com.stepup.springrobot.common.ValidationUtils;
import com.stepup.springrobot.config.ConfigUtil;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.sms.DataSMSClientReqDTO;
import com.stepup.springrobot.dto.sms.SMSRequestChildInternalDTO;
import com.stepup.springrobot.dto.sms.SMSRequestInternalDTO;
import com.stepup.springrobot.dto.sms.SendSMSResInternalDTO;
import com.stepup.springrobot.model.sms.*;
import com.stepup.springrobot.repository.sms.SMSCellularRepository;
import com.stepup.springrobot.repository.sms.SMSQuotaRepository;
import com.stepup.springrobot.repository.sms.SMSReceiveRepository;
import com.stepup.springrobot.repository.sms.SMSSendHistoryRepository;
import com.stepup.springrobot.security.JwtService;
import com.stepup.springrobot.service.CommonService;
import com.stepup.springrobot.service.SlackWarningSystemService;
import com.stepup.springrobot.service.UploadFileToS3;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpConnectionManager;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.RequestEntity;
import org.apache.commons.httpclient.methods.StringRequestEntity;
import org.apache.commons.httpclient.params.HttpConnectionManagerParams;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

/**
 * <AUTHOR>
 * @since : 12/16/20, Wed
 */

@Slf4j
@Service
public class SMSService extends CommonService {
    @Value("${sms_gateway_url}")
    private String SMS_GATEWAY_URL;

    @Value("${brandname_network_accept_send}")
    private String BRANDNAME_NETWORK_ACCEPT_SEND;

    @Value("${brandname_sender}")
    private String BRANDNAME_SENDER;

    @Value("${brandname_content_tf_sign_up}")
    private String BRANDNAME_CONTENT_TF_SIGN_UP;

    @Value("${brandname_content_tf_forgot_pw}")
    private String BRANDNAME_CONTENT_TF_FORGOT_PW;

    @Value("${brandname_username}")
    private String BRANDNAME_USERNAME;

    @Value("${brandname_password}")
    private String BRANDNAME_PASSWORD;

    @Value("${brandname_cpcode}")
    private String BRANDNAME_CPCODE;

    @Value("${brandname_content_purchase_confirmation}")
    private String BRANDNAME_CONTENT_PURCHASE_CONFIRMATION;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private SMSSendHistoryRepository smsSendHistoryRepository;

    @Autowired
    private SMSCellularRepository smsCellularRepository;

    @Autowired
    private SlackWarningSystemService slackWarningSystemService;

    @Autowired
    private SMSReceiveRepository smsReceiveRepository;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private SMSQuotaRepository smsQuotaRepository;

    @Autowired
    private OTPService otpService;

    public static int noOfQuickServiceThreads = 20;

    /**
     * this statement create a thread pool of twenty threads
     * here we are assigning send mail task using ScheduledExecutorService.submit();
     */
    private ScheduledExecutorService quickService = Executors.newScheduledThreadPool(noOfQuickServiceThreads); // Creates a thread pool that reuses fixed number of threads(as specified by noOfThreads in this case).

    protected SMSService(ObjectMapper objectMapper, UploadFileToS3 uploadFileToS3, JwtService jwtService, SlackWarningSystemService slackWarningSystemService) {
        super(objectMapper, uploadFileToS3, jwtService, slackWarningSystemService);
    }

    @Transactional
    public void sendSMSToClient(DataSMSClientReqDTO dataSMSClientReqDTO) {
        String phone = dataSMSClientReqDTO.getPhone();
        try {
            if (phone.length() > 10) {
                throw new Exception("phone over length 10 characters " + phone);
            }
        } catch (Exception e) {
            saveSMSSendHistory(dataSMSClientReqDTO, phone, HttpStatus.NOT_FOUND);
            log.error("Error exception send to sms: " + e);
            String msg = "Không thể gửi tin nhắn OTP tới số " + phone;
            slackWarningSystemService.sendWarningSystemToSlack(msg, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
        }


        saveSMSSendHistory(dataSMSClientReqDTO, phone, HttpStatus.OK);
        quickService.submit(() -> {
            if (dataSMSClientReqDTO.getSmsServiceProvider() == SMSServiceProvider.SMS_ZALO) {
                boolean isSendZaloOtpSuccess;
                if (otpService.isAvailableFNSQuota()) {
                    isSendZaloOtpSuccess = otpService.sendFnsOtpToUser(phone, dataSMSClientReqDTO.getOtp(), dataSMSClientReqDTO.getIp());
                } else {
                    isSendZaloOtpSuccess = otpService.sendZnsOtpToUser(phone, dataSMSClientReqDTO.getOtp(), dataSMSClientReqDTO.getIp());
                }

                if (!isSendZaloOtpSuccess) {
                    sendSMSByBrandname(phone, dataSMSClientReqDTO);
                }

                return;
            }

            if (dataSMSClientReqDTO.getSmsServiceProvider() == SMSServiceProvider.SMS_BRANDNAME) {
                sendSMSByBrandname(phone, dataSMSClientReqDTO);
            }
        });
    }

    private void sendSMSByGateway(String phone, DataSMSClientReqDTO dataSMSClientReqDTO) {
        // create headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));

        int portFromPhone = determinePortWithProviderNetwork(phone);
        if (portFromPhone == CodeDefine.SMS_GATEWAY_NOT_EXIST_PORT) {
            log.info("Không có port nào có sẵn để gửi sms:===>" + phone);
            return;
        }

        if (portFromPhone == CodeDefine.SMS_GATEWAY_NOT_DETERMINE_NETWORK) {
            log.info("Số điện thoại không thuộc nhà mạng nào:===>" + phone);
            return;
        }

        SMSRequestChildInternalDTO smsRequestChildInternalDTO = new SMSRequestChildInternalDTO();
        smsRequestChildInternalDTO.setNumber(phone);
        smsRequestChildInternalDTO.setTextParam(new String[]{dataSMSClientReqDTO.getContent()});
        smsRequestChildInternalDTO.setUserId(phone);

        SMSRequestInternalDTO smsRequestInternalDTO = new SMSRequestInternalDTO(Collections.singletonList(smsRequestChildInternalDTO));
        smsRequestInternalDTO.setPort(new Integer[]{portFromPhone});
        log.info("Data send to gw internal:===> " + smsRequestInternalDTO);

        HttpEntity<SMSRequestInternalDTO> entity = new HttpEntity<>(smsRequestInternalDTO, headers);

        try {
            ResponseEntity<SendSMSResInternalDTO> response = restTemplate.postForEntity(SMS_GATEWAY_URL + "/api/send_sms", entity, SendSMSResInternalDTO.class);
            log.info("Data receive to gw internal:===> {}", response.getBody());
            saveSMSSendHistory(dataSMSClientReqDTO, phone, response.getStatusCode());
        } catch (Exception e) {
            log.error("Error receive data from gw internal:===> {}", e.getMessage());
            String msg = "Hệ thống gửi sms có thể đang bị downtime, hãy kiểm tra " + phone;
            slackWarningSystemService.sendWarningSystemToSlack(msg, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
        }
    }

    public void sendCustomSMSByBrandname(String phone, DataSMSClientReqDTO dataSMSClientReqDTO) {
        if (StringUtils.isEmpty(BRANDNAME_NETWORK_ACCEPT_SEND)) {
            return;
        }

        phone = phone.replaceFirst(ValidationUtils.prefixPhone, ValidationUtils.locationVN);
        int result;
        String content = dataSMSClientReqDTO.getContent();
        boolean networkAcceptSend = checkNetworkAcceptSendBrandname(phone);
        if (!networkAcceptSend) {
            return;
        }

        // set lại msg để lưu lịch sử
        dataSMSClientReqDTO.setContent(content);

        log.info("=======Send sms brandname: {}", dataSMSClientReqDTO);
        result = sendMT(BRANDNAME_SENDER, phone, content);
        if (result == 0) {
            saveSMSSendHistory(dataSMSClientReqDTO, phone, HttpStatus.OK);
        } else {
            saveSMSSendHistory(dataSMSClientReqDTO, phone, HttpStatus.NOT_FOUND);
        }
    }

    private void sendSMSByBrandname(String phone, DataSMSClientReqDTO dataSMSClientReqDTO) {
        if (StringUtils.isEmpty(BRANDNAME_NETWORK_ACCEPT_SEND)) {
            return;
        }

        phone = phone.replaceFirst(ValidationUtils.prefixPhone, ValidationUtils.locationVN);
        int result;
        String content = dataSMSClientReqDTO.getContent();
        boolean networkAcceptSend = checkNetworkAcceptSendBrandname(phone);
        if (!networkAcceptSend) {
            return;
        }

        // The Coach và CRM thì sẽ lấy content từ api truyền vào.
        // Còn App TOFU thì sẽ lấy theo cấu hình config bên dưới
        if (!SMSAction.COMPLETE_ORDER.equals(dataSMSClientReqDTO.getAction()) &&
                (StringUtils.isEmpty(dataSMSClientReqDTO.getFromApp()) ||
                        CodeDefine.APP_NAME_DEFAULT.equals(dataSMSClientReqDTO.getFromApp()))) {

            if (SMSAction.FORGOT_PASSWORD.equals(dataSMSClientReqDTO.getAction())) {
                content = BRANDNAME_CONTENT_TF_FORGOT_PW;
            } else if (SMSAction.SIGN_UP.equals(dataSMSClientReqDTO.getAction())) {
                content = BRANDNAME_CONTENT_TF_SIGN_UP;
            }
        }

        // set lại msg để lưu lịch sử
        content = replaceKeyActiveCodeSMS(content, dataSMSClientReqDTO.getOtp());
        dataSMSClientReqDTO.setContent(content);

        log.info("=======Send sms brandname: {}", dataSMSClientReqDTO);
        result = sendMT(BRANDNAME_SENDER, phone, content);
    }

    private String replaceKeyActiveCodeSMS(String content, String activeCode) {
        if (content != null && activeCode != null) {
            content = content.replace("%active_code%", activeCode);
        }

        return content;
    }

    public int sendMT(String brandnameSender, String receiver, String content) {
        int result = -1;
        long startTime = System.currentTimeMillis();

        String url = "http://ams.tinnhanthuonghieu.vn:8009/bulkapi?wsdl";

        String request = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:impl=\"http://impl.bulkSms.ws/\">"
                + "<soapenv:Header/>"
                + "<soapenv:Body>"
                + "<impl:wsCpMt>"
                + "<User>" + BRANDNAME_USERNAME + "</User>"
                + "<Password>" + BRANDNAME_PASSWORD + "</Password>"
                + "<CPCode>" + BRANDNAME_CPCODE + "</CPCode>"
                + "<RequestID>" + "1" + "</RequestID>"
                + "<UserID>" + receiver + "</UserID>"
                + "<ReceiverID>" + receiver + "</ReceiverID>"
                + "<ServiceID>" + brandnameSender + "</ServiceID>"
                + "<CommandCode>" + "bulksms" + "</CommandCode>"
                + "<Content>" + content + "</Content>"
                + "<ContentType>" + 0 + "</ContentType>"
                + "</impl:wsCpMt>"
                + "</soapenv:Body>"
                + "</soapenv:Envelope>";

        PostMethod post = null;
        try {
            Protocol protocol = new Protocol(url);
            HttpClient httpclient = new HttpClient();
            HttpConnectionManager conMgr = httpclient.getHttpConnectionManager();
            HttpConnectionManagerParams conPars = conMgr.getParams();
            conPars.setConnectionTimeout(20000);
            conPars.setSoTimeout(60000);
            post = new PostMethod(protocol.getUrl());

            RequestEntity entity = new StringRequestEntity(request, "text/xml", "UTF-8");
            post.setRequestEntity(entity);
            post.setRequestHeader("SOAPAction", "");
            httpclient.executeMethod(post);
            InputStream is = post.getResponseBodyAsStream();
            String response = null;
            if (is != null) {
                response = getStringFromInputStream(is);
            }
            log.info("Call sendMT response: ======================" + response);

            if (response != null && !response.equals("")) {
                if (response.contains("<result>")) {
                    int start = response.indexOf("<result>") + "<result>".length();
                    int end = response.lastIndexOf("</result>");
                    String responseCode = response.substring(start, end);
                    if (responseCode.equalsIgnoreCase("1")) {
                        result = 0; //call success
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error send sms brandname ============" + e);
        } finally {
            if (post != null) {
                post.releaseConnection();
            }
        }

        log.info("Finish sendMT in " + (System.currentTimeMillis() - startTime) + " ms");
        return result;
    }

    private static String getStringFromInputStream(InputStream is) {
        BufferedReader br = null;
        StringBuilder sb = new StringBuilder();

        String line;
        try {
            br = new BufferedReader(new InputStreamReader(is));
            while ((line = br.readLine()) != null) {
                sb.append(line);
            }
        } catch (IOException e) {
            System.err.println(e);
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    System.err.println(e);
                }
            }
        }

        return sb.toString();
    }

    private void saveSMSSendHistory(DataSMSClientReqDTO dataSMSClientReqDTO, String phone, HttpStatus httpStatus) {
        SMSSendHistory smsSendHistory = new SMSSendHistory();
        phone = ValidationUtils.refactorPhone(phone);
        if (dataSMSClientReqDTO.getOtp() != null) {
            smsSendHistory.setOtp(dataSMSClientReqDTO.getOtp());
        }

        if (dataSMSClientReqDTO.getAction() != null) {
            smsSendHistory.setAction(dataSMSClientReqDTO.getAction());
        }

        if (!StringUtils.isEmpty(dataSMSClientReqDTO.getFromApp())) {
            smsSendHistory.setFromApp(dataSMSClientReqDTO.getFromApp());
        }

        smsSendHistory.setSmsServiceProvider(dataSMSClientReqDTO.getSmsServiceProvider());

        smsSendHistory.setToPhone(phone);
        smsSendHistory.setContent(dataSMSClientReqDTO.getContent());
        smsSendHistory.setStatus(httpStatus == HttpStatus.OK);
        smsSendHistory.setNetwork(getNetWorkByPhone(phone));
        smsSendHistory.setVerified(false);
        smsSendHistory.setIp(dataSMSClientReqDTO.getIp());
        smsSendHistoryRepository.save(smsSendHistory);
    }

    public boolean checkVerifiedSMSByPhone(String phone, String smsAction) {
        var smsSendHistories = smsSendHistoryRepository.findSMSSendHistoriesByToPhone(phone, smsAction);
        return !smsSendHistories.isEmpty();
    }

    public boolean verifySMSByPhoneAndOTP(String phone, String otp, String smsAction) {
        List<SMSSendHistory> smsSendHistories = smsSendHistoryRepository.findSMSSendHistoriesByToPhoneAndOtpAndAction(phone, otp, smsAction);

        if (smsSendHistories.isEmpty()) {
            return false;
        }

        // thực hiện update verified
        smsSendHistories.forEach(smsSendHistory -> smsSendHistory.setVerified(true));
        smsSendHistoryRepository.saveAll(smsSendHistories);
        return true;
    }

    public boolean checkPhoneVerifyOTP(String phone) {
        return smsSendHistoryRepository.existsPhoneVerifyOTP(phone);
    }

    private Integer determinePortWithProviderNetwork(String phone) {
        List<Integer> listPortVT = null;
        List<Integer> listPortVina = null;
        List<Integer> listPortMobi = null;
        List<Integer> listPortVietnamobile = null;
        List<Integer> listPortGtel = null;
        List<Integer> listPortOther = null;
        List<SMSCellular> SMSCellulars = smsCellularRepository.findAll();

        for (SMSCellular smsCellular : SMSCellulars) {
            if (SMSNetworkName.VIETTEL.name().equals(smsCellular.getCellularName())) {
                listPortVT = smsCellular.getListPort();
            }

            if (SMSNetworkName.VINA.name().equals(smsCellular.getCellularName())) {
                listPortVina = smsCellular.getListPort();
            }

            if (SMSNetworkName.MOBI.name().equals(smsCellular.getCellularName())) {
                listPortMobi = smsCellular.getListPort();
            }

            if (SMSNetworkName.VIETNAMOBILE.name().equals(smsCellular.getCellularName())) {
                listPortVietnamobile = smsCellular.getListPort();
            }

            if (SMSNetworkName.GTEL.name().equals(smsCellular.getCellularName())) {
                listPortGtel = smsCellular.getListPort();
            }

            if (SMSNetworkName.OTHER.name().equals(smsCellular.getCellularName())) {
                listPortOther = smsCellular.getListPort();
            }
        }

        if (!StringUtils.isEmpty(phone)) {
            Random rand = new Random();
            String network = getNetWorkByPhone(phone);
            if (SMSNetworkName.VIETTEL.name().equals(network) && listPortVT != null) {
                return listPortVT.get(rand.nextInt(listPortVT.size()));
            } else if (SMSNetworkName.VINA.name().equals(network) && listPortVina != null) {
                return listPortVina.get(rand.nextInt(listPortVina.size()));
            } else if (SMSNetworkName.MOBI.name().equals(network) && listPortMobi != null) {
                return listPortMobi.get(rand.nextInt(listPortMobi.size()));
            } else if (SMSNetworkName.VIETNAMOBILE.name().equals(network) && listPortVietnamobile != null) {
                return listPortVietnamobile.get(rand.nextInt(listPortVietnamobile.size()));
            } else if (SMSNetworkName.GTEL.name().equals(network) && listPortGtel != null) {
                return listPortGtel.get(rand.nextInt(listPortGtel.size()));
            } else if (SMSNetworkName.OTHER.name().equals(network) && listPortOther != null) {
                return listPortOther.get(rand.nextInt(listPortOther.size()));
            }

            return CodeDefine.SMS_GATEWAY_NOT_EXIST_PORT;
        }

        return CodeDefine.SMS_GATEWAY_NOT_DETERMINE_NETWORK;
    }

    private String getNetWorkByPhone(String phone) {
        phone = ValidationUtils.refactorPhone(phone);

        List<String> patternVT = Arrays.asList("096", "097", "098", "032", "033", "034", "035", "036", "037", "038", "039", "086");
        List<String> patternVina = Arrays.asList("091", "094", "081", "082", "083", "084", "085", "088");
        List<String> patternMobi = Arrays.asList("090", "093", "070", "076", "077", "078", "079", "089");
        List<String> patternVietnamobile = Arrays.asList("052", "056", "058", "092");
        List<String> patternGtel = Arrays.asList("059", "099");
        List<String> patternOther = Arrays.asList("02", "087", "1900", "1800", "055");

        if (!StringUtils.isEmpty(phone)) {
            if (patternVT.contains(phone.substring(0, 3))) {
                return SMSNetworkName.VIETTEL.name();
            } else if (patternVina.contains(phone.substring(0, 3))) {
                return SMSNetworkName.VINA.name();
            } else if (patternMobi.contains(phone.substring(0, 3))) {
                return SMSNetworkName.MOBI.name();
            } else if (patternVietnamobile.contains(phone.substring(0, 3))) {
                return SMSNetworkName.VIETNAMOBILE.name();
            } else if (patternGtel.contains(phone.substring(0, 3))) {
                return SMSNetworkName.GTEL.name();
            } else if (patternOther.contains(phone.substring(0, 3))) {
                return SMSNetworkName.OTHER.name();
            }
        }

        return null;
    }

    private String randomStringSMS(int numberWord) {
        List<String> stringWords = Arrays.asList("weekday", "press", "turn on", "get up", "role", "noon", "typical", "usual", "routine", "arrive", "count", "pull", "elevator", "hang", "shower", "reach", "refill", "iron", "upstairs", "shut", "dust", "tend", "flow", "sweep", "ticket", "laundry", "put on", "fold", "come back", "shift", "tough", "slip", "run out", "slightly", "fill up", "phase", "awake", "spill", "sit around", "rest", "break", "knock", "series", "stay up", "sleepy", "stretch", "neighbor", "garbage", "spare", "leisure", "snooze", "alone", "breath", "sleep in", "flush", "push", "bother", "sunrise", "sunset", "midnight", "estimate", "workload", "agenda", "almost", "decline", "assist", "outcome", "discipline", "productive", "necessary", "practice", "patient", "grateful", "workplace", "amazing", "plan ahead", "tip", "news", "minute", "quit", "period", "arrange", "follow up", "burnout", "efficient", "exercise", "jogging", "calendar", "hang out", "focus", "personal", "birth", "gender", "male", "female", "code", "permanent", "information", "district", "grow up", "recently", "identity", "additional", "hometown", "middle", "fill out", "interview", "northern", "southern", "province", "photograph", "live with", "introduction", "favorite", "previous", "form", "nationality", "sign", "type", "correct", "future", "attend", "struggle", "achievement", "inspiration", "detail", "freelance", "major", "engineer", "experience", "occupation", "primary", "acquire", "university", "abroad", "grade", "career", "degree", "suitable", "background", "passion", "several", "fit in", "specific", "dream", "suppose", "motivation", "decide", "developer", "specialist", "position", "ability", "hire", "evaluate", "temporary", "goal", "company", "prepare", "duty", "insurance", "process", "official", "potential", "relevant", "bonus", "stand out", "assess", "department", "mission", "look forward to", "training", "rule", "salary", "failure", "lesson", "qualify", "weakness", "condition", "appointment", "offer", "put off", "report", "feedback", "client", "call off", "meeting", "approve", "presentation", "solution", "function", "strategy", "conference", "hand over", "folder", "equipment", "member", "deadline", "colleague", "stamp", "retire", "internship", "secretary", "fault", "print", "carry out", "assistant", "expert", "involve", "due", "handle", "assignment", "include", "subject", "exam", "textbook", "semester", "hand in", "project", "notebook", "blank", "instruction", "example", "complete", "possible", "topic", "certificate", "turn to", "summary", "fall behind", "improve", "expect", "lecture", "history", "literature", "chemistry", "math", "physics", "curious", "final", "result", "uniform", "scholarship", "fee", "faculty", "tool", "guide", "freshman", "absent", "thesis", "club", "event", "campus", "transcript", "get into", "research", "graduate", "knowledge", "curriculum", "join", "course", "professor", "principal", "prize", "excellent", "cross out", "laboratory", "calculator", "education", "drop out", "tutor", "gift", "get along", "advice", "trust", "fond of", "relationship", "roommate", "difference", "loyal", "comfortable", "harmony", "text", "mention", "familiar", "friendship", "reliable", "gossip", "apart", "joy", "doubt", "argue", "forever", "conversation", "dear", "remain", "stranger", "count on", "laugh", "reason", "easy-going", "plan", "split", "takeout", "reserve", "service", "awesome", "terrible", "customer", "bill", "free", "rare", "chef", "vendor", "order", "full", "catch up", "book", "available", "mix up", "rather", "message", "apologize", "eat out", "affordable", "option", "convenient", "go out", "meet up", "pick up", "suggest", "raise", "relative", "twin", "behavior", "share", "take after", "senior", "remind", "gap", "provide", "care", "grandfather", "get together", "responsible", "childhood", "allow", "generation", "communicate", "strict", "close", "uncle", "peace", "discuss", "household", "similar", "encourage", "parent", "principle", "cousin", "bring up", "secret", "hope", "wish", "attention", "stress", "desire", "reaction", "issue", "scary", "fear", "toxic", "individual", "surprised", "pleasant", "shy", "anxiety", "excited", "happiness", "reach out", "lonely", "upset", "angry", "understand", "find out", "cheer up", "mood", "burden", "nervous", "release", "regret", "enormous", "mess", "resist", "realize", "go through", "survive", "blame", "step", "stage", "embrace", "suffer", "sure", "power", "mindset", "give up", "guarantee", "overcome", "growth", "develop", "attempt", "emotional", "bitter", "commit", "lead", "approach", "complicated", "come up with", "horrible", "courage", "extreme", "joke", "natural", "simple", "friendly", "especially", "perfect", "positive", "pretty", "negative", "handsome", "helpful", "serious", "smart", "height", "whenever", "describe", "compare", "bangs", "clever", "lucky", "attitude", "careful", "scar", "genius", "humor", "ordinary", "figure", "accept", "independent", "wisdom", "swallow", "sour", "tongue", "stir", "beef", "onion", "corn", "steak", "spicy", "garlic", "pork", "slice", "bowl", "juicy", "pour", "microwave", "pot", "fry", "lime", "fridge", "mango", "rib", "shrimp", "bland", "potato", "seafood", "quick", "snack", "pepper", "heat", "tray", "chopstick", "lotus", "durian", "cuisine", "spice", "brunch", "noodle", "sticky", "sauce", "delicious", "bacon", "cheese", "dessert", "bite", "alcohol", "taste", "barbecue", "flour", "bottle", "raw", "honey", "flavor", "unique", "roll", "local", "stew", "guest", "fork", "pickle", "organic", "steam", "portion", "skip", "homemade", "meal", "healthy", "science", "ingredient", "cereal", "recipe", "whole", "bake", "vegetable", "herb", "lean", "cut down on", "amount", "yogurt", "boil", "total", "oil", "salmon", "juice", "mix", "oven", "essential", "enough", "contain", "avoid", "throat", "disease", "pill", "medicine", "catch", "pregnant", "emergency", "temperature", "pain", "nutrient", "pharmacy", "wheelchair", "prescription", "level", "clinic", "system", "dangerous", "blood", "badly", "hurt", "look after", "flu", "surgery", "stomach", "headache", "throw up", "wound", "sick", "dentist", "treatment", "knee", "neck", "shoulder", "wrist", "injury", "health", "safety", "cancer", "cough", "frequent", "brain", "stiff", "protect", "bone", "cure", "recover", "ankle", "method", "maintain", "lift", "grab", "bend", "work out", "massage", "train", "warm up", "cool down", "fitness", "elbow", "gain", "airport", "trip", "sunglasses", "look around", "satisfy", "trap", "transit", "passport", "check out", "resort", "bridge", "nature", "top", "get away", "ancient", "crew", "delay", "reservation", "overnight", "deposit", "map", "departure", "destination", "attraction", "cave", "lobby", "travel", "cruise", "pillow", "wooden", "tourist", "buddy", "craft", "brochure", "backpacking", "advantage", "booking", "duty-free", "hiking", "sand", "honeymoon", "tour", "border", "airline", "announce", "center", "island", "belt", "ideal", "discover", "flexible", "double", "facility", "adult", "cancel", "admission", "impressive", "expense", "intend", "relax", "recall", "turn out", "happen", "crisis", "calm", "lifetime", "ago", "imagine", "incident", "successful", "actually", "cause", "main", "admit", "reflect", "slowly", "disappear", "wonder", "opportunity", "miracle", "fate", "fantastic", "panic", "precious", "disagree", "immediate", "nightmare", "exact", "complaint", "awkward", "withdraw", "borrow", "add", "price", "own", "value", "cost", "range", "average", "transfer", "discount", "increase", "extra", "set aside", "worth", "monthly", "drop", "pay", "spend", "reasonable", "credit", "proper", "give away", "receipt", "save up", "valuable", "decrease", "afford", "wealth", "help out", "narrow", "size", "carry", "liquid", "heavy", "material", "weight", "wide", "crack", "volume", "huge", "length", "sharp", "surface", "manual", "empty", "metal", "deep", "medium", "remove", "variety", "repair", "lose", "quantity", "separate", "plastic", "stick", "straight", "clearly", "depth", "ending", "scene", "action", "angle", "thriller", "brilliant", "drama", "documentary", "screen", "dialogue", "character", "adventure", "fiction", "crime", "musical", "historical", "studio", "soundtrack", "show", "wonderful", "setting", "odd", "mystery", "actress", "director", "come out", "hit", "confused", "actor", "reveal", "viral", "honor", "showbiz", "award", "channel", "subscribe", "well-known", "admire", "live up to", "funny", "media", "modest", "live", "rumor", "scandal", "gifted", "speak out", "charity", "become", "past", "performance", "currently", "global", "fan", "volunteer", "biography", "recognize", "scream", "look up to", "deserve", "collection", "vlog", "tutorial", "photography", "holiday", "cartoon", "indoor", "concert", "guess", "go in for", "comedy", "novel", "cooperate", "puzzle", "knit", "pleasure", "meaningful", "ride", "participate", "interested", "sail", "camping", "program", "try out", "climb", "tent", "team", "keen", "genre", "prefer", "professional", "score", "match", "fishing", "winner", "tournament", "hero", "title", "player", "golf", "annual", "maximum", "target", "athlete", "basketball", "opponent", "strike", "unfortunately", "league", "championship", "appreciate", "athletics", "cycling", "surf", "wrestling", "stadium", "beat", "net", "coach", "captain", "product", "light", "edge", "shave", "layer", "chin", "shampoo", "perfume", "mirror", "underneath", "hair dryer", "visible", "forehead", "smooth", "acne", "cheek", "comb", "gorgeous", "combination", "nail", "style", "powder", "spray", "moisture", "dye", "lipstick", "wipe", "cosmetics", "sunscreen", "sensitive", "receive", "shop around", "checkout", "sell", "item", "purse", "souvenir", "take back", "quality", "import", "cashier", "line up", "deal", "bunch", "delivery", "exchange", "cart", "promotion", "label", "mall", "refund", "coupon", "brand", "package", "request", "cut back on", "purchase", "fake", "sell out", "payment", "curve", "sleeve", "shape", "try on", "store", "fashion", "leather", "pocket", "diamond", "pattern", "button", "piece", "suit", "show off", "tight", "pants", "tie", "go with", "cool", "bracelet", "colorful", "jewelry", "chic", "beauty", "collar", "loose", "closet", "sock", "waist", "wrap up", "pray", "treasure", "invite", "story", "culture", "modern", "traditional", "recommend", "religion", "explain", "luck", "temple", "crowd", "western", "mixture", "festival", "polite", "museum", "husband", "national", "belong to", "purpose", "proud", "majority", "spirit", "rude", "manner", "worldwide", "legend", "accent", "death", "marry", "mask", "party", "ring", "die out", "generous", "strange", "instead", "society", "regard", "moral", "firework", "pass down", "anthem", "various", "display", "celebrate", "flag", "minor", "ceremony", "clothing", "offensive", "mostly", "difficulty", "candle", "royal", "problem", "highlight", "envelope", "blush", "fall for", "letter", "contact", "ask out", "instantly", "attractive", "opposite", "bride", "groom", "moment", "marriage", "intention", "forgive", "couple", "situation", "feeling", "weird", "warm", "excuse", "obviously", "anniversary", "willing", "break up", "steady", "romantic", "connection", "lie", "wedding", "gentle", "port", "distance", "one-way", "get around", "return", "enter", "engine", "direct", "continue", "direction", "flight", "parking", "truck", "take off", "route", "carry-on", "gate", "arrival", "passenger", "station", "luggage", "accident", "speed", "helmet", "wheel", "brake", "cross", "highway", "intersection", "ferry", "property", "central", "traffic", "lane", "theater", "bookstore", "building", "chain", "neighborhood", "wander", "pace", "pedestrian", "noise", "sidewalk", "tower", "playground", "represent", "police", "alley", "run-down", "dense", "grocery", "transportation", "crowded", "move in", "random", "accessible", "diverse", "underground", "show around", "pipe", "tissue", "electricity", "entrance", "soap", "mouse", "ceiling", "throw away", "neat", "move out", "put away", "hole", "mattress", "furniture", "turn off", "owner", "bin", "shelf", "pool", "bright", "corner", "put back", "apartment", "seat", "luxury", "backyard", "lock", "pan", "slide", "transparent", "stream", "insect", "cottage", "lovely", "farmer", "countryside", "vacation", "peaceful", "rural", "tropical", "vibrant", "forest", "livestock", "soil", "mud", "resource", "branch", "atmosphere", "path", "valley", "buffalo", "head to", "field", "environment", "zone", "cattle", "hill", "harvest", "agriculture", "seed", "run out of", "habitat", "pet", "adopt", "scratch", "breed", "fur", "wild", "wing", "giant", "hunt", "lion", "race", "feed", "clean up", "cage", "dirty", "silent", "rough", "spacious", "enjoy", "grass", "goat", "biology", "adjust", "intelligent", "enemy", "kingdom", "encounter", "hungry", "freeze", "lake", "season", "fall", "spring", "earth", "ground", "coast", "storm", "desert", "cloudy", "sight", "unusual", "wave", "pour down", "thunder", "shelter", "earthquake", "slope", "mild", "brighten up", "accurate", "severe", "disaster", "crop", "geography", "destroy", "sunny", "lightning", "predict", "artist", "blend", "soul", "flute", "exhibition", "puppet", "sculpture", "absolutely", "painter", "design", "talented", "rhythm", "musician", "headphones", "classical", "tone", "chart", "contest", "record", "drum", "century", "poetry", "instrument", "extraordinary", "folk", "statue", "theme", "sing along", "symbol", "tune", "article", "analysis", "factor", "episode", "original", "part", "convince", "existence", "chance", "rate", "adequate", "author", "concern", "conclusion", "frustrated", "assumption", "replace", "magic", "completely", "control", "honest", "inside", "throughout", "disappointed", "justice", "look into", "stand for", "structure", "passage", "pick", "pay off", "set up", "supplier", "terms", "investment", "financial", "tax", "trade", "cash", "percentage", "obtain", "goods", "stock", "currency", "earn", "leader", "region", "license", "industry", "profit", "aim", "economic", "scale", "shortage", "scarce", "extend", "experiment", "emphasize", "contribute", "battle", "view", "personally", "truth", "comment", "opinion", "seem", "agree", "argument", "totally", "definitely", "claim", "freedom", "debate", "theory", "choice", "optimistic", "fair", "sense", "mind", "divide", "realistic", "sympathy", "quite", "witness", "useful", "incredible", "version", "statement", "perhaps", "believe", "language", "basic", "word", "chat", "define", "foreign", "regular", "dictionary", "verb", "beginner", "sentence", "slow down", "interact", "sound", "speaker", "meaning", "forget", "fluent", "long-term", "journey", "technique", "concept", "phrase", "translate", "context", "native", "confident", "spell", "grammar", "note", "network", "site", "online", "post", "software", "link", "technology", "advanced", "access", "board", "look up", "blog", "space", "icon", "update", "finger", "communication", "switch", "connect", "sign up", "cable", "visitor", "solve", "reply", "download", "innovative", "keyword", "signal", "notification", "click", "able", "automatic", "library", "machine", "limit", "permit", "page", "policy", "storage", "data", "thief", "stable", "select", "warning", "satellite", "robot", "weapon", "rocket", "account", "back up", "valid", "attach", "search", "publish", "edit", "profile", "respond", "standard", "survey", "status", "rob", "prison", "escape", "murder", "divorce", "chase", "guilty", "attack", "criminal", "lawyer", "hide", "government", "court", "case", "security", "law", "evidence", "victim", "violent", "abuse", "cruel", "break into", "legal", "liberty", "spread", "rapid", "poverty", "ultimate", "seek", "presence", "climate", "rescue", "planet", "sewage", "species", "extinction", "conservation", "campaign", "union", "population", "exist", "vote", "donate", "powerful", "army", "election", "president", "organization", "politics", "pilot", "movement", "occur", "massive", "revolution", "soldier", "medal", "magazine", "force", "section", "sample");
        Collections.shuffle(stringWords);
        return stringWords.subList(0, numberWord).toString();
    }

    /**
     * Khởi tạo quota mỗi tháng cho service sms
     * Firebase
     * Brandname
     * SMS cong ty
     */
    private void initSMSQuotaEveryMonth() {
        int numberYearMonth = DateUtils.getCurrentYearMonthNumber();
        List<SMSQuota> smsQuotas = new ArrayList<>();
        SMSQuota smsQuotaFirebase = smsQuotaRepository.findSMSQuotaByDatetimeAndService(numberYearMonth, SMSServiceProvider.SMS_FIREBASE.getType());
        if (smsQuotaFirebase == null) {
            smsQuotaFirebase = SMSQuota.builder()
                    .service(SMSServiceProvider.SMS_FIREBASE.getType())
                    .datetime(numberYearMonth)
                    .quota(13_500)
                    .used(0)
                    .build();

            smsQuotas.add(smsQuotaFirebase);
        }

        SMSQuota smsQuotaBrandname = smsQuotaRepository.findSMSQuotaByDatetimeAndService(numberYearMonth, SMSServiceProvider.SMS_BRANDNAME.getType());
        if (smsQuotaBrandname == null) {
            smsQuotaBrandname = SMSQuota.builder()
                    .service(SMSServiceProvider.SMS_BRANDNAME.getType())
                    .datetime(numberYearMonth)
                    .quota(50_000)
                    .used(0)
                    .build();

            smsQuotas.add(smsQuotaBrandname);
        }

        SMSQuota smsQuotaSMSCompany = smsQuotaRepository.findSMSQuotaByDatetimeAndService(numberYearMonth, SMSServiceProvider.SMS_COMPANY.getType());
        if (smsQuotaSMSCompany == null) {
            smsQuotaSMSCompany = SMSQuota.builder()
                    .service(SMSServiceProvider.SMS_COMPANY.getType())
                    .datetime(numberYearMonth)
                    .quota(10_000)
                    .used(0)
                    .build();

            smsQuotas.add(smsQuotaSMSCompany);
        }

        if (!smsQuotas.isEmpty()) {
            smsQuotaRepository.saveAll(smsQuotas);
        }
    }

    private boolean isSMSQuotaLimit(String service) {
        int numberYearMonth = DateUtils.getCurrentYearMonthNumber();
        SMSQuota smsQuota = smsQuotaRepository.findSMSQuotaByDatetimeAndService(numberYearMonth, service);

        if (smsQuota == null) {
            initSMSQuotaEveryMonth();

            smsQuota = smsQuotaRepository.findSMSQuotaByDatetimeAndService(numberYearMonth, service);
            return smsQuota.getUsed() >= smsQuota.getQuota();
        }

        return smsQuota.getUsed() >= smsQuota.getQuota();
    }

    private boolean checkNetworkAcceptSendBrandname(String phone) {
        List<String> networkAcceptSend = new ArrayList<>(Arrays.asList(BRANDNAME_NETWORK_ACCEPT_SEND.split(",")));
        if (!networkAcceptSend.contains(getNetWorkByPhone(phone))) {
            return false;
        }
        return true;
    }

    public boolean isAcceptSendBrandname(String phone, String service) {
        return checkNetworkAcceptSendBrandname(phone) && isSMSQuotaLimit(service);
    }

    public DataResponseDTO<Void> sendPurchaseConfirmation(String phone) {
        log.info("Sending purchase confirmation SMS to user: {}", phone);

        phone = phone.replaceFirst(ValidationUtils.prefixPhone, ValidationUtils.locationVN);
        int result = sendMT(BRANDNAME_SENDER, phone, BRANDNAME_CONTENT_PURCHASE_CONFIRMATION);

        if (result == 0) {
            return new DataResponseDTO<>(200, "SMS sent successfully", null);
        } else {
            return new DataResponseDTO<>(400, "Failed to send SMS", null);
        }
    }
}
