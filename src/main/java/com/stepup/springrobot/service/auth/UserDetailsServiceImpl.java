package com.stepup.springrobot.service.auth;

import com.stepup.springrobot.common.ValidationUtils;
import com.stepup.springrobot.exception.business.user.UserNotFoundException;
import com.stepup.springrobot.security.CustomUserDetails;
import com.stepup.springrobot.model.user.User;
import com.stepup.springrobot.repository.auth.UserRepository;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

@Log4j2
@Component
public class UserDetailsServiceImpl implements UserDetailsService {
    @Autowired
    private UserRepository userRepository;

    @Override
    public UserDetails loadUserByUsername(String phone) throws UsernameNotFoundException {
        phone = ValidationUtils.validateAndNormalizePhone(phone);
        log.debug("Entering in loadUserByUsername Method...");
        User user = userRepository.findByPhone(phone);
        if(user == null){
            log.error("Phone not found: " + phone);
            throw new UserNotFoundException();
        }

        log.info("User Authenticated Successfully..!!!: {}, {}", user.getPhone(), user.getId());
        return new CustomUserDetails(user);
    }
}
