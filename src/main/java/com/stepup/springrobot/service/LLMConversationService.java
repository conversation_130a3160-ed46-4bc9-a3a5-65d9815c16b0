package com.stepup.springrobot.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.config.ConfigUtil;
import com.stepup.springrobot.dto.chat.ConversationIdAndUserIdDTO;
import com.stepup.springrobot.dto.llm.LlmInitConversationResDTO;
import com.stepup.springrobot.dto.llm_conversation.ChatReqDTO;
import com.stepup.springrobot.dto.llm_conversation.InitConversationReqDTO;
import com.stepup.springrobot.dto.llm_conversation.LLMChatResDTO;
import com.stepup.springrobot.model.chat.ConversationLogType;
import lombok.extern.log4j.Log4j2;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.TimeUnit;

@Log4j2
@Service
public class LLMConversationService {
    @Value("${llm_host_name}")
    private String llmHostName;

    @Value("${llm_init_uri}")
    private String llmInitUri;

    @Value("${llm_summary_uri}")
    private String llmSummaryUri;

    @Value("${llm_webhook_uri}")
    private String llmWebhookUri;

    private final ObjectMapper objectMapper;

    private final SlackWarningSystemService slackWarningSystemService;

    private final SharedService sharedService;

    @Autowired
    public LLMConversationService(ObjectMapper objectMapper, SlackWarningSystemService slackWarningSystemService, SharedService sharedService) {
        this.objectMapper = objectMapper;
        this.slackWarningSystemService = slackWarningSystemService;
        this.sharedService = sharedService;
    }

    public LlmInitConversationResDTO initConversation(String conversationId, String userId, Long botId) throws IOException {
        try {
            OkHttpClient client = new OkHttpClient.Builder()
                    .connectTimeout(20, TimeUnit.SECONDS)
                    .writeTimeout(20, TimeUnit.SECONDS)
                    .readTimeout(20, TimeUnit.SECONDS)
                    .build();

            InitConversationReqDTO reqDTO = InitConversationReqDTO.builder()
                    .botId(botId)
                    .userId(userId)
                    .conversationId(conversationId)
                    .build();

            RequestBody body = RequestBody.create(
                    okhttp3.MediaType.parse("application/json"), objectMapper.writeValueAsBytes(reqDTO));

            Request request = new Request.Builder()
                    .url(llmHostName + llmInitUri)
                    .post(body)
                    .build();

            Response response = client.newCall(request).execute();
            String responseString = response.body().string();
            log.info("=== Init LLM conversation: {}, url: {}", llmHostName + llmInitUri, responseString);
            return objectMapper.readValue(responseString, new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error when init LLM conversation", e);
            slackWarningSystemService.sendWarningSystemToSlack("Error when init LLM conversation: " + e.getMessage(), ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
            throw e;
        }
    }

    public LLMChatResDTO getConversationResponse(Long conversationId, String llmConversationId, String userAnswer, String userAudio) throws IOException {
        try {
            Instant start = Instant.now();
            OkHttpClient client = new OkHttpClient.Builder()
                    .connectTimeout(20, TimeUnit.SECONDS)
                    .writeTimeout(20, TimeUnit.SECONDS)
                    .readTimeout(20, TimeUnit.SECONDS)
                    .build();

            ChatReqDTO reqDTO = ChatReqDTO.builder()
                    .message(userAnswer)
                    .conversationId(llmConversationId)
                    .audioUrl(StringUtils.isEmpty(userAudio) ? null : userAudio)
                    .build();

            RequestBody body = RequestBody.create(
                    okhttp3.MediaType.parse("application/json"), objectMapper.writeValueAsBytes(reqDTO));

            Request request = new Request.Builder()
                    .url(llmHostName + llmWebhookUri)
                    .post(body)
                    .build();

            Response response = client.newCall(request).execute();
            String responseString = response.body().string();
            sharedService.saveConversationLog(conversationId, ConversationLogType.LLM_RESPONSE, responseString, Duration.between(start, Instant.now()).toMillis());
            log.info("=== Get response LLM conversation: {}", responseString);
            LLMChatResDTO LLMChatResDTO = objectMapper.readValue(responseString, new TypeReference<>() {
            });

            if (LLMChatResDTO == null) {
                throw new RuntimeException("Response lỗi: " + responseString);
            }

            return LLMChatResDTO;
        } catch (Exception e) {
            log.error("Error when get response LLM conversation", e);
            slackWarningSystemService.sendWarningSystemToSlack("Error when get response LLM conversation: " + e.getMessage(), ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
            throw e;
        }
    }

    /**
     * Generate conversation summary by calling external API
     *
     * @param conversationData contain externalId and userId
     */
    public String generateConversationSummary(ConversationIdAndUserIdDTO conversationData) {
        String conversationId = conversationData.getExternalId();
        String userId = conversationData.getUserId();

        try {
            // Create HTTP client with timeouts
            OkHttpClient client = new OkHttpClient.Builder()
                    .connectTimeout(20, java.util.concurrent.TimeUnit.SECONDS)
                    .writeTimeout(20, java.util.concurrent.TimeUnit.SECONDS)
                    .readTimeout(20, java.util.concurrent.TimeUnit.SECONDS)
                    .build();

            String jsonBody = String.format(
                    "{\"conversation_id\":\"%s\",\"user_id\":\"%s\"}",
                    conversationId, userId
            );

            Request request = new Request.Builder()
                    .url(llmHostName + llmSummaryUri)
                    .post(RequestBody.create(okhttp3.MediaType.parse("application/json"), jsonBody))
                    .addHeader("Content-Type", "application/json")
                    .build();

            // Execute request
            Response response = client.newCall(request).execute();

            if (response.isSuccessful()) {
                return response.body() != null ? response.body().string() : "";
            } else {
                String errorBody = response.body() != null ? response.body().string() : "No response body";
                logError(userId, new RuntimeException("HTTP " + response.code()),
                        "Failed to generate conversation summary. Response: " + errorBody);
                throw new RuntimeException("Failed to call external API to generate conversation summary. Response: " + errorBody);
            }

        } catch (Exception e) {
            logError(userId, e, "Error generating conversation summary for conversation: " + conversationId);
            throw new RuntimeException("Error generating conversation summary for conversation: " + conversationId + e.getMessage());
        }
    }

    protected void logError(String userId, Exception e, String message) {
        log.error("Client: {} - {}", userId, message, e);
    }
}
