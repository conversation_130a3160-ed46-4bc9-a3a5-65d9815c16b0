package com.stepup.springrobot.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.auth.AccountInfoDTO;
import com.stepup.springrobot.dto.entrance_test.EntranceTestDataResDTO;
import com.stepup.springrobot.dto.entrance_test.EntranceTestDetailDTO;
import com.stepup.springrobot.dto.entrance_test.SaveEntranceDetailReqDTO;
import com.stepup.springrobot.exception.business.onboard.IncompleteOnboardingException;
import com.stepup.springrobot.exception.business.request.ConflictEntityException;
import com.stepup.springrobot.model.entrance_test.EntranceTestDetail;
import com.stepup.springrobot.model.entrance_test.EntranceTestDetailResult;
import com.stepup.springrobot.model.entrance_test.EntranceTestProfile;
import com.stepup.springrobot.model.user.Profile;
import com.stepup.springrobot.repository.auth.ProfileRepository;
import com.stepup.springrobot.repository.entrance_test.EntranceTestDetailRepository;
import com.stepup.springrobot.repository.entrance_test.EntranceTestDetailResultRepository;
import com.stepup.springrobot.repository.entrance_test.EntranceTestProfileRepository;
import com.stepup.springrobot.security.JwtService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class EntranceTestService extends CommonService {
    @Autowired
    private EntranceTestDetailRepository entranceTestDetailRepository;

    @Autowired
    private EntranceTestDetailResultRepository entranceTestDetailResultRepository;

    @Autowired
    private EntranceTestProfileRepository entranceTestProfileRepository;

    @Autowired
    private ProfileRepository profileRepository;

    protected EntranceTestService(ObjectMapper objectMapper, UploadFileToS3 uploadFileToS3, JwtService jwtService, SlackWarningSystemService slackWarningSystemService) {
        super(objectMapper, uploadFileToS3, jwtService, slackWarningSystemService);
    }


    public DataResponseDTO<?> getEntranceTestData(HttpServletRequest request, String deviceId, boolean isOpenApi) throws IOException {
        AccountInfoDTO accountInfoDTO = getAccountInfo(request, deviceId, isOpenApi);
        String userId = isOpenApi ? null : accountInfoDTO.getUserData().getUserId();
        deviceId = accountInfoDTO.getDeviceId();

        Profile currentProfile = isOpenApi
                ? profileRepository.getCurrentProfileByDeviceId(deviceId)
                : profileRepository.getCurrentProfileByUserId(userId);
        if (currentProfile == null) {
            throw new IncompleteOnboardingException();
        }

        EntranceTestDataResDTO entranceTestDataResDTO = EntranceTestDataResDTO.builder()
                .image("https://smedia.stepup.edu.vn/robot/entrance_test/robot-avatar.png")
                .title("Hoàn thành bài test đầu vào")
                .description("Sau khi thực hiện bài test ngắn để ROBOT AI tạo ra một giáo trình phù hợp nhất với khả năng và sở thích của bé.")
                .build();

        Integer turnPlay;
        List<EntranceTestProfile> entranceTestProfiles = entranceTestProfileRepository.findByProfileIdOrderByIdAsc(currentProfile.getId());
        EntranceTestProfile incompleteTest = entranceTestProfiles.stream().filter(e -> !e.getIsComplete()).findFirst().orElse(null);
        List<EntranceTestDetail> testDetails = entranceTestDetailRepository.findAllTestDetails();
        if (incompleteTest == null) {
            if (CollectionUtils.isEmpty(entranceTestProfiles)) {
                turnPlay = 1;
            } else {
                turnPlay = entranceTestProfiles.get(entranceTestProfiles.size() - 1).getTurnPlay() + 1;
            }

            entranceTestProfileRepository.save(EntranceTestProfile.builder()
                    .profileId(currentProfile.getId())
                    .turnPlay(turnPlay)
                    .isComplete(false)
                    .build());
        } else {
            turnPlay = incompleteTest.getTurnPlay();
            List<EntranceTestDetailResult> results = entranceTestDetailResultRepository.findByProfileIdAndTurnPlay(currentProfile.getId(), turnPlay);
            List<String> remainingDetailIds = testDetails.stream()
                    .map(EntranceTestDetail::getId)
                    .filter(id -> results.stream()
                            .noneMatch(r -> r.getEntranceTestDetailId().equals(id)))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(remainingDetailIds)) {
                testDetails = testDetails.stream()
                        .filter(e -> remainingDetailIds.contains(e.getId()))
                        .collect(Collectors.toList());
            } else {
                incompleteTest.setIsComplete(true);
                entranceTestProfileRepository.save(incompleteTest);

                turnPlay++;
                entranceTestProfileRepository.save(EntranceTestProfile.builder()
                        .profileId(currentProfile.getId())
                        .turnPlay(turnPlay)
                        .isComplete(false)
                        .build());
            }
        }

        entranceTestDataResDTO.setTurnPlay(turnPlay);
        entranceTestDataResDTO.setDetails(testDetails.stream()
                .map(e -> EntranceTestDetailDTO.builder()
                        .detailId(e.getId())
                        .data(e.getData())
                        .type(e.getType())
                        .build())
                .collect(Collectors.toList()));

        return new DataResponseDTO<>(CodeDefine.OK, "Lấy dữ liệu test đầu vào thành công", entranceTestDataResDTO);
    }

    @Transactional
    public DataResponseDTO<?> saveEntranceTestDetailResult(HttpServletRequest request, SaveEntranceDetailReqDTO saveEntranceDetailReqDTO, boolean isOpenApi) throws IOException {
        AccountInfoDTO accountInfoDTO = getAccountInfo(request, saveEntranceDetailReqDTO.getDeviceId(), isOpenApi);
        String userId = isOpenApi ? null : accountInfoDTO.getUserData().getUserId();
        String deviceId = accountInfoDTO.getDeviceId();

        Profile currentProfile = isOpenApi
                ? profileRepository.getCurrentProfileByDeviceId(deviceId)
                : profileRepository.getCurrentProfileByUserId(userId);
        if (currentProfile == null) {
            throw new IncompleteOnboardingException();
        }

        try {
            entranceTestDetailResultRepository.save(EntranceTestDetailResult.builder()
                    .profileId(currentProfile.getId())
                    .entranceTestDetailId(saveEntranceDetailReqDTO.getDetailId())
                    .turnPlay(saveEntranceDetailReqDTO.getTurnPlay())
                    .build());
        } catch (DataIntegrityViolationException e) {
            throw new ConflictEntityException("bài test đầu vào");
        }

        if (entranceTestDetailResultRepository.isUserFinishAllTestDetailsByTurnPlay(currentProfile.getId(), saveEntranceDetailReqDTO.getTurnPlay())) {
            entranceTestProfileRepository.updateStatusByProfileIdAndTurnPlay(currentProfile.getId(), saveEntranceDetailReqDTO.getTurnPlay());
        }

        return new DataResponseDTO<>(CodeDefine.OK, "Lưu dữ liệu bài test đầu vào thành công");
    }
}
