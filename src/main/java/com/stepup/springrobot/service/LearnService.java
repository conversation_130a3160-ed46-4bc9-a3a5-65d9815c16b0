package com.stepup.springrobot.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.chat.AIConversationDetailMessageResDTO;
import com.stepup.springrobot.dto.chat.WhisperAudioCMUDTO;
import com.stepup.springrobot.dto.learn.LessonDataDTO;
import com.stepup.springrobot.dto.learn.LessonDetailDataDTO;
import com.stepup.springrobot.exception.business.content.ContentNotFoundException;
import com.stepup.springrobot.model.learn.LessonDetail;
import com.stepup.springrobot.model.learn.LessonDetailType;
import com.stepup.springrobot.repository.learn.LessonDetailRepository;
import com.stepup.springrobot.repository.learn.LessonRepository;
import com.stepup.springrobot.security.JwtService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.stream.Collectors;

@Log4j2
@Service
public class LearnService extends CommonService{
    @Autowired
    private LessonRepository lessonRepository;

    @Autowired
    private LessonDetailRepository lessonDetailRepository;

    @Autowired
    private ObjectMapper objectMapper;

    protected LearnService(ObjectMapper objectMapper, UploadFileToS3 uploadFileToS3, JwtService jwtService, SlackWarningSystemService slackWarningSystemService) {
        super(objectMapper, uploadFileToS3, jwtService, slackWarningSystemService);
    }

    public DataResponseDTO<?> getAllLessons() {
        return new DataResponseDTO<>(CodeDefine.OK, "Thành công", lessonRepository.findAll());
    }

    public DataResponseDTO<?> getLearnData(Long lessonId) {
        List<LessonDetail> lessonDetails = lessonDetailRepository.findByLessonIdOrderByOrderAsc(lessonId);
        LessonDataDTO lessonDataDTO = LessonDataDTO.builder()
                .activity(lessonDetails.stream().map(lessonDetail -> LessonDetailDataDTO.builder()
                        .type(lessonDetail.getType())
                        .lessonDetailId(lessonDetail.getId())
                        .data(lessonDetail.getData())
                        .build()).collect(Collectors.toList()))
                .build();

        return new DataResponseDTO<>(CodeDefine.OK, "Thành công", lessonDataDTO);
    }

    public DataResponseDTO<?> getLearnDetailData(String lessonDetailId) throws IOException {
        LessonDetail lessonDetail = lessonDetailRepository.findById(lessonDetailId).orElse(null);
        if (lessonDetail == null) {
            ContentNotFoundException exception = new ContentNotFoundException("lesson_detail", lessonDetailId);
            exception.addDetail("lessonDetailId", lessonDetailId);
            throw exception;
        }

        JsonNode data = lessonDetail.getData();
        if (lessonDetail.getType() == LessonDetailType.LISTENING) {
            data = getListeningData(lessonDetail);
        }

        return new DataResponseDTO<>(CodeDefine.OK, "Thành công", data);
    }

    public JsonNode getListeningData(LessonDetail lessonDetail) throws IOException {
        if (lessonDetail.getData() == null) {
            InputStream inputStream = getClass().getResourceAsStream("/listening.json");
            String data = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
            return objectMapper.readValue(data, new TypeReference<>() {
            });
        } else {
            String audioUrl = lessonDetail.getData().get("audio").asText();
            String transcript = lessonDetail.getData().get("transcript").asText();
            List<WhisperAudioCMUDTO> audioCMUDTOS = getSpeechAnimationForSentence(AIConversationDetailMessageResDTO.builder()
                            .content(transcript)
                            .audio(audioUrl)
                            .isLastSentence(true)
                            .build(),
                    "web_mvp", true);
            ObjectNode data = objectMapper.createObjectNode();

            ObjectNode audio = objectMapper.createObjectNode();
            audio.put("url", audioUrl);
            ArrayNode sentences = objectMapper.createArrayNode();
            ObjectNode transcriptElement = objectMapper.createObjectNode();
            transcriptElement.put("text", transcript);
            transcriptElement.set("animations", objectMapper.valueToTree(audioCMUDTOS));
            transcriptElement.put("start_time", audioCMUDTOS.get(0).getStart());
            transcriptElement.put("end_time", audioCMUDTOS.get(audioCMUDTOS.size() - 1).getEnd());
            sentences.add(transcriptElement);
            audio.set("transcript", sentences);

            data.set("audio", audio);
            data.set("intro", lessonDetail.getData().get("intro"));

            ObjectNode lessonData = objectMapper.createObjectNode();
            lessonData.set("data", data);
            lessonData.put("type", lessonDetail.getType().getType());
            return lessonData;
        }
    }
}
