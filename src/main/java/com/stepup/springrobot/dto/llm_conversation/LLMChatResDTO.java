package com.stepup.springrobot.dto.llm_conversation;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stepup.springrobot.dto.chat.LlmListeningDetailDTO;
import com.stepup.springrobot.dto.llm.LlmTextDTO;
import com.stepup.springrobot.model.chat.AnswerModeType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LLMChatResDTO {
    private String status;

    private List<LlmTextDTO> text;

    @JsonProperty("conversation_id")
    private String conversationId;

    private String msg;

    private String language;

    @JsonProperty("listening_animations")
    private List<LlmListeningDetailDTO> listeningAnimations;

    @JsonProperty("audio_listening")
    private String audioListening;

    @JsonProperty("image_listening")
    private String imageListening;

    @JsonProperty("robot_type")
    private String robotType;

    @JsonProperty("answer_mode")
    private AnswerModeType answerMode;
}
