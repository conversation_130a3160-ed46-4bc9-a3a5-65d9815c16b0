package com.stepup.springrobot.dto.llm_conversation;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UpdateUserProfileReqDTO {
    @NotNull(message = "token is required")
    private String token;

    @NotNull(message = "conversation_id is required")
    @JsonProperty("conversation_id")
    private String conversationId;

    private Map<String, String> data;
}
