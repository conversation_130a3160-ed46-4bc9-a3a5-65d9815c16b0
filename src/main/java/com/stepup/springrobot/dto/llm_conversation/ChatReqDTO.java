package com.stepup.springrobot.dto.llm_conversation;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ChatReqDTO {
    private String message;

    @JsonProperty("conversation_id")
    private String conversationId;

    @JsonProperty("audio_url")
    private String audioUrl;
}
