package com.stepup.springrobot.dto.admin;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ConversationSummaryResDTO {
    @JsonProperty("conversation_id")
    private Long conversationId;

    private String summary;

    @JsonProperty("created_at")
    private Date createdAt;
}
