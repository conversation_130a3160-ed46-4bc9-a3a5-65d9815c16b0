package com.stepup.springrobot.dto.admin;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class UpdateSDReqDTO {
    @NotNull(message = "url is required")
    private String url;

    @NotNull(message = "path is required")
    private String path;

    @NotNull(message = "robot_id is required")
    @JsonProperty("robot_id")
    private String robotId;
}
