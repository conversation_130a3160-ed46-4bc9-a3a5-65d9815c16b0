package com.stepup.springrobot.dto.ladipage;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;

@Data
public class LadipageOrder {
    @JsonProperty("order_id")
    private Long orderId;

    @JsonProperty("store_id")
    private Long storeId;

    @JsonProperty("creator_id")
    private String creatorId;

    @JsonProperty("assignee_id")
    private String assigneeId;

    @JsonProperty("reference_no")
    private String referenceNo;

    @JsonProperty("discount_id")
    private String discountId;

    @JsonProperty("discount_note")
    private String discountNote;

    @JsonProperty("discount_code")
    private String discountCode;

    @JsonProperty("discount_value")
    private Double discountValue;

    @JsonProperty("discount_type")
    private String discountType;

    @JsonProperty("discount_fee")
    private Double discountFee;

    @JsonProperty("discount_maximum")
    private Double discountMaximum;

    @JsonProperty("shipping_fee")
    private Double shippingFee;

    @JsonProperty("is_auto_shipping")
    private Integer isAutoShipping;

    @JsonProperty("shipping_refund")
    private Double shippingRefund;

    @JsonProperty("tax_fee")
    private Double taxFee;

    @JsonProperty("sub_total")
    private Double subTotal;

    private Double total;
    private String refund;
    private Double paid;
    private String note;
    private Double weight;
    private String status;

    @JsonProperty("payment_status")
    private String paymentStatus;

    @JsonProperty("shipping_status")
    private String shippingStatus;

    @JsonProperty("customer_id")
    private Long customerId;

    @JsonProperty("customer_first_name")
    private String customerFirstName;

    @JsonProperty("customer_email")
    private String customerEmail;

    @JsonProperty("customer_phone")
    private String customerPhone;

    @JsonProperty("customer_note")
    private String customerNote;

    @JsonProperty("shipping_note")
    private String shippingNote;

    private String source;

    @JsonProperty("url_page")
    private String urlPage;

    @JsonProperty("product_type")
    private String productType;

    private String ip;

    @JsonProperty("parent_id")
    private Long parentId;

    @JsonProperty("is_delete")
    private Integer isDelete;

    @JsonProperty("paid_at")
    private ZonedDateTime paidAt;

    @JsonProperty("created_at")
    private ZonedDateTime createdAt;

    @JsonProperty("updated_at")
    private ZonedDateTime updatedAt;

    @JsonProperty("ordered_at")
    private ZonedDateTime orderedAt;

    @JsonProperty("user_agent")
    private String userAgent;

    @JsonProperty("order_details")
    private List<LadipageOrderDetail> orderDetails;

    @JsonProperty("custom_fields")
    private Map<String, Object> customFields;

    @JsonProperty("list_item_combo")
    private List<Object> listItemCombo;

    @JsonProperty("payment_type")
    private String paymentType;

    @JsonProperty("payment_gateway_method")
    private String paymentGatewayMethod;

    private Integer fee;

    @JsonProperty("currency_code")
    private String currencyCode;

    @JsonProperty("currency_symbol")
    private String currencySymbol;

    @JsonProperty("ticket_seats")
    private List<Object> ticketSeats;

    @JsonProperty("download_url")
    private String downloadUrl;
} 