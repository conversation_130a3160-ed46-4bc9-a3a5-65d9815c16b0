package com.stepup.springrobot.dto.ladipage;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.time.ZonedDateTime;
import java.util.List;

@Data
public class LadipageOrderDetail {
    @JsonProperty("order_detail_id")
    private Long orderDetailId;
    
    @JsonProperty("store_id")
    private Long storeId;
    
    @JsonProperty("order_id")
    private Long orderId;
    
    @JsonProperty("product_id")
    private Long productId;
    
    @JsonProperty("product_variant_id")
    private Long productVariantId;
    
    @JsonProperty("product_name")
    private String productName;
    
    @JsonProperty("product_type")
    private String productType;
    
    @JsonProperty("option_name")
    private String optionName;
    
    @JsonProperty("up_sell_ids")
    private String upSellIds;
    
    private Double price;
    private Integer quantity;
    private Integer restock;
    private Double total;
    private String sku;
    private String unit;
    private Double weight;
    
    @JsonProperty("discount_id")
    private String discountId;
    
    @JsonProperty("discount_type")
    private String discountType;
    
    @JsonProperty("discount_maximum")
    private Double discountMaximum;
    
    @JsonProperty("discount_value")
    private Double discountValue;
    
    @JsonProperty("discount_fee")
    private Double discountFee;
    
    @JsonProperty("discount_note")
    private String discountNote;
    
    private String note;
    
    @JsonProperty("list_customer")
    private Object listCustomer;
    
    @JsonProperty("created_at")
    private ZonedDateTime createdAt;
    
    @JsonProperty("updated_at")
    private ZonedDateTime updatedAt;
    
    @JsonProperty("cost_per_item")
    private Double costPerItem;
    
    @JsonProperty("product_images")
    private List<String> productImages;
    
    @JsonProperty("custom_fields")
    private List<Object> customFields;
    
    private List<Tag> tag;
    
    @JsonProperty("product_categories")
    private List<Category> productCategories;
}