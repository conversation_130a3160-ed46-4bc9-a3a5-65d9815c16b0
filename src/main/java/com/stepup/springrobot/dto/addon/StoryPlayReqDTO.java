package com.stepup.springrobot.dto.addon;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class StoryPlayReqDTO {
    @NotNull(message = "Story ID is required")
    @JsonProperty("story_id")
    private Long storyId;
} 