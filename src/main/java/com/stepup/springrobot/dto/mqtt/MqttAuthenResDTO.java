package com.stepup.springrobot.dto.mqtt;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stepup.springrobot.model.mqtt.MqttPermissionType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MqttAuthenResDTO {
    private MqttPermissionType result;

    @JsonProperty("is_superuser")
    private boolean isSuperUser;
}
