package com.stepup.springrobot.dto.mqtt;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MqttAuthorizeReqDTO {
    @NotNull(message = "Token không được để trống")
    private String token;

    @NotNull(message = "Username không được để trống")
    private String username;

    private String topic;

    private String action;
}
