package com.stepup.springrobot.dto.setting;

import com.stepup.springrobot.model.mqtt.MqttMessageType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ChangeSettingReqDTO {
    @NotNull(message = "Type không được để trống")
    private MqttMessageType type;

    @NotNull(message = "value không được để trống")
    private Object value;
}
