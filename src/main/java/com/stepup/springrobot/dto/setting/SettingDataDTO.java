package com.stepup.springrobot.dto.setting;

import com.fasterxml.jackson.databind.JsonNode;
import com.stepup.springrobot.model.mqtt.MqttMessageType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SettingDataDTO {
    private MqttMessageType type;

    private JsonNode data;
}
