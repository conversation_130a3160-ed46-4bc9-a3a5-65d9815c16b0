
package com.stepup.springrobot.dto.entrance_test;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.stepup.springrobot.model.entrance_test.EntranceTestDetailType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EntranceTestDetailDTO {
    @JsonProperty("detail_id")
    private String detailId;

    private EntranceTestDetailType type;

    private JsonNode data;
}
