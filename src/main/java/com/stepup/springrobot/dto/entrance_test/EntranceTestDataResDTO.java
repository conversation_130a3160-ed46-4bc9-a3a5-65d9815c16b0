package com.stepup.springrobot.dto.entrance_test;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EntranceTestDataResDTO {
    private String image;

    private String title;

    private String description;

    @JsonProperty("turn_play")
    private Integer turnPlay;

    private List<EntranceTestDetailDTO> details;
}
