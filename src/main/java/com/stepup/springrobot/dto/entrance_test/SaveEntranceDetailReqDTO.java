package com.stepup.springrobot.dto.entrance_test;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SaveEntranceDetailReqDTO {
    @NotBlank(message = "Thiếu thông tin device_id")
    @JsonProperty("device_id")
    private String deviceId;

    @NotBlank(message = "Thiếu thông tin detail_id")
    @JsonProperty("detail_id")
    private String detailId;

    @NotNull(message = "Thiếu thông tin turn_play")
    @JsonProperty("turn_play")
    private Integer turnPlay;

    @NotNull(message = "Thiếu thông tin is_correct")
    @JsonProperty("is_correct")
    private Boolean isCorrect;
}
