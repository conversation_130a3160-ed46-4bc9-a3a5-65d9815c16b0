package com.stepup.springrobot.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.PageRequest;

import java.util.Objects;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class SearchPageLimit {
    private String search;
    private int page;
    private int limit = 20;

    public SearchPageLimit(Integer page) {
        this.page = Objects.requireNonNullElse(page, 0);
    }

    public SearchPageLimit(Integer page, int limit) {
        this.page = Objects.requireNonNullElse(page, 0);
        this.limit = limit;
    }

    public SearchPageLimit(String search, Integer page) {
        this.search = search;
        this.page = Objects.requireNonNullElse(page, 0);
    }

    /**
     * Custom lại page vì page trong db tính từ 0
     */
    public int getPage() {
        if (this.page <= 0) {
            this.page = 0;
        } else {
            this.page = page - 1;// database tính từ 0
        }

        return this.page;
    }

    /**
     * get page để truyền vào jpa
     */
    public PageRequest getPageRequest() {
        return PageRequest.of(getPage(), getLimit());
    }
}
