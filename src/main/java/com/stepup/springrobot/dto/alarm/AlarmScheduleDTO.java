package com.stepup.springrobot.dto.alarm;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stepup.springrobot.util.LocalTimeDeserializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AlarmScheduleDTO {
    @JsonProperty("id")
    private Long id;

    @NotBlank(message = "Thiếu thông tin tên báo thức")
    @JsonProperty("name")
    private String name;

    @NotNull(message = "Thiếu thông tin thời gian báo thức")
    @JsonDeserialize(using = LocalTimeDeserializer.class)
    @JsonProperty("time")
    private LocalTime time;

    @JsonProperty("is_active")
    private Boolean isActive;

    @JsonProperty("repeat_days")
    private String repeatDays; // Format: "2,3,4,5,6,7,cn" for days of week

    @JsonProperty("activity_id")
    private Long activityId;

    @JsonProperty("device_id")
    private String deviceId;
}