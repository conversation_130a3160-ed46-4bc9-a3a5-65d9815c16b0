package com.stepup.springrobot.dto.alarm;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stepup.springrobot.model.alarm.AlarmDateType;
import com.stepup.springrobot.model.alarm.AlarmTimeType;
import com.stepup.springrobot.util.LocalTimeDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.time.LocalTime;
import java.util.List;

/**
 * DTO for receiving alarm schedule requests from clients
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AlarmScheduleReqDTO {
    private Long id;

    @NotNull(message = "Thiếu thông tin khung giờ báo thức")
    @JsonProperty("day_part")
    private AlarmTimeType dayPart;

    @NotNull(message = "Thiếu thông tin thời gian báo thức")
    @JsonDeserialize(using = LocalTimeDeserializer.class)
    private LocalTime time;

    @JsonProperty("is_active")
    private Boolean isActive;

    @NotNull(message = "Thiếu thông tin ngày báo thức")
    @JsonProperty("repeat_days")
    @Schema(description = "Các ngày lặp lại báo thức trong tuần", example = "[\"ONE_TIME\",\"MONDAY\",\"TUESDAY\",\"WEDNESDAY\",\"THURSDAY\",\"FRIDAY\",\"SATURDAY\",\"SUNDAY\"]",
            type = "array", implementation = List.class)
    private List<AlarmDateType> repeatDays; // Format: ["2","3","4","5","6","7","cn"] for days of week

    @JsonProperty("activity_id")
    private Long activityId;
}
