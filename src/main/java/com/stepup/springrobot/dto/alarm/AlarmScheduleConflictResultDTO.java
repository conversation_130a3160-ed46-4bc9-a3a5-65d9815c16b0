package com.stepup.springrobot.dto.alarm;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * DTO for representing the result of a schedule conflict check
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AlarmScheduleConflictResultDTO {
    @JsonProperty("has_conflicts")
    private Boolean hasConflicts;

    private List<AlarmScheduleConflictDTO> conflicts;
}
