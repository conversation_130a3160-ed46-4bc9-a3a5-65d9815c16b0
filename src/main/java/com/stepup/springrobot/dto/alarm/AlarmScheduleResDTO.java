package com.stepup.springrobot.dto.alarm;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalTime;
import java.util.List;

/**
 * DTO for sending alarm schedule responses to clients
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AlarmScheduleResDTO {
    private Long id;

    private String time;

    @JsonProperty("day_part")
    private String dayPart;

    @JsonProperty("is_active")
    private Boolean isActive;

    @JsonProperty("repeat_days")
    private List<String> repeatDays; // Format: ["2","3","4","5","6","7","cn"] for days of week

    @JsonProperty("repeat_days_description")
    private String repeatDaysDescription;

    @JsonProperty("activity_name")
    private String activityName;

    @JsonProperty("activity_id")
    private Long activityId;
}
