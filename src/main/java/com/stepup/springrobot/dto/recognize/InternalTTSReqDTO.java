package com.stepup.springrobot.dto.recognize;

import com.stepup.springrobot.model.InternalTextToSpeechVoice;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InternalTTSReqDTO {
    private InternalTextToSpeechVoice voice;

    private String text;

    private String token;

    private String format;

    private String source;

    private Double speed;

    private Integer vol;
}
