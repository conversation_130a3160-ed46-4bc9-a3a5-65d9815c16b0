package com.stepup.springrobot.dto.recognize;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InternalAsrDTO {
    private String success;

    private String msg;

    private ResultAsrDTO result;

    @JsonProperty("response_time")
    private double responseTime;
}
