package com.stepup.springrobot.dto.recognize;

import com.stepup.springrobot.model.chat.AICharacter;
import com.stepup.springrobot.model.chat.GPTCharacter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InternalTTSResDTO {
    private int status;

    private String msg;

    private InternalTTSResDataDTO data;

    private Object stats;
}
