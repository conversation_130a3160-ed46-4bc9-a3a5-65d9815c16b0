package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AIRobotConversationResDTO {
    @JsonProperty("conversation_id")
    private Long conversationId;

    private String text;

    private String mp3;

    private List<WhisperAudioCMUDTO> animations;

    @Builder.Default
    private String emotion = "HAPPY";

    private JsonNode emotions;

    @Builder.Default
    private int servo = 1;

    private String status;

    @JsonProperty("servo_data")
    private JsonNode servoData;

    private MessageMediaDTO media;
}
