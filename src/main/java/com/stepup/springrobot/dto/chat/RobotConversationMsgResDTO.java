package com.stepup.springrobot.dto.chat;

import com.stepup.springrobot.model.chat.AnswerModeType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RobotConversationMsgResDTO {
    private List<AIRobotConversationResDTO> messages;

    private List<RobotResponseMessageDTO> responseMessages;

    private RobotListeningEmotionDTO listeningEmotion;

    private String language;

    private List<String> gifs;

    private AnswerModeType answerMode;
}
