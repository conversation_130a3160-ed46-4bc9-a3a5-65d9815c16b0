package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AIRobotConversationReqDTO {
    @JsonProperty("conversation_id")
    private Long conversationId;

    @JsonProperty("voice_data")
    private String voiceData;

    private String token;
}
