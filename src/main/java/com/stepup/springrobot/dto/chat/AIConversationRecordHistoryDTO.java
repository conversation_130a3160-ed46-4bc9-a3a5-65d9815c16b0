package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stepup.springrobot.model.chat.AICharacter;
import com.stepup.springrobot.model.chat.GPTCharacter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AIConversationRecordHistoryDTO {
    private AICharacter character;

    private GPTCharacter gptCharacter;

    private Long aiUserConversationId;

    private String content;

    private String audio;

    private String messageId;

    private Boolean isFinishMessage;

    private String userId;

    private String phone;

    private Long responseTime;

    private String groupMessageId;

    private String voiceData;

    private String emotion;

    private String image;

    private String video;

    private String data;

    private String servoData;

    private String robotType;

    private String emotions;

    @JsonProperty("listening_animation")
    private String listeningAnimation;

    private String language;

    @JsonProperty("text_viewer")
    private String textViewer;

    private Integer volume;
}
