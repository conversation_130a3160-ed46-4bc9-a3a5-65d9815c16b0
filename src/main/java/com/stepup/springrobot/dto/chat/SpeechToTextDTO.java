package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SpeechToTextDTO {
    private String uid;

    private List<SpeechToTextSegmentDTO> segments;

    @JsonProperty("final")
    private boolean isFinal;
}
