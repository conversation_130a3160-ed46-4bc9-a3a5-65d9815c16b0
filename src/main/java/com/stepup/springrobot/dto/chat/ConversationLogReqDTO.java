package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ConversationLogReqDTO {
    @NotBlank(message = "socket_session_id is required")
    @JsonProperty("socket_session_id")
    private String socketSessionId;

    @NotNull(message = "log is required")
    private JsonNode log;
}
