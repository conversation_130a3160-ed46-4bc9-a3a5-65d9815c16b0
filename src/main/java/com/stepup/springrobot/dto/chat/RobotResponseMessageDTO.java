package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RobotResponseMessageDTO {
    private String text;

    private String audio;

    private String emotion;

    @Builder.Default
    private int servo = 1;

    private List<WhisperAudioCMUDTO> animations;

    @JsonProperty("servo_data")
    private JsonNode servoData;

    private MessageMediaDTO media;

    private JsonNode emotions;

    private Long conversationId;
}
