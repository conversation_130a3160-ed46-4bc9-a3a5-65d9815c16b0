package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FastResponseResDTO {
    @JsonProperty("user_intent")
    private String userIntent;

    @JsonProperty("confidence_score")
    private Double confidenceScore;

    @JsonProperty("response_time_ms")
    private Double responseTimeMs;

    @JsonProperty("fast_response")
    private String fastResponse;
}
