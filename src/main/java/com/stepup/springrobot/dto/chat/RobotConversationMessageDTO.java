package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stepup.springrobot.model.chat.RobotConversationResponseType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RobotConversationMessageDTO {
    private RobotConversationResponseType type;

    private Object data;

    @JsonProperty("conversation_id")
    private Long conversationId;

    @JsonProperty("socket_session_id")
    private String socketSessionId;
}
