package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AIConversationDetailMessageResDTO {
    private String content;

    @Builder.Default
    private List<String> action = new ArrayList<>();

    @JsonProperty("is_saved")
    private boolean isSaved;

    @JsonProperty("separated_sentences")
    private List<String> separatedSentences;

    private String audio;

    @JsonProperty("is_finish_message")
    private boolean isFinishMessage;

    /**
     * @deprecated since version 1.7.1
     */
    @Deprecated(since = "Version 1.7.1")
    @JsonProperty("example_answer")
    private String exampleAnswer;

    private List<WhisperAudioWordDTO> words;

    @JsonIgnore
    private boolean isLastSentence = false;
}
