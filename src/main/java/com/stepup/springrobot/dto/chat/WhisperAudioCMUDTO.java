package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WhisperAudioCMUDTO {
    @Builder.Default
    private String cmu = "expression";

    @JsonProperty("animation_id")
    private Integer animationId;

    @JsonProperty("animation")
    private String animation;

    private Double start;

    private Double end;

    private Double duration;
}
