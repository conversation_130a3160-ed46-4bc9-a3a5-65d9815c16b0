package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stepup.springrobot.model.chat.AnswerModeType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RobotChatResponseDTO {
    private List<RobotResponseMessageDTO> messages;

    @JsonProperty("has_next_message")
    private boolean hasNextMessage;

    @JsonProperty("is_end_conversation")
    private boolean isEndConversation;

    @JsonProperty("listening_emotion")
    private RobotListeningEmotionDTO listeningEmotion;

    private List<String> gifs;

    @JsonProperty("answer_mode")
    private AnswerModeType answerMode;
}
