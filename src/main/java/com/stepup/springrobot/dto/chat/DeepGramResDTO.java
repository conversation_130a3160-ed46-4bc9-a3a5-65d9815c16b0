package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DeepGramResDTO {
    @JsonProperty("is_final")
    private boolean isFinal;

    @JsonProperty("speech_final")
    private boolean speechFinal;

    private double duration;

    private double start;

    private DeepGramChannelDTO channel;
}
