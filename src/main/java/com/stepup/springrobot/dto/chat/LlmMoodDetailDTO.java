package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LlmMoodDetailDTO {
    private Integer duration;

    @JsonProperty("mood_name")
    private String moodName;

    @JsonProperty("servo_name")
    private String servoName;
}
