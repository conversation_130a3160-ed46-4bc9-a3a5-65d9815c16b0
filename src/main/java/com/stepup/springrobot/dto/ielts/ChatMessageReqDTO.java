package com.stepup.springrobot.dto.ielts;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatMessageReqDTO {
    @JsonProperty("message_type")
    private String messageType;

    @JsonProperty("user_message")
    private String userMessage;
}
