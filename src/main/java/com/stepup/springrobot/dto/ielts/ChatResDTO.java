package com.stepup.springrobot.dto.ielts;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ChatResDTO {
    @JsonProperty("messages")
    private List<ChatMessageResDTO> messages;

    @JsonProperty("available_action")
    private List<String> availableAction;

    @JsonProperty("session_id")
    private String sessionId;

    @JsonProperty("bot_response")
    private String botResponse;

    @JsonProperty("has_next_messages")
    private boolean hasNextMessages;

    @JsonProperty("is_hide_user_messages")
    private boolean isHideUserMessages;

    @JsonProperty("is_finish")
    private boolean isFinish;

    @JsonProperty("lesson_id")
    private String lessonId;

    @JsonProperty("step")
    private String step;
}
