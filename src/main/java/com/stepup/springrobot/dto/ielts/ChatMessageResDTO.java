package com.stepup.springrobot.dto.ielts;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ChatMessageResDTO {
    @JsonProperty("order")
    private double order;

    @JsonProperty("message")
    private Object message;

    @JsonProperty("message_type")
    private String messageType;

    @JsonProperty("progress_id")
    private String progressId;

    @JsonProperty("is_display")
    private Boolean isDisplay;

    @JsonProperty("is_pause")
    private boolean isPause;

    @JsonProperty("idea_board")
    private Object ideaBoard;

    @JsonIgnore
    private boolean isFinish;

    @Builder.Default
    @JsonIgnore
    private Boolean hasToCallNextBot = false;

    @JsonIgnore
    private Boolean isStop;

    @JsonIgnore
    public Boolean getStop() {
        return isStop;
    }

    @JsonProperty("is_stop")
    public void setStop(Boolean stop) {
        isStop = stop;
    }

    @JsonIgnore
    private Boolean isHideUserMessages;

    @JsonIgnore
    public Boolean getIsHideUserMessages() {
        return isHideUserMessages;
    }

    @JsonProperty("is_hide_user_messages")
    public void setIsHideUserMessages(Boolean isHideUserMessages) {
        this.isHideUserMessages = isHideUserMessages;
    }

    @JsonIgnore
    public boolean isFinish() {
        return isFinish;
    }

    @JsonProperty("is_finish")
    public void setFinish(boolean finish) {
        isFinish = finish;
    }
}
