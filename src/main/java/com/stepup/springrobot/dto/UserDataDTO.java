package com.stepup.springrobot.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserDataDTO implements Serializable {
    private static final long serialVersionUID = -8091879091924046844L;

    @JsonProperty("user_id")
    private String userId;
    private String phone;
    private String email;
    @JsonProperty("status_use")
    private String statusUse;
    private String jti;//jwt id
    private Object role;
    private Object books;
    private Object product;

    @JsonProperty("lock_device_max")
    private Boolean lockDeviceMax;
}
