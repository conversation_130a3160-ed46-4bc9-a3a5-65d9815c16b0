package com.stepup.springrobot.dto.sms;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SendSMSResInternalDTO {
    @JsonProperty("error_code")
    private Integer errorCode;

    private String sn;

    @JsonProperty("sms_in_queue")
    private String smsInQueue;

    @JsonProperty("task_id")
    private Integer taskId;
}
