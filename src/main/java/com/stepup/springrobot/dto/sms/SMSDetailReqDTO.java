package com.stepup.springrobot.dto.sms;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class SMSDetailReqDTO {
    @JsonProperty("incoming_sms_id")
    private int incomingSmsId;

    private int port;
    private String number;
    private String smsc;
    private String timestamp;
    private String text;
    private String imsi;
}
