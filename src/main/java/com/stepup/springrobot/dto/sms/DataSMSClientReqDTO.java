package com.stepup.springrobot.dto.sms;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stepup.springrobot.model.sms.SMSAction;
import com.stepup.springrobot.model.sms.SMSServiceProvider;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataSMSClientReqDTO {
    private String phone;
    private String content;
    private String otp;
    private SMSAction action;
    private String ip;

    @JsonProperty("from_app")
    private String fromApp;

    @JsonProperty("is_brandname")
    private boolean isBrandname;

    private String appV;

    @JsonProperty("sms_service_provider")
    private SMSServiceProvider smsServiceProvider;
}
