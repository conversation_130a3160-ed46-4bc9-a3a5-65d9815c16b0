package com.stepup.springrobot.dto.sms;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SMSRequestChildInternalDTO {
    private String number = "#param#";

    @JsonProperty("text_param")
    private String[] textParam;

    @JsonProperty("user_id")
    private String userId;
}
