package com.stepup.springrobot.dto.sms;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SMSRequestInternalDTO {
    private String text = "#param#";
    private Integer[] port = new Integer[1];
    private List<SMSRequestChildInternalDTO> param;

    public SMSRequestInternalDTO(List<SMSRequestChildInternalDTO> smsRequestChildInternalDTOS) {
        this.param = smsRequestChildInternalDTOS;
    }
}
