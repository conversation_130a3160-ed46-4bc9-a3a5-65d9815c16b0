package com.stepup.springrobot.dto.feedback;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserFeedbackDTO {
    private String id;
    private String description;
    private JsonNode images;
    private String feedbackId;
    private Integer rating;
    private UUID accountId;
} 