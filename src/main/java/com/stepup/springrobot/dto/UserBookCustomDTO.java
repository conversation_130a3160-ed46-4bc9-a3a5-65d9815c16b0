package com.stepup.springrobot.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class UserBookCustomDTO {
    @JsonProperty("user_id")
    private Long userId;

    @JsonProperty("book")
    private String book;

    @JsonProperty("expire_date")
    private String expireDate;

    @JsonProperty("is_trial")
    private boolean isTrial;

    @JsonProperty("is_trial_x_day")
    private boolean isTrialXDay;
}
