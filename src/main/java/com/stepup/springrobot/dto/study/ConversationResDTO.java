package com.stepup.springrobot.dto.study;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class ConversationResDTO {
    @JsonProperty("bot_avatar")
    private String botAvatar;

    @JsonProperty("bot_name")
    private String botName;

    private String caption;

    private List<ConversationMessageDTO> messages;
}
