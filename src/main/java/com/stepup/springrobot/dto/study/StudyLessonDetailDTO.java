package com.stepup.springrobot.dto.study;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class StudyLessonDetailDTO {
    private StudyLessonDetailHeaderDTO header;

    @JsonProperty("lesson_content")
    private StudyLessonDetailContentDTO lessonContent;

    private StudyLessonDetailProgressDTO progress;

    private List<String> evaluation;

    @JsonProperty("bot_id")
    private Long botId;
}
