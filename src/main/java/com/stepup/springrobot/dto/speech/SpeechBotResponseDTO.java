package com.stepup.springrobot.dto.speech;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class SpeechBotResponseDTO {
    private boolean success;

    private String msg;

    @JsonProperty("text_refs")
    private String textRefs;

    private JsonNode result;

    @JsonProperty("audio_url")
    private String audioUrl;

    @JsonProperty("total_score")
    private Double totalScore;

    @JsonProperty("response_time")
    private String responseTime;
}
