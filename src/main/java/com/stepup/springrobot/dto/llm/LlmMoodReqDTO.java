package com.stepup.springrobot.dto.llm;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LlmMoodReqDTO {
    @NotEmpty(message = "token must not be empty")
    private String token;

    @NotEmpty(message = "moods must not be empty")
    private List<LlmMoodDTO> moods;
} 