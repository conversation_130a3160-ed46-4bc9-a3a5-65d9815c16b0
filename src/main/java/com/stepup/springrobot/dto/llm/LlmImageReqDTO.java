package com.stepup.springrobot.dto.llm;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LlmImageReqDTO {
    @NotEmpty(message = "token must not be empty")
    private String token;

    @JsonProperty("bot_id")
    @NotNull(message = "bot_id must not be null")
    private Long botId;

    @NotEmpty(message = "images must not be empty")
    private List<LlmImageDTO> images;
} 