package com.stepup.springrobot.dto.llm;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stepup.springrobot.dto.chat.LlmMoodDetailDTO;
import com.stepup.springrobot.util.VoiceSpeedDeserializer;
import com.stepup.springrobot.util.VolumeDeserializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LlmTextDTO {
    private String text;

    private String audio;

    private List<LlmMoodDetailDTO> moods;

    private String image;

    private String video;

    @JsonProperty("voice_speed")
    @JsonDeserialize(using = VoiceSpeedDeserializer.class)
    private Double voiceSpeed;

    @JsonProperty("text_viewer")
    private String textViewer;

    @JsonDeserialize(using = VolumeDeserializer.class)
    private Integer volume;
}