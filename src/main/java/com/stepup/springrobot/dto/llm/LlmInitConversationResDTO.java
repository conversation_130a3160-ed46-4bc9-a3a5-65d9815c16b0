package com.stepup.springrobot.dto.llm;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LlmInitConversationResDTO {
    private Integer status;

    private String msg;

    @NotEmpty(message = "conversation_id must not be empty")
    @JsonProperty("conversation_id")
    private String conversationId;

    private List<String> gifs;
} 