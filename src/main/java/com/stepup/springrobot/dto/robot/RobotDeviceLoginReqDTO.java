package com.stepup.springrobot.dto.robot;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RobotDeviceLoginReqDTO {
    @NotBlank(message = "device_id is required")
    @JsonProperty("device_id")
    private String deviceId;

    @NotBlank(message = "password is required")
    private String password;
}