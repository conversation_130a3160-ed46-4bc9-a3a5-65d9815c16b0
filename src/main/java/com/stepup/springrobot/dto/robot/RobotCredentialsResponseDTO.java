package com.stepup.springrobot.dto.robot;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RobotCredentialsResponseDTO {
    @JsonProperty("robot_id")
    private String robotId;

    private String password;

    @JsonProperty("encryption_key")
    private String encryptionKey;
} 