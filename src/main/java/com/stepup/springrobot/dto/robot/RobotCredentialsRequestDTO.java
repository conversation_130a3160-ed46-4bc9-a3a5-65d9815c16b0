package com.stepup.springrobot.dto.robot;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RobotCredentialsRequestDTO {
    @NotBlank(message = "mac_address is required")
    @JsonProperty("mac_address")
    private String macAddress;

    @NotBlank(message = "device_id is required")
    @JsonProperty("device_id")
    private String deviceId;
}