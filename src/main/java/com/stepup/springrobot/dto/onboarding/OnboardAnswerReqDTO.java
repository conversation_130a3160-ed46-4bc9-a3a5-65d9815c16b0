package com.stepup.springrobot.dto.onboarding;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class OnboardAnswerReqDTO {
    @JsonProperty("onboarding_answers")
    private List<OnboardingAnswerDTO> obAnswers;

    @JsonProperty("app_v")
    private String appV;

    @NotBlank(message = "Thiếu thông tin device_id")
    @JsonProperty("device_id")
    private String deviceId;
}
