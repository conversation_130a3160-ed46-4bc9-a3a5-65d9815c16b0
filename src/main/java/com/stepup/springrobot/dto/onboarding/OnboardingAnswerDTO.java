package com.stepup.springrobot.dto.onboarding;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class OnboardingAnswerDTO {
    @JsonProperty("type")
    private String type;

    @JsonProperty("answer")
    private JsonNode answer;
}
