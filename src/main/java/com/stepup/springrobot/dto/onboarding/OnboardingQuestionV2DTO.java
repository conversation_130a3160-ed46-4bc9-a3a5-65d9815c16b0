package com.stepup.springrobot.dto.onboarding;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.stepup.springrobot.model.onboarding.OnboardingAnswerType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class OnboardingQuestionV2DTO {
    private String background;

    private String character;

    private String type;

    @JsonProperty("answer_type")
    private OnboardingAnswerType answerType;

    private JsonNode data;

    private String text;
}
