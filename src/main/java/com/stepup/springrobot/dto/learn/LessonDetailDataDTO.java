package com.stepup.springrobot.dto.learn;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.stepup.springrobot.model.learn.LessonDetailType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LessonDetailDataDTO {
    private LessonDetailType type;

    @JsonProperty("lesson_detail_id")
    private String lessonDetailId;

    private JsonNode data;
}
