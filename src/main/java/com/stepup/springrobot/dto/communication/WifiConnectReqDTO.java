package com.stepup.springrobot.dto.communication;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class WifiConnectReqDTO {
    @NotNull(message = "wifi name is required")
    private String name;

    @NotNull(message = "password is required")
    private String password;

    @NotNull(message = "robot_id is required")
    @JsonProperty("robot_id")
    private String robotId;
}