package com.stepup.springrobot.dto.communication;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stepup.springrobot.model.communication.CommunicationRequestStatusType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class MqttUpdateResultReqDTO {
    @NotNull(message = "Request ID is required")
    @JsonProperty("request_id")
    private String requestId;

    @NotNull(message = "Status is required")
    private CommunicationRequestStatusType status;

    private String message;
}