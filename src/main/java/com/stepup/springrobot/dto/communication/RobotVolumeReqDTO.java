package com.stepup.springrobot.dto.communication;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RobotVolumeReqDTO {
    @NotNull(message = "robot_id is required")
    @JsonProperty("robot_id")
    private String robotId;

    @NotNull(message = "value is required")
    private Integer value;
}