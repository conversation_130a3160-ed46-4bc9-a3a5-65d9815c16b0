package com.stepup.springrobot.dto.communication;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class WifiScanResultReqDTO {
    @NotNull(message = "Request ID is required")
    @JsonProperty("request_id")
    private String requestId;

    private List<WifiDetailDTO> wifi;
}