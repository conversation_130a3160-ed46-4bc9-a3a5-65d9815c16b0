package com.stepup.springrobot.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class DataResponseDTO<T> implements Serializable {
    private static final long serialVersionUID = -8091879091924046844L;

    private int status;
    private String message;
    private T data;

    public DataResponseDTO(int status, String message) {
        this.status = status;
        this.message = message;
    }
}
