package com.stepup.springrobot.dto.community;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FeatureDto {
    private Long id;

    private String title;

    private String description;

    private int likes;

    @JsonProperty("created_at")
    private Date createdAt;

    private long comments;

    @JsonProperty("user_liked")
    private boolean userLiked;
}