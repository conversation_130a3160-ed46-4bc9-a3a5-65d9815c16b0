package com.stepup.springrobot.dto.community;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CommentResDto {
    private List<CommentDto> comments;

    @JsonProperty("current_page")
    private int currentPage;

    @JsonProperty("total_page")
    private int totalPage;
}