package com.stepup.springrobot.dto.community;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CommentDto {
    private Long id;

    private String content;

    private int likes;

    @JsonProperty("feature_id")
    private Long featureId;

    @JsonProperty("account_name")
    private String accountName;

    @JsonProperty("created_at")
    private Date createdAt;

    @JsonProperty("user_liked")
    private boolean userLiked;
} 