package com.stepup.springrobot.dto.game;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EchoTowerDataResDTO {
    private Long floor;

    private Long score;

    private EchoTowerPlayerDTO player;

    private EchoTowerPlayerDTO monster;

    private List<EchoTowerWordDTO> words;
} 