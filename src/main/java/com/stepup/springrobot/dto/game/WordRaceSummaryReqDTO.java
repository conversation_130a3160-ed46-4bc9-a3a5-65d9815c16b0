package com.stepup.springrobot.dto.game;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WordRaceSummaryReqDTO {
    @NotNull(message = "score is required")
    private Long score;

    @NotNull(message = "distance is required")
    private Integer distance;

    @NotNull(message = "word_count is required")
    @JsonProperty("word_count")
    private Integer wordCount;

    private List<WordRaceHistoryReqDTO> progress;
} 