package com.stepup.springrobot.dto.game;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GameSummaryResDTO {
    private Object player;

    @JsonProperty("top_ranks")
    private List<String> topRanks;
} 