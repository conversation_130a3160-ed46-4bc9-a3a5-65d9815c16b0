package com.stepup.springrobot.dto.game;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AlphaBlastSummaryReqDTO {
    @NotNull(message = "score is required")
    private Long score;

    @NotNull(message = "character_count is required")
    @JsonProperty("character_count")
    private Integer characterCount;

    @NotNull(message = "word_count is required")
    @JsonProperty("word_count")
    private Integer wordCount;
} 