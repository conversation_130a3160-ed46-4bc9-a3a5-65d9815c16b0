package com.stepup.springrobot.dto.game;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WordRaceOpponentProgressDTO {
    @JsonProperty("is_correct")
    private boolean isCorrect;

    @JsonProperty("response_time")
    private double responseTime;
} 