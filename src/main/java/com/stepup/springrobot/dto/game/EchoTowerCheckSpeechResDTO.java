package com.stepup.springrobot.dto.game;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EchoTowerCheckSpeechResDTO {
    private Long score;

    private EchoTowerPlayerDTO player;

    private EchoTowerPlayerDTO monster;

    @JsonProperty("pronounce_result")
    private List<PronounceScoreResultDTO> pronounceResult;
} 