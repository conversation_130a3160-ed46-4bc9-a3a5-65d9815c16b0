package com.stepup.springrobot.dto.game;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WordRaceDataResDTO {
    private List<WordRaceWordDTO> words;

    @JsonProperty("power_up_words")
    private List<WordRaceWordDTO> powerUpWords;

    @JsonProperty("target_distance")
    private Integer targetDistance;

    @JsonProperty("first_opponent")
    private WordRaceOpponentDTO firstOpponent;

    @JsonProperty("second_opponent")
    private WordRaceOpponentDTO secondOpponent;
} 