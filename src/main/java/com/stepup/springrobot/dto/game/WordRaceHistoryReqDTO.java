package com.stepup.springrobot.dto.game;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WordRaceHistoryReqDTO {
    @NotNull(message = "word is required")
    private String word;

    @NotNull(message = "is_correct is required")
    @JsonProperty("is_correct")
    private Boolean isCorrect;

    @NotNull(message = "response_time is required")
    @JsonProperty("response_time")
    private Double responseTime;
} 