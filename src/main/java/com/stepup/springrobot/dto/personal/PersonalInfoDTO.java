package com.stepup.springrobot.dto.personal;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PersonalInfoDTO {
    @JsonProperty("user_id")
    private String userId;

    private String phone;

    @JsonProperty("is_complete_onboard")
    private boolean isCompleteOnboard;

    @JsonProperty("current_profile")
    private PersonalProfileDTO currentProfile;

    @JsonProperty("is_complete_entrance_test")
    private boolean isCompleteEntranceTest;
}