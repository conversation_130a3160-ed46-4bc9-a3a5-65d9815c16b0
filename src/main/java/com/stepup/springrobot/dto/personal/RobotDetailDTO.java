package com.stepup.springrobot.dto.personal;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RobotDetailDTO {
    private String name;

    private String id;

    @JsonProperty("current_version")
    private String currentVersion;

    @JsonProperty("latest_version")
    private String latestVersion;

    @JsonProperty("is_updating")
    private boolean isUpdating;
}