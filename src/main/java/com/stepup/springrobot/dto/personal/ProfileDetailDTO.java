package com.stepup.springrobot.dto.personal;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ProfileDetailDTO {
    private String id;

    private String username;

    private String avatar;

    @JsonProperty("is_current")
    private boolean isCurrent;

    @Builder.Default
    @JsonProperty("is_complete_onboard")
    private boolean isCompleteOnboard = true;

    @JsonProperty("is_complete_entrance_test")
    private boolean isCompleteEntranceTest;
}