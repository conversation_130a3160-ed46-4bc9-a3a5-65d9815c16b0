package com.stepup.springrobot.dto.personal;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SwitchProfileReqDTO {
    @NotBlank(message = "profile_id is required")
    @JsonProperty("profile_id")
    private String profileId;

    @JsonProperty("device_id")
    private String deviceId;

    @JsonProperty("app_v")
    private String appV;
}