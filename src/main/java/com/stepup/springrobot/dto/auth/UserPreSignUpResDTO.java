package com.stepup.springrobot.dto.auth;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserPreSignUpResDTO {
    private String phone;

    @JsonProperty("is_brandname")
    private boolean isBrandname;

    @JsonProperty("sms_firebase_request")
    private Integer smsFirebaseReq = 1;

    @JsonProperty("sms_brandname_request")
    private Integer smsBrandnameReq = 1;

    @JsonProperty("available_otp_methods")
    private List<String> availableOTPMethods;
}
