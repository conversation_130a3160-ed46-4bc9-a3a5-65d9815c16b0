package com.stepup.springrobot.dto.auth;

import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserCreateReqDTO implements Serializable {
    private static final long serialVersionUID = 5926468583005150707L;

    @NotBlank
    @JsonProperty("phone")
    private String phone;

    @NotBlank
    @JsonProperty("password")
    private String password;

    @JsonProperty("device_id")
    private String deviceId;

    @JsonProperty("device_token")
    private String deviceToken;

    @JsonProperty("app_v")
    private String appV;

    @JsonProperty("os")
    private String os;

    @JsonProperty("device_name")
    private String deviceName;
}
