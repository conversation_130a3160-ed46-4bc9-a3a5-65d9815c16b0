package com.stepup.springrobot.dto.auth;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class UserVerifySMSReqDTO {
    @NotNull(message = "Phone number is required")
    private String phone;

    @NotNull(message = "OTP is required")
    private String otp;

    //SMSAction
    @NotNull(message = "Action is required")
    private String action;

    private LocalDateTime time;

    @JsonProperty("device_id")
    private String deviceId;

    @JsonProperty("app_v")
    private String appV;
}