package com.stepup.springrobot.dto.auth;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stepup.springrobot.model.sms.SMSAction;
import com.stepup.springrobot.model.sms.SMSServiceProvider;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserRecoveryPWByOTPReqDTO {
    @NotNull(message = "phone number is required")
    private String phone;

    @NotNull(message = "action is required")
    private SMSAction action;

    @NotNull(message = "sms_service_provider is required")
    @JsonProperty("sms_service_provider")
    private SMSServiceProvider smsServiceProvider;

    @NotNull(message = "password is required")
    private String password;

    @JsonProperty("device_id")
    private String deviceId;

    @JsonProperty("device_token")
    private String deviceToken;
}
