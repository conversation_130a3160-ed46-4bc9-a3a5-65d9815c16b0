package com.stepup.springrobot.dto.auth;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LogoutReqDTO implements Serializable {
    @NotBlank(message = "Device id is required")
    @JsonProperty("device_id")
    private String deviceId;
}
