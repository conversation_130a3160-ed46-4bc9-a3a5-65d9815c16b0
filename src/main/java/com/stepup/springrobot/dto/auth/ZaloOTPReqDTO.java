package com.stepup.springrobot.dto.auth;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class ZaloOTPReqDTO {
    private String phone;

    @JsonProperty("template_id")
    private String templateId;

    @JsonProperty("template_data")
    private ZaloOTPDetailReqDTO zaloOTPDetailReqDTO;

    @JsonProperty("tracking_id")
    private String trackingId;
}
