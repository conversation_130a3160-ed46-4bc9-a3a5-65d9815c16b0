package com.stepup.springrobot.dto.auth;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stepup.springrobot.model.sms.SMSServiceProvider;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class UserSendSMSReqDTO {
    @NotNull(message = "Phone number is required")
    private String phone;

    @NotNull(message = "Action is required")
    @JsonProperty("action")
    private String action;

    @JsonProperty("is_brandname")
    private boolean isBrandname;

    @JsonProperty("sms_service_provider")
    private SMSServiceProvider smsServiceProvider;
}
