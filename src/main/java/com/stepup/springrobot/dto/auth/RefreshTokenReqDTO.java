package com.stepup.springrobot.dto.auth;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class RefreshTokenReqDTO {
    @NotNull(message = "Token is required")
    private String token;

    @JsonProperty("device_id")
    private String deviceId;
}
