package com.stepup.springrobot.websocket.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.service.AIRobotConversationService;
import com.stepup.springrobot.websocket.service.GoogleConversationSessionService;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.BinaryMessage;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.BinaryWebSocketHandler;

import java.net.URI;
import java.nio.ByteBuffer;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Log4j2
@Component
public class GoogleSocketConversationHandler extends BinaryWebSocketHandler {
    private final Map<WebSocketSession, GoogleConversationSessionService> sessionHandlers = new ConcurrentHashMap<>();
    private final ObjectMapper objectMapper;
    private final AIRobotConversationService aiRobotConversationService;
    private final WebSocketUtils webSocketUtils;

    public GoogleSocketConversationHandler(ObjectMapper objectMapper, AIRobotConversationService aiRobotConversationService,
                                           WebSocketUtils webSocketUtils) {
        this.objectMapper = objectMapper;
        this.aiRobotConversationService = aiRobotConversationService;
        this.webSocketUtils = webSocketUtils;
    }

    private String getRoomIdFromSession(WebSocketSession session) {
        URI uri = session.getUri();
        String query = uri.getQuery();
        if (query != null && query.contains("lesson_id")) {
            return query.split("=")[1];  // Extract roomId from query string
        }

        return null;
    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        log.info("WebSocket connection established for session: {}", session.getId());
        log.info("Lesson_id: {}", getRoomIdFromSession(session));
        log.info("Params: {}", session.getAttributes());

        String userId = webSocketUtils.getUserIdFromSocketSession(session);

        // Initialize the SpeechSessionHandler, which includes SpeechClient and other resources
        GoogleConversationSessionService handler = new GoogleConversationSessionService(session, objectMapper, aiRobotConversationService, userId);
        handler.initializeSpeechClient();

        // Store the handler associated with the session
        sessionHandlers.put(session, handler);
    }

    @Override
    protected void handleBinaryMessage(WebSocketSession session, BinaryMessage message) {
        ByteBuffer payload = message.getPayload();
        GoogleConversationSessionService handler = sessionHandlers.get(session);

        if (handler != null) {
            handler.handleAudioData(payload, false);
        }
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
        log.info("WebSocket connection closed: {}", session.getId());
        GoogleConversationSessionService handler = sessionHandlers.remove(session);
        if (handler != null) {
            handler.close();
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) {
        log.error("WebSocket error, session: {}, error: {}" , session.getId(), exception.getMessage());
        GoogleConversationSessionService handler = sessionHandlers.remove(session);
        if (handler != null) {
            handler.close();
        }

        exception.printStackTrace();
    }
}
