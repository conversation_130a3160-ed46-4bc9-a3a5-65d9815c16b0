package com.stepup.springrobot.websocket.handler;

import com.stepup.springrobot.model.chat.STTHandlerType;
import com.stepup.springrobot.websocket.service.HandlerRoutingService;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.BinaryMessage;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.AbstractWebSocketHandler;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class DynamicRoutingHandler extends AbstractWebSocketHandler {
    private final RobotSocketConversationHandler robotHandler;
    private final GoogleSocketConversationHandler googleHandler;
    private final HandlerRoutingService handlerRoutingService;
    private final WebSocketUtils webSocketUtils;
    private final Map<WebSocketSession, AbstractWebSocketHandler> sessionHandlers = new ConcurrentHashMap<>();

    public DynamicRoutingHandler(RobotSocketConversationHandler robotHandler, GoogleSocketConversationHandler googleHandler,
                                 HandlerRoutingService handlerRoutingService,
                                 WebSocketUtils webSocketUtils) {
        this.robotHandler = robotHandler;
        this.googleHandler = googleHandler;
        this.handlerRoutingService = handlerRoutingService;
        this.webSocketUtils = webSocketUtils;
    }

    @Override
    public void afterConnectionEstablished(@NotNull WebSocketSession session) throws Exception {
        String userId = webSocketUtils.getUserIdFromSocketSession(session);
        STTHandlerType handlerType = handlerRoutingService.getUserConversationSpeechToTextHandler(userId);
        AbstractWebSocketHandler selectedHandler = handlerType == STTHandlerType.GOOGLE
                ? googleHandler
                : robotHandler;

        sessionHandlers.put(session, selectedHandler);
        selectedHandler.afterConnectionEstablished(session);
    }

    @Override
    protected void handleBinaryMessage(@NotNull WebSocketSession session, BinaryMessage message) throws Exception {
        AbstractWebSocketHandler handler = sessionHandlers.get(session);
        handler.handleMessage(session, message);
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        AbstractWebSocketHandler handler = sessionHandlers.get(session);
        handler.handleMessage(session, message);
    }

    @Override
    public void afterConnectionClosed(@NotNull WebSocketSession session, CloseStatus status) throws Exception {
        AbstractWebSocketHandler handler = sessionHandlers.remove(session);
        handler.afterConnectionClosed(session, status);
    }
}
