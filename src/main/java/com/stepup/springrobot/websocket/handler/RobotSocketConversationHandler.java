package com.stepup.springrobot.websocket.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.model.chat.STTHandlerType;
import com.stepup.springrobot.security.robot.RobotJwtService;
import com.stepup.springrobot.service.AIRobotConversationService;
import com.stepup.springrobot.service.SharedService;
import com.stepup.springrobot.websocket.service.BaseConversationSessionService;
import com.stepup.springrobot.websocket.service.HandlerRoutingService;
import com.stepup.springrobot.websocket.service.RobotNewConversationSessionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.BinaryMessage;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.AbstractWebSocketHandler;

import java.io.EOFException;
import java.nio.ByteBuffer;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class RobotSocketConversationHandler extends AbstractWebSocketHandler {
    @Value("${conversation_timeout_minutes}")
    private long conversationTimeoutMinutes;

    @Value("${is_log_audio_chunk}")
    private boolean isLogAudioChunk;

    @Value("${audio_socket_host}")
    private String socketHost;

    @Value("${stress_test_robot_id}")
    private String stressTestRobotIds;

    private final Map<WebSocketSession, BaseConversationSessionService> sessionHandlers = new ConcurrentHashMap<>();

    private final ObjectMapper objectMapper;

    private final AIRobotConversationService aiRobotConversationService;

    private final WebSocketUtils webSocketUtils;

    private final HandlerRoutingService handlerRoutingService;

    private final RobotJwtService robotJwtService;

    private final SharedService sharedService;

    public RobotSocketConversationHandler(ObjectMapper objectMapper,
                                          AIRobotConversationService aiRobotConversationService,
                                          WebSocketUtils webSocketUtils,
                                          HandlerRoutingService handlerRoutingService,
                                          RobotJwtService robotJwtService,
                                          SharedService sharedService) {
        this.objectMapper = objectMapper;
        this.aiRobotConversationService = aiRobotConversationService;
        this.webSocketUtils = webSocketUtils;
        this.handlerRoutingService = handlerRoutingService;
        this.robotJwtService = robotJwtService;
        this.sharedService = sharedService;
    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) {
        String endpoint = session.getUri() != null ? session.getUri().getPath() : "unknown";
        log.info("Client: {} - (Robot - BE) Initialize WebSocket session: {}, endpoint: {}, params: {}", session.getRemoteAddress(), session.getId(), endpoint, session.getAttributes());
        String userId = webSocketUtils.getUserIdFromSocketSession(session);

        // Initialize the SpeechSessionHandler, which includes SpeechClient and other resources
        BaseConversationSessionService handler;
        STTHandlerType handlerType = handlerRoutingService.getUserConversationSpeechToTextHandler(userId);
        if (STTHandlerType.DEEP_GRAM == handlerType) {
            handler = new RobotNewConversationSessionService(session, objectMapper, aiRobotConversationService, conversationTimeoutMinutes, socketHost, userId, STTHandlerType.DEEP_GRAM, stressTestRobotIds, sharedService);
        } else {
            handler = new RobotNewConversationSessionService(session, objectMapper, aiRobotConversationService, conversationTimeoutMinutes, socketHost, userId, STTHandlerType.ASR, stressTestRobotIds, sharedService);
        }

        handler.initializeSessionConfig();

        // Store the handler associated with the session
        sessionHandlers.put(session, handler);
    }

    @Override
    protected void handleBinaryMessage(WebSocketSession session, BinaryMessage message) {
        ByteBuffer payload = message.getPayload();
        BaseConversationSessionService handler = sessionHandlers.get(session);
        if (isLogAudioChunk) {
            log.info("(Robot - BE) Received audio chunk: {}", payload.array());
        }

        if (handler != null) {
            handler.handleAudioData(payload);
        }
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) {
        String payload = message.getPayload();
        BaseConversationSessionService handler = sessionHandlers.get(session);

        try {
            // Parse the message to check if it's an authentication message
            Map<String, Object> messageMap = objectMapper.readValue(payload, Map.class);
            if ("AUTH".equals(messageMap.get("type"))) {
                String token = (String) messageMap.get("token");
                if (robotJwtService.validateToken(token)) {
                    // Delete the token after validation
                    robotJwtService.revokeToken(token);

                    // Send authentication success response
                    Map<String, String> response = Map.of("type", "AUTH_SUCCESS");
                    session.sendMessage(new TextMessage(objectMapper.writeValueAsString(response)));
                    log.info("WebSocket authentication successful for session: {}", session.getId());
                    return;
                } else {
                    // Send authentication failure response
                    Map<String, String> response = Map.of("type", "AUTH_FAILED");
                    session.sendMessage(new TextMessage(objectMapper.writeValueAsString(response)));
                    log.error("WebSocket authentication failed for session: {}", session.getId());
                    session.close();
                    return;
                }
            }
        } catch (Exception e) {
            log.error("Error handling authentication message: {}", e.getMessage());
        }

        // Handle other messages normally
        if (handler != null) {
            handler.handleTextMessage(payload);
        }
        log.info("Client: {} - (Robot - BE) Received text message: {}", session.getRemoteAddress(), payload);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
        log.info("Client: {} - (Robot - BE) WebSocket connection closed: {}, status: {}", session.getRemoteAddress(), session.getId(), status);

        aiRobotConversationService.generateConversationSummary(session.getId());

        BaseConversationSessionService handler = sessionHandlers.remove(session);
        if (handler != null) {
            handler.close();
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) {
        boolean isClientError = exception instanceof EOFException ||
                exception.getMessage().contains("Connection reset by peer");
        log.error("Client: {} - (Robot - BE) WebSocket error: {}, session: {}, error: {}",
                session.getRemoteAddress(),
                isClientError ? "client-side" : "server-side",
                session.getId(),
                exception.getMessage());
        BaseConversationSessionService handler = sessionHandlers.remove(session);
        if (handler != null) {
            handler.close();
        }

        exception.printStackTrace();
    }
}
