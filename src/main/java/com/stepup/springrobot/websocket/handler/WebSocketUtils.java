package com.stepup.springrobot.websocket.handler;

import com.stepup.springrobot.security.JwtService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.WebSocketSession;

import java.util.Map;

@Slf4j
@Service
public class WebSocketUtils {
    @Autowired
    private JwtService jwtService;

    public String getUserIdFromSocketSession(WebSocketSession session) {
        String userId = null;
        try {
            String token = (String) session.getAttributes().get("token");
            if (!StringUtils.isEmpty(token)) {
                Map<String, Object> claims = jwtService.extractAllClaims(token);
                userId = claims.get("user_id").toString();
                log.info("user_id: {}", userId);
            }
        } catch (Exception e) {
            log.error("Lỗi lấy userId từ token: {}", e.getMessage(), e);
        }

        if (StringUtils.isEmpty(userId)) {
            userId = (String) session.getAttributes().get("robot_id");
        }

        if (StringUtils.isEmpty(userId)) {
            userId = "undefined";
        }

        return userId;
    }
}
