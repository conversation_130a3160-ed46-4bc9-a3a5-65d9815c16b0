package com.stepup.springrobot.websocket.service;

import com.stepup.springrobot.model.chat.RobotUserConversationSTTHandler;
import com.stepup.springrobot.model.chat.STTHandlerType;
import com.stepup.springrobot.repository.chat.RobotUserConversationSTTHandlerRepository;
import org.springframework.stereotype.Service;

@Service
public class HandlerRoutingService {
    private final RobotUserConversationSTTHandlerRepository robotUserConversationSTTHandlerRepository;

    public HandlerRoutingService(RobotUserConversationSTTHandlerRepository robotUserConversationSTTHandlerRepository) {
        this.robotUserConversationSTTHandlerRepository = robotUserConversationSTTHandlerRepository;
    }

    public STTHandlerType getUserConversationSpeechToTextHandler(String userId) {
        RobotUserConversationSTTHandler handler = robotUserConversationSTTHandlerRepository.findByUserId(userId);
        if (handler == null) {
            return STTHandlerType.ASR;
        }

        return handler.getSpeechToText();
    }
}
