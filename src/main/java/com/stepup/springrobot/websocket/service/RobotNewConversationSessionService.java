package com.stepup.springrobot.websocket.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.dto.chat.*;
import com.stepup.springrobot.model.chat.ConversationLogType;
import com.stepup.springrobot.model.chat.RobotConversationResponseType;
import com.stepup.springrobot.model.chat.STTHandlerType;
import com.stepup.springrobot.service.AIRobotConversationService;
import com.stepup.springrobot.service.SharedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.time.Duration;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

@Slf4j
public class RobotNewConversationSessionService extends BaseConversationSessionService {
    private final long conversationTimeoutMinutes;

    private final Instant startConversationTime;

    public RobotNewConversationSessionService(WebSocketSession session, ObjectMapper objectMapper, AIRobotConversationService aiRobotConversationService, long conversationTimeoutMinutes,
                                              String socketHost, String userId, STTHandlerType sttHandlerType, String stressTestRobotIds, SharedService sharedService) {
        super(session, objectMapper, aiRobotConversationService, conversationTimeoutMinutes, socketHost, userId, sttHandlerType, stressTestRobotIds, sharedService);
        this.conversationTimeoutMinutes = conversationTimeoutMinutes;
        this.startConversationTime = Instant.now();
    }

    @Override
    public void initializeSessionConfig() {
        startPingTask();
        Instant start = Instant.now();
        Long botId = getBotIdParam();
        StringBuilder logMessage = new StringBuilder();
        boolean isWebMvp = getIsFromWebMvp();
        RobotConversationMsgResDTO robotConversationMsgResDTO = aiRobotConversationService.sendCustomUserSentence(userId, "Hello", session.getId(), session.getRemoteAddress().toString(), botId, logMessage, STTHandlerType.ASR, null, isWebMvp);
        language = robotConversationMsgResDTO.getLanguage();
        List<AIRobotConversationResDTO> resDTOS = robotConversationMsgResDTO.getMessages();
        Long conversationId = resDTOS.get(0).getConversationId();

        RobotConversationMessageDTO audioMessage = RobotConversationMessageDTO.builder()
                .type(RobotConversationResponseType.CHAT_RESPONSE)
                .data(RobotChatResponseDTO.builder()
                        .messages(robotConversationMsgResDTO.getResponseMessages())
                        .listeningEmotion(robotConversationMsgResDTO.getListeningEmotion())
                        .gifs(robotConversationMsgResDTO.getGifs())
                        .answerMode(robotConversationMsgResDTO.getAnswerMode())
                        .build())
                .socketSessionId(session.getId())
                .conversationId(conversationId)
                .build();

        if (session.isOpen()) {
            try {
                String chatResponse = objectMapper.writeValueAsString(audioMessage);
                session.sendMessage(new TextMessage(chatResponse));  // Send the bot response back to the user
                long duration = Duration.between(start, Instant.now()).toMillis();
                sharedService.saveConversationLog(resDTOS.get(0).getConversationId(), ConversationLogType.SERVER_RESPONSE, chatResponse, duration);
                logInfo("(BE - Robot) Send bot response - conversation_id: " + resDTOS.get(0).getConversationId() + ", response: " + chatResponse);
                String responseTime = "ROBOT - res time: " + duration + "ms";
                logMessage.insert(0, responseTime);
                log.error(logMessage.toString());
            } catch (IOException e) {
                logError(e, "Failed to send first bot response");
            }
        }
    }

    protected void sendEndConversationTimeOut() {
        List<RobotResponseMessageDTO> responseMessageDTOS = List.of(RobotResponseMessageDTO.builder()
                .text(CodeDefine.CONVERSATION_FINISH_SENTENCE)
                .audio(CodeDefine.CONVERSATION_FINISH_AUDIO)
                .build());

        RobotConversationMessageDTO audioMessage = RobotConversationMessageDTO.builder()
                .type(RobotConversationResponseType.CHAT_RESPONSE)
                .data(RobotChatResponseDTO.builder()
                        .messages(responseMessageDTOS)
                        .hasNextMessage(false)
                        .isEndConversation(true)
                        .build())
                .socketSessionId(session.getId())
                .build();

        if (session.isOpen()) {
            try {
                String chatResponse = objectMapper.writeValueAsString(audioMessage);
                session.sendMessage(new TextMessage(chatResponse));  // Send the bot response back to the user
                logInfo("(BE - Robot) Send bot response: " + chatResponse);
            } catch (IOException e) {
                logError(e, "Failed to send last bot response");
            }
        }
    }

    @Override
    protected byte[] convertAudioFormat(byte[] audioData) {
        return audioData;
    }

    @Override
    protected File convertToWavFile(byte[] audioData, String fileName) {
        File outputFile = new File(fileName);

        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
             FileOutputStream fileOutputStream = new FileOutputStream(outputFile)) {

            // Calculate data size from the actual audio data
            int dataSize = audioData.length;
            byte[] header = createWavHeader(dataSize);

            // Combine header and audio data
            outputStream.write(header);
            outputStream.write(audioData);

            // Write to file
            fileOutputStream.write(outputStream.toByteArray());

            log.info("WAV file created: {}", outputFile.getAbsolutePath());
            return outputFile;

        } catch (IOException e) {
            log.error("Error converting audio data to WAV", e);
            if (outputFile.exists()) {
                outputFile.delete();
            }
            return null;
        }
    }
    private static byte[] createWavHeader(int dataSize) {
        int HEADER_SIZE = 44;
        int CHANNELS = 1;
        int SAMPLE_RATE = 16000;
        int BITS_PER_SAMPLE = 16;
        ByteBuffer buffer = ByteBuffer.allocate(HEADER_SIZE);
        buffer.order(ByteOrder.LITTLE_ENDIAN);

        // RIFF header
        buffer.put("RIFF".getBytes());                    // ChunkID
        buffer.putInt(dataSize + 36);                     // ChunkSize
        buffer.put("WAVE".getBytes());                    // Format
        // fmt subchunk
        buffer.put("fmt ".getBytes());                    // Subchunk1ID
        buffer.putInt(16);                                // Subchunk1Size
        buffer.putShort((short) 1);                       // AudioFormat (PCM)
        buffer.putShort((short) CHANNELS);                // NumChannels
        buffer.putInt(SAMPLE_RATE);                       // SampleRate
        buffer.putInt(SAMPLE_RATE * CHANNELS * BITS_PER_SAMPLE / 8);  // ByteRate
        buffer.putShort((short) (CHANNELS * BITS_PER_SAMPLE / 8));    // BlockAlign
        buffer.putShort((short) BITS_PER_SAMPLE);         // BitsPerSample

        // data subchunk
        buffer.put("data".getBytes());                    // Subchunk2ID
        buffer.putInt(dataSize);                          // Subchunk2Size

        return buffer.array();
    }

    @Override
    protected RobotConversationMessageDTO handleGetBotResponse(String transcript, File file, String sessionId, String speechToTextResponse, Long botId, StringBuilder logMessage) {
        if (!Arrays.asList(stressTestRobotIds.split(",")).contains(userId) && Duration.between(startConversationTime, Instant.now()).toMillis() > conversationTimeoutMinutes * 60 * 1000) {
            log.error("======== Hết thời gian hội thoại");
            sendEndConversationTimeOut();
            return null;
        }

        String fileName = file.getName();
        RobotConversationMsgResDTO robotConversationMsgResDTO = aiRobotConversationService.getRobotResponse(userId, transcript, file, sessionId, speechToTextResponse, session.getRemoteAddress().toString(), botId, logMessage, STTHandlerType.ASR, true);
        language = robotConversationMsgResDTO.getLanguage();
        List<AIRobotConversationResDTO> resDTOS = robotConversationMsgResDTO.getMessages();
        String status = resDTOS.get(resDTOS.size() - 1).getStatus();
        if (Objects.equals(status, "ACTION")) {
            CompletableFuture.runAsync(() -> handleGetMoreResponse(botId, logMessage, aiRobotConversationService.getUserAudioUrl(fileName)));
        }

        boolean hasNextMessage = Objects.equals(status, "ACTION");
        return RobotConversationMessageDTO.builder()
                .type(RobotConversationResponseType.CHAT_RESPONSE)
                .data(RobotChatResponseDTO.builder()
                        .messages(robotConversationMsgResDTO.getResponseMessages())
                        .hasNextMessage(hasNextMessage)
                        .listeningEmotion(hasNextMessage ? null : robotConversationMsgResDTO.getListeningEmotion())
                        .answerMode(robotConversationMsgResDTO.getAnswerMode())
                        .isEndConversation(Objects.equals(status, "END"))
                        .build())
                .socketSessionId(session.getId())
                .conversationId(resDTOS.get(0).getConversationId())
                .build();
    }

    private void handleGetMoreResponse(Long botId, StringBuilder logMessage, String userAudio) {
        Instant start = Instant.now();
        RobotConversationMsgResDTO robotConversationMsgResDTO = aiRobotConversationService.sendCustomUserSentence(userId, "ACTION", session.getId(), session.getRemoteAddress().toString(), botId, logMessage, STTHandlerType.ASR, userAudio, false);
        language = robotConversationMsgResDTO.getLanguage();
        List<AIRobotConversationResDTO> resDTOS = robotConversationMsgResDTO.getMessages();
        Long conversationId = resDTOS.get(0).getConversationId();
        RobotConversationMessageDTO stallingMsg = RobotConversationMessageDTO.builder()
                .type(RobotConversationResponseType.CHAT_RESPONSE)
                .data(RobotChatResponseDTO.builder()
                        .messages(robotConversationMsgResDTO.getResponseMessages())
                        .listeningEmotion(robotConversationMsgResDTO.getListeningEmotion())
                        .answerMode(robotConversationMsgResDTO.getAnswerMode())
                        .build())
                .socketSessionId(session.getId())
                .conversationId(conversationId)
                .build();
        try {
            String chatResponse = objectMapper.writeValueAsString(stallingMsg);
            session.sendMessage(new TextMessage(chatResponse));  // Send the bot response back to the user
            sharedService.saveConversationLog(conversationId, ConversationLogType.SERVER_RESPONSE, chatResponse, Duration.between(start, Instant.now()).toMillis());
            logInfo("(BE - Robot) Send bot response - conversation_id: " + conversationId + ", response: " + chatResponse);
        } catch (IOException e) {
            logError(e, "Failed to send bot response");
        }
    }

    @Override
    protected void handleSendStalling(StringBuilder logMessage, String userAnswer) {
        Instant startStalling = Instant.now();
        List<RobotResponseMessageDTO> stallingMsgs = aiRobotConversationService.getStallingMessage(session.getId(), userAnswer);
        RobotConversationMessageDTO stallingMsg = RobotConversationMessageDTO.builder()
                .type(RobotConversationResponseType.CHAT_STALLING)
                .data(RobotChatResponseDTO.builder()
                        .messages(stallingMsgs)
                        .build())
                .socketSessionId(session.getId())
                .build();
        try {
            String chatResponse = objectMapper.writeValueAsString(stallingMsg);
            long duration = Duration.between(startStalling, Instant.now()).toMillis();
            sharedService.saveConversationLog(stallingMsgs.get(0).getConversationId(), ConversationLogType.SERVER_RESPONSE, chatResponse, duration);
            if (hasReturnedChatResponse.get()) {
                return;
            }

            session.sendMessage(new TextMessage(chatResponse));  // Send the bot response back to the user
            logMessage.append(", stalling: ").append(duration).append("ms");
            logInfo("(BE - Robot) Send stalling bot response: " + chatResponse);
        } catch (IOException e) {
            logError(e, "Failed to send bot response");
        }
    }
}
