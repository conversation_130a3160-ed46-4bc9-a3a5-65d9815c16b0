package com.stepup.springrobot.websocket.service;

import com.stepup.springrobot.service.UtilsService;
import lombok.extern.log4j.Log4j2;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.net.URI;
import java.nio.ByteBuffer;
import java.util.HashMap;
import java.util.Map;

@Service
@Log4j2
public class LocalFileStreamingEmulationService {
    private static final int CHUNK_SIZE = 8192; // Adjust based on your needs
    private WebSocketClient webSocketClient;

    @Value("${test_audio_socket_host}")
    private String socketHost;

    public void streamFile(MultipartFile multipartFile) {
        File file = UtilsService.convertMultiPartFileToFile(multipartFile, 1L);
        start(file);
    }

    public void start(File file) {
        try {
            webSocketClient = new WebSocketClient(new URI(socketHost)) {
                @Override
                public void onOpen(ServerHandshake handshakedata) {
                    log.info("Connected to WebSocket server: {}", socketHost);
                    startStreaming(file);
                }

                @Override
                public void onMessage(String message) {
                    log.info("Received transcription: " + message);
                }

                @Override
                public void onClose(int code, String reason, boolean remote) {
                    log.info("WebSocket connection closed: " + reason);
                    file.delete();
                }

                @Override
                public void onError(Exception ex) {
                    log.error("WebSocket error: " + ex.getMessage());
                }
            };

            webSocketClient.connect();
        } catch (Exception e) {
            log.info("Error initializing WebSocket client: " + e.getMessage());
        }
    }

    private void startStreaming(File file) {
        new Thread(() -> {
            try (FileInputStream fileInputStream = new FileInputStream(file.getAbsoluteFile())) {
                byte[] buffer = new byte[CHUNK_SIZE];
                int bytesRead;

                while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                    if (webSocketClient.isOpen()) {
                        // Create a properly sized byte array for the chunk
                        byte[] chunk = new byte[bytesRead];
                        System.arraycopy(buffer, 0, chunk, 0, bytesRead);

//                        // Convert 16-bit PCM to float
//                        float[] floatData = new float[bytesRead / 2]; // 2 bytes per sample for 16-bit
//                        for (int i = 0; i < floatData.length; i++) {
//                            // Combine two bytes into a 16-bit sample
//                            short sample = (short) ((chunk[i * 2 + 1] << 8) | (chunk[i * 2] & 0xFF));
//                            // Convert to float in range [-1.0, 1.0]
//                            floatData[i] = sample / 32768.0f;
//                        }
//
//                        // Convert float to 32-bit float byte representation
//                        byte[] floatBytes = new byte[floatData.length * 4];
//                        for (int i = 0; i < floatData.length; i++) {
//                            int bits = Float.floatToRawIntBits(floatData[i]);
//                            int idx = i * 4;
//                            floatBytes[idx] = (byte) (bits & 0xFF);
//                            floatBytes[idx + 1] = (byte) ((bits >> 8) & 0xFF);
//                            floatBytes[idx + 2] = (byte) ((bits >> 16) & 0xFF);
//                            floatBytes[idx + 3] = (byte) ((bits >> 24) & 0xFF);
//                        }

                        // Send the audio chunk
                        webSocketClient.send(ByteBuffer.wrap(chunk));
                        log.info("Sent audio chunk of size: " + chunk.length);

                        // Add a small delay to control streaming rate
                        Thread.sleep(50);
                    } else {
                        break;
                    }
                }

                log.info("Audio streaming complete");

                // Send end-of-stream marker if needed
                webSocketClient.send("EOS");
            } catch (Exception e) {
                log.info("Error streaming audio: {} ", e.getMessage());
            } finally {
                try {
                    Thread.sleep(20000);
                } catch (InterruptedException e) {
                    log.info("Error streaming audio: {} ", e.getMessage());
                }
                webSocketClient.close();
            }
        }).start();
    }
}
