package com.stepup.springrobot.exception.handler;

import com.stepup.springrobot.exception.base.BaseException;
import com.stepup.springrobot.exception.business.user.TokenExpiredException;
import com.stepup.springrobot.exception.dto.ErrorResponse;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.validation.UnexpectedTypeException;
import java.util.HashMap;
import java.util.Map;

@Log4j2
@RestControllerAdvice
public class GlobalExceptionHandler {
    @ExceptionHandler(BaseException.class)
    public ResponseEntity<ErrorResponse> handleBaseException(BaseException e) {
        log.error("An error occurred: ", e);

        ErrorResponse errorResponse = ErrorResponse.builder()
                .message(e.getMessage())
                .status(e.getStatus())
                .data(e.getDetails())
                .build();

        return ResponseEntity
                .status(e.getHttpStatus())
                .body(errorResponse);
    }

    @ExceptionHandler(TokenExpiredException.class)
    public ResponseEntity<ErrorResponse> handleTokenExpiredException(BaseException e) {
        log.error("An error occurred: ", e);

        ErrorResponse errorResponse = ErrorResponse.builder()
                .message(e.getMessage())
                .status(e.getStatus())
                .data(e.getDetails())
                .build();

        return ResponseEntity
                .status(e.getHttpStatus())
                .body(errorResponse);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<Object> handleValidationExceptions(MethodArgumentNotValidException ex) {
        Map<String, String> errors = new HashMap<>();

        ex.getBindingResult().getFieldErrors().forEach(error -> {
            String fieldName = error.getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });

        ErrorResponse errorResponse = ErrorResponse.builder()
                .message("Dữ liệu không hợp lệ. Hãy kiểm tra lại nha!")
                .status(HttpStatus.BAD_REQUEST.value())
                .data(errors)
                .build();

        return ResponseEntity
                .status(HttpStatus.BAD_REQUEST.value())
                .body(errorResponse);
    }

    @ExceptionHandler(UnexpectedTypeException.class)
    public ResponseEntity<Object> handleUnexpectedTypeException(UnexpectedTypeException ex) {
        String message = "Validation error: " + ex.getMessage();
        ErrorResponse errorResponse = ErrorResponse.builder()
                .message(message)
                .status(HttpStatus.BAD_REQUEST.value())
                .build();

        return ResponseEntity
                .status(HttpStatus.BAD_REQUEST.value())
                .body(errorResponse);
    }
}
