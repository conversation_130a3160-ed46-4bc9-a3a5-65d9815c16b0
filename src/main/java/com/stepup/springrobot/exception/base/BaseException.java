package com.stepup.springrobot.exception.base;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public class BaseException extends RuntimeException {
    private final Integer status;

    private final String message;

    private final Integer httpStatus;

    private final Map<String, Object> details;

    public BaseException(ErrorCode error, Object... args) {
        super(error.formatMessage(args));
        this.status = error.getCode();
        this.message = error.formatMessage(args);
        this.httpStatus = error.getHttpStatus();
        this.details = new HashMap<>();
    }

    public BaseException addDetail(String key, Object value) {
        details.put(key, value);
        return this;
    }
}
