package com.stepup.springrobot.exception.base;

import lombok.Getter;
import org.springframework.http.HttpStatus;

@Getter
public enum ErrorCode {
    // Authentication
    UNAUTHORIZED(HttpStatus.UNAUTHORIZED.value(), "Unauthorized access", HttpStatus.UNAUTHORIZED.value()),
    EXPIRED_TOKEN(40101, "Access token hết hạn, bạn hãy đăng nhập lại nhé!", HttpStatus.UNAUTHORIZED.value()),
    EXPIRED_REFRESH_TOKEN(40103, "Refresh token hết hạn, bạn hãy đăng nhập lại nhé!", HttpStatus.UNAUTHORIZED.value()),
    INVALID_TOKEN(40102, "Token không hợp lệ, bạn hãy đăng nhập lại nhé!", HttpStatus.UNAUTHORIZED.value()),
    INVALID_PASSWORD(HttpStatus.UNAUTHORIZED.value(), "Sai mật khẩu.", HttpStatus.UNAUTHORIZED.value()),
    ACCESS_DENIED(HttpStatus.FORBIDDEN.value(), "Access denied to resource {0}", HttpStatus.FORBIDDEN.value()),
    INVALID_OTP(HttpStatus.BAD_REQUEST.value(), "%s", HttpStatus.BAD_REQUEST.value()),
    ANONYMOUS_ACCESS(HttpStatus.UNAUTHORIZED.value(), "Bạn không thể thực hiện thao tác này. Chúng tớ không biết bạn là ai!", HttpStatus.UNAUTHORIZED.value()),
    PHONE_DELETE_PERMISSION_DENY(HttpStatus.FORBIDDEN.value(), "The phone number is not allowed for this feature", HttpStatus.FORBIDDEN.value()),

    // Request
    TOO_MANY_REQUEST(HttpStatus.TOO_MANY_REQUESTS.value(), "Quá limit: %s", HttpStatus.TOO_MANY_REQUESTS.value()),

    // User
    USER_NOT_FOUND(HttpStatus.NOT_FOUND.value(), "Số điện thoại chưa được đăng ký", HttpStatus.NOT_FOUND.value()),
    USER_ALREADY_EXISTS(HttpStatus.CONFLICT.value(), "Tài khoản đã tồn tại, bạn hãy đăng nhập luôn nha ^_^", HttpStatus.CONFLICT.value()),
    INVALID_USER_DATA(HttpStatus.BAD_REQUEST.value(), "Thông tin không hợp lệ: %s", HttpStatus.BAD_REQUEST.value()),
    USER_NOT_ACTIVE(HttpStatus.LOCKED.value(), "Tài khoản đã bị khóa.", HttpStatus.LOCKED.value()),

    // Learn
    CONTENT_NOT_FOUND(HttpStatus.NOT_FOUND.value(), "Content not found. Type: %s - id: %s", HttpStatus.NOT_FOUND.value()),
    INCOMPLETE_ONBOARDING(HttpStatus.FORBIDDEN.value(), "Bạn cần hoàn thành onboard đã nhé.", HttpStatus.FORBIDDEN.value()),

    // Product
    PRODUCT_NOT_FOUND(HttpStatus.NOT_FOUND.value(), "Product not found with id: {0}", HttpStatus.NOT_FOUND.value()),
    INSUFFICIENT_STOCK(HttpStatus.CONFLICT.value(), "Insufficient stock for product {0}. Available: {1}, Requested: {2}", HttpStatus.CONFLICT.value()),

    // Validation
    INVALID_FORMAT(HttpStatus.BAD_REQUEST.value(), "%s", HttpStatus.BAD_REQUEST.value()),

    // General
    CONFLICT_ENTITY(HttpStatus.CONFLICT.value(), "Dữ liệu %s đã tồn tại", HttpStatus.CONFLICT.value()),
    MISSING_PARAM(HttpStatus.BAD_REQUEST.value(), "Thiếu thông tin %s", HttpStatus.BAD_REQUEST.value()),

    // Audio
    AUDIO_ERROR(HttpStatus.BAD_REQUEST.value(), "File audio gửi lên bị lỗi, bạn vui lòng kiểm tra lại.", HttpStatus.BAD_REQUEST.value()),
    AUDIO_TOO_SHORT(HttpStatus.BAD_REQUEST.value(), "Bạn nói ngắn quá mình chưa kịp ghi âm. Bạn nói chậm lại một xíu nha!", HttpStatus.BAD_REQUEST.value()),
    AUDIO_TOO_LONG(HttpStatus.BAD_REQUEST.value(), "Nghe bạn nói thích ghê, nhưng dài quá mình không nhớ hết. Bạn thử lại nhé!", HttpStatus.BAD_REQUEST.value()),

    // MQTT communication
    COMMUNICATION_TIMEOUT(HttpStatus.REQUEST_TIMEOUT.value(), "Quá thời gian chờ kết nối. Bạn thử lại nhé!", HttpStatus.REQUEST_TIMEOUT.value());

    private final Integer code;

    private final String message;

    private final int httpStatus;

    ErrorCode(Integer code, String message, int httpStatus) {
        this.code = code;
        this.message = message;
        this.httpStatus = httpStatus;
    }

    public String formatMessage(Object... args) {
        return String.format(message, args);
    }
}
