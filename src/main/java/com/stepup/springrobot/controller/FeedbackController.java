package com.stepup.springrobot.controller;

import com.stepup.springrobot.dto.feedback.FeedbackDTO;
import com.stepup.springrobot.dto.feedback.UserFeedbackDTO;
import com.stepup.springrobot.dto.feedback.UserFeedbackReqDTO;
import com.stepup.springrobot.service.FeedbackService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/robot/api/v1/feedback")
public class FeedbackController extends BaseController {
    private final FeedbackService feedbackService;

    @Autowired
    public FeedbackController(FeedbackService feedbackService) {
        this.feedbackService = feedbackService;
    }

    @Operation(summary = "Get all feedbacks")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "List of feedbacks",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE))
    })
    @GetMapping
    public ResponseEntity<List<FeedbackDTO>> getAllFeedbacks() {
        return ResponseEntity.ok(feedbackService.getAllFeedbacks());
    }

    @PostMapping(value = "/user", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<UserFeedbackDTO> saveUserFeedback(
            @Parameter(
                    description = "User feedback data with optional base64 encoded files",
                    example = "{\n" +
                            "  \"description\": \"Feedback description\",\n" +
                            "  \"feedbackId\": \"1\",\n" +
                            "  \"rating\": 1,\n" +
                            "  \"accountId\": \"user_id\",\n" +
                            "  \"files\": [{\n" +
                            "    \"fileName\": \"image.jpg\",\n" +
                            "    \"contentType\": \"image/jpeg\",\n" +
                            "    \"base64Content\": \"base64EncodedStringHere\"\n" +
                            "  }]\n" +
                            "}"
            )
            @RequestBody UserFeedbackReqDTO request) {
        return ResponseEntity.ok(feedbackService.saveUserFeedback(request));
    }
}
