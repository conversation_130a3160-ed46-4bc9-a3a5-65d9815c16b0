package com.stepup.springrobot.controller;

import com.stepup.springrobot.dto.study.AssignLessonReqDTO;
import com.stepup.springrobot.service.StudyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.IOException;

@RestController
@CrossOrigin("*")
@RequestMapping("robot/api/v1/study")
public class StudyController extends BaseController {
    @Autowired
    private StudyService studyService;

    @GetMapping("/plan")
    public ResponseEntity<?> getStudyData(HttpServletRequest request) throws IOException {
        return success(studyService.getStudyPlan(request));
    }

    @GetMapping("/lessons")
    public ResponseEntity<?> getLessonsByTopics(HttpServletRequest request, @RequestParam("topic_id") Long topicId) throws IOException {
        return success(studyService.getLessonsByTopicId(request, topicId));
    }

    @GetMapping("/lesson/result")
    public ResponseEntity<?> getLessonResult(HttpServletRequest request, @RequestParam("lesson_id") Long lessonId) throws IOException {
        return success(studyService.getLessonResult(request, lessonId));
    }

    @GetMapping("/lesson/conversation")
    public ResponseEntity<?> getLessonConversation(HttpServletRequest request, @RequestParam("lesson_id") Long lessonId) throws IOException {
        return success(studyService.getLessonConversation(request, lessonId));
    }

    @PutMapping("/lesson/assign")
    public ResponseEntity<?> assignLesson(HttpServletRequest request, @Valid @RequestBody AssignLessonReqDTO reqDTO) throws IOException {
        return success(studyService.assignLesson(request, reqDTO));
    }
}
