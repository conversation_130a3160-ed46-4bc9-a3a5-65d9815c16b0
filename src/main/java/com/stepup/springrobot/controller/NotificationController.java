package com.stepup.springrobot.controller;

import com.stepup.springrobot.dto.notification.PushNotificationReqDTO;
import com.stepup.springrobot.service.NotificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

@RestController
@CrossOrigin("*")
@RequestMapping("/robot/api/v1/notification")
public class NotificationController extends BaseController {

    @Autowired
    private NotificationService notificationService;


    @PostMapping("/test")
    public ResponseEntity<?> getSongs(HttpServletRequest request, @RequestBody PushNotificationReqDTO pushNotificationReqDTO) throws IOException {
        notificationService.sendNotificationToUserId(pushNotificationReqDTO);
        return success();
    }
}
