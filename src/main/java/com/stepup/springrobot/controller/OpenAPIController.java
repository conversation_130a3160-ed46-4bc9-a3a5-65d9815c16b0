package com.stepup.springrobot.controller;

import com.stepup.springrobot.dto.chat.ConversationReactionReqDTO;
import com.stepup.springrobot.service.OpenAPIService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@Controller
@CrossOrigin("*")
@RequestMapping("robot/api/v1/open-api")
public class OpenAPIController extends BaseController {
    @Autowired
    private OpenAPIService openAPIService;

    @PostMapping("/video")
    public ResponseEntity<?> getLearnLessonData(@RequestParam(value = "socket_session_id") String socketSessionId,
                                                @RequestParam(value = "file") MultipartFile file) {
        return success(openAPIService.uploadVideoBySocketSessionId(socketSessionId, file));
    }

    @PostMapping("/conversation/reaction")
    public ResponseEntity<?> saveConversationReaction(@RequestBody ConversationReactionReqDTO reqDTO) {
        return success(openAPIService.saveConversationLikeReaction(reqDTO));
    }
}
