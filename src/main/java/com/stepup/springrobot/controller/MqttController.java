package com.stepup.springrobot.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.stepup.springrobot.dto.mqtt.PublishMqttMessageReqDTO;
import com.stepup.springrobot.dto.mqtt.SubscribeMqttTopicReqDTO;
import com.stepup.springrobot.dto.mqtt.UnSubscribeMqttTopicReqDTO;
import com.stepup.springrobot.service.MqttService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@CrossOrigin("*")
@RequestMapping("robot/api/v1/mqtt")
public class MqttController extends BaseController {
    @Autowired
    private MqttService mqttService;

    @PostMapping("/publish")
    public ResponseEntity<?> publishMessage(@RequestBody PublishMqttMessageReqDTO reqDTO) throws JsonProcessingException {
        mqttService.sendMessage(reqDTO);
        return success();
    }

    @PostMapping("/subscribe")
    public ResponseEntity<?> subscribe(@RequestBody SubscribeMqttTopicReqDTO reqDTO) {
        mqttService.subscribeToTopic(reqDTO);
        return success();
    }

    @PostMapping("/unsubscribe")
    public ResponseEntity<?> unsubscribe(@RequestBody UnSubscribeMqttTopicReqDTO reqDTO) {
        mqttService.unsubscribeFromTopic(reqDTO);
        return success();
    }

    @GetMapping("/save_mqtt_acl")
    public ResponseEntity<?> saveMqttAcl(@RequestParam("username") String username) {
        return success(mqttService.saveMqttAclToRedis(username));
    }
}
