package com.stepup.springrobot.controller;

import com.stepup.springrobot.service.LearnService;
import com.stepup.springrobot.service.OnboardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

@RestController
@CrossOrigin("*")
@RequestMapping("/api/v1/learn")
public class LearnController extends BaseController {
    @Autowired
    private LearnService learnService;

    @Autowired
    private OnboardService onboardService;

    @GetMapping("/lessons")
    public ResponseEntity<?> getAllLessons() {
        return success(learnService.getAllLessons());
    }

    @GetMapping("/lesson_data")
    public ResponseEntity<?> getLearnLessonData(@RequestParam(value = "lesson_id") Long lessonId) {
        return success(learnService.getLearnData(lessonId));
    }

    @GetMapping("/lesson_data/detail")
    public ResponseEntity<?> getLearnLessonData(@RequestParam(value = "lesson_detail_id") String lessonDetailId) throws IOException {
        return success(learnService.getLearnDetailData(lessonDetailId));
    }

    @GetMapping("/onboarding")
    public ResponseEntity<?> getOnboardingData() {
        return success(onboardService.getOnboardQuestion());
    }
}
