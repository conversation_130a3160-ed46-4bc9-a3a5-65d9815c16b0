package com.stepup.springrobot.controller.speech;

import com.stepup.springrobot.controller.BaseController;
import com.stepup.springrobot.service.speech.AsrService;
import com.stepup.springrobot.service.speech.CheckSpeechService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

@RestController
@CrossOrigin("*")
@RequestMapping("robot-speech/api/v1/robot-open/check")
public class SpeechOpenController extends BaseController {
    @Autowired
    private CheckSpeechService checkSpeechService;

    @Autowired
    private AsrService asrService;

    @PostMapping("/speech")
    public ResponseEntity<?> checkSpeech(@RequestParam("audio-file") MultipartFile file,
                                         @RequestParam("text-refs") String textRefs,
                                         HttpServletRequest request) throws IOException {
        return success(checkSpeechService.uploadSpeechOpen(request, file, textRefs));
    }

    /**
     * Nhận diện text từ file audio
     * Giới hạn số lần dùng trong 1 ngày
     */
    @PostMapping("/asr")
    public ResponseEntity<?> getAudioScriptEn(@RequestParam("audio-file") MultipartFile file, HttpServletRequest request) throws Exception {
        return success(asrService.generateSpeechToText(request, file, true));
    }
}
