package com.stepup.springrobot.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.alarm.AlarmScheduleDeleteReqDTO;
import com.stepup.springrobot.dto.alarm.AlarmScheduleReqDTO;
import com.stepup.springrobot.service.AlarmService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.Authorization;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@RestController
@CrossOrigin("*")
@RequestMapping("robot/api/v1/alarm")
@Api(tags = "Alarm", description = "Alarm management APIs")
public class AlarmController extends BaseController {
    @Autowired
    private AlarmService alarmService;

    @ApiOperation(value = "Get all alarm schedules for current user", authorizations = {@Authorization(value = "Bearer")})
    @GetMapping("/schedules")
    public ResponseEntity<?> getUserAlarmSchedules(HttpServletRequest request) {
        return success(alarmService.getAllSchedules(request));
    }

    @ApiOperation(value = "Create a new alarm schedule", authorizations = {@Authorization(value = "Bearer")})
    @PostMapping("/schedules")
    public ResponseEntity<?> createAlarmSchedule(@Valid @RequestBody AlarmScheduleReqDTO alarmScheduleReqDTO,
                                                 HttpServletRequest request) throws JsonProcessingException {
        DataResponseDTO<?> dataResponseDTO = alarmService.createAlarmSchedule(alarmScheduleReqDTO, request);
        return success(dataResponseDTO, HttpStatus.valueOf(dataResponseDTO.getStatus()));
    }

    @ApiOperation(value = "Update an existing alarm schedule", authorizations = {@Authorization(value = "Bearer")})
    @PutMapping("/schedules/{id}")
    public ResponseEntity<?> updateAlarmSchedule(@PathVariable Long id, @Valid @RequestBody AlarmScheduleReqDTO alarmScheduleReqDTO, HttpServletRequest request) throws JsonProcessingException {
        alarmScheduleReqDTO.setId(id); // Ensure ID is set correctly
        DataResponseDTO<?> dataResponseDTO = alarmService.updateAlarmSchedule(id, alarmScheduleReqDTO, request);
        return success(dataResponseDTO, HttpStatus.valueOf(dataResponseDTO.getStatus()));
    }

    @ApiOperation(value = "Delete an alarm schedule", authorizations = {@Authorization(value = "Bearer")})
    @DeleteMapping("/schedules")
    public ResponseEntity<?> deleteAlarmSchedule(@Valid @RequestBody AlarmScheduleDeleteReqDTO reqDTO, HttpServletRequest request) {
        return success(alarmService.deleteAlarmSchedule(reqDTO, request));
    }

    @ApiOperation(value = "Toggle alarm schedule active status", authorizations = {@Authorization(value = "Bearer")})
    @PutMapping("/schedules/{id}/toggle")
    public ResponseEntity<?> toggleAlarmScheduleStatus(@PathVariable Long id, HttpServletRequest request) {
        return success(alarmService.toggleAlarmScheduleStatus(id, request));
    }

    @ApiOperation(value = "Get all alarm activities", authorizations = {@Authorization(value = "Bearer")})
    @GetMapping("/activities")
    public ResponseEntity<?> getAllActivities(HttpServletRequest request) {
        return success(alarmService.getAllActivities(request));
    }
}
