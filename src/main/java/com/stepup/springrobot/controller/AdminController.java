package com.stepup.springrobot.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.admin.UpdateSDReqDTO;
import com.stepup.springrobot.service.AdminService;
import com.stepup.springrobot.service.LearnService;
import com.stepup.springrobot.service.SentryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.IOException;

@Controller
@CrossOrigin("*")
@RequestMapping("robot/api/v1/admin")
public class AdminController extends BaseController {
    @Autowired
    private LearnService learnService;

    @Autowired
    private AdminService adminService;

    @Autowired
    private SentryService sentryService;

    @PreAuthorize("hasAuthority('ADMIN')")
    @GetMapping("/lesson_data")
    public ResponseEntity<?> getLearnLessonData(@RequestParam(value = "lesson_id") Long lessonId) {
        return success(learnService.getLearnData(lessonId));
    }

    @GetMapping("/sd_path")
    public ResponseEntity<?> getAllUpdateSDPath(HttpServletRequest request) throws IOException {
        DataResponseDTO<?> dataResponseDTO = adminService.getAllUpdateSDPath(request);
        return success(dataResponseDTO);
    }

    @PostMapping("/upload")
    public ResponseEntity<?> sendUserAudio(HttpServletRequest request, @RequestParam("file") MultipartFile file,
                                           @RequestParam(value = "file_name", required = false) String fileName) throws IOException {
        DataResponseDTO<?> dataResponseDTO = adminService.uploadFile(request, file, fileName);
        return success(dataResponseDTO);
    }

    @PostMapping("/update_sd")
    public ResponseEntity<?> updateSD(HttpServletRequest request, @Valid @RequestBody UpdateSDReqDTO updateSDReqDTO)
            throws IOException {
        DataResponseDTO<?> dataResponseDTO = adminService.updateSD(request, updateSDReqDTO);
        return success(dataResponseDTO);
    }

    @PostMapping("/face_demo")
    public ResponseEntity<?> showFaceDemo(HttpServletRequest request, @RequestBody UpdateSDReqDTO updateSDReqDTO)
            throws IOException {
        DataResponseDTO<?> dataResponseDTO = adminService.showFaceDemo(request, updateSDReqDTO);
        return success(dataResponseDTO);
    }

    @PostMapping("/map_robot_to_phone")
    @ResponseBody
    public ResponseEntity<?> mapRobotToPhone(@RequestBody JsonNode data) {
        String robotId = data.get("robot_id").asText();
        String phoneNumber = data.get("value").asText();
        DataResponseDTO<?> dataResponseDTO = adminService.mapRobotToPhone(robotId, phoneNumber);
        return success(dataResponseDTO);
    }

    @GetMapping("/metrics")
    @ResponseBody
    public ResponseEntity<?> getMetrics(
            HttpServletRequest request,
            @RequestParam(value = "cursor", required = false) String cursor) throws IOException {
        DataResponseDTO<?> metrics = sentryService.getMetrics(request, cursor);
        return success(metrics);
    }

    @GetMapping("/robot_log")
    @ResponseBody
    public ResponseEntity<?> getConversationLogs(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        DataResponseDTO<?> response = adminService.getConversationLogs(page, size);
        return success(response);
    }

    @GetMapping("/conversations/{id}")
    public ResponseEntity<?> conversationDetail(@PathVariable Long id) {
        return success(adminService.getConversationChatHistory(id));
    }

    @GetMapping("/conversations")
    public ResponseEntity<?> listConversations(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(required = false) Long id,
            @RequestParam(required = false) String phone,
            @RequestParam(required = false) String userId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) Long botId) {
        return success(new DataResponseDTO<>(200, "Thành công", adminService
                .findAllWithFilters(id, phone, userId, startDate, endDate, botId, PageRequest.of(page, 10))));
    }

    @GetMapping("/profile-variables/{robotId}")
    public ResponseEntity<?> getRobotProfileVariables(@PathVariable String robotId) {
        return success(adminService.getRobotProfileVariables(robotId));
    }

    @PutMapping("/profile-variables/{robotId}")
    public ResponseEntity<?> createOrUpdateProfile(@PathVariable String robotId, @RequestBody JsonNode data) {
        return success(adminService.createOrUpdateProfile(robotId, data));
    }

    @GetMapping("/conversations/{conversation_id}/predict-language")
    public ResponseEntity<?> getConversationPredictLanguage(@PathVariable("conversation_id") String conversation_id) {
        return success(adminService.getConversationPredictLanguage(conversation_id));
    }

    @GetMapping("/conversations/summaries")
    public ResponseEntity<?> getConversationSummary(
            @RequestParam("user_id") String userId,
            @RequestParam("bot_id") Long botId) {
        return success(adminService.getConversationSummary(userId, botId));
    }
}
