package com.stepup.springrobot.controller;

import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.community.*;
import com.stepup.springrobot.service.CommunityService;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RestController
@CrossOrigin("*")
@RequestMapping("/robot/api/v1/community")
public class CommunityController extends BaseController {
    @Autowired
    private CommunityService communityService;

    @GetMapping("/features")
    @Operation(summary = "Get list of features")
    public ResponseEntity<?> getAllFeatures(
            @RequestParam(name = "user_id", required = false) String userId,
            @RequestParam(name = "page", defaultValue = "1") int page) {
        FeatureResDto features = communityService.getAllFeatures(userId, page);
        return success(new DataResponseDTO<>(200, "<PERSON><PERSON><PERSON> danh sách tính năng thành công", features));
    }

    @PostMapping("/features")
    @Operation(summary = "Add a new feature")
    public ResponseEntity<?> addFeature(
            @RequestBody FeatureReqDto featureDto,
            @RequestParam(name = "user_id") UUID userId) {
        FeatureDto savedFeature = communityService.addFeature(featureDto, userId.toString());
        return success(new DataResponseDTO<>(201, "Thêm tính năng mới thành công", savedFeature));
    }

    @PostMapping("/features/{featureId}/like")
    @Operation(summary = "Like a feature")
    public ResponseEntity<?> likeFeature(
            @PathVariable Long featureId,
            @RequestParam(name = "user_id") UUID userId) {
        communityService.likeFeature(featureId, userId);
        return success(new DataResponseDTO<>(200, "Đã cập nhật trạng thái thích tính năng"));
    }

    @PostMapping("/features/{featureId}/comments")
    @Operation(summary = "Comment on a feature")
    public ResponseEntity<?> addComment(
            @PathVariable Long featureId,
            @RequestBody CommentReqDto commentDto,
            @RequestParam(name = "user_id") UUID userId) {
        CommentDto savedComment = communityService.addComment(featureId, commentDto, userId);
        return success(new DataResponseDTO<>(201, "Thêm bình luận thành công", savedComment));
    }

    @PostMapping("/comments/{commentId}/like")
    @Operation(summary = "Like a comment")
    public ResponseEntity<?> likeComment(
            @PathVariable Long commentId,
            @RequestParam(name = "user_id") UUID userId) {
        communityService.likeComment(commentId, userId);
        return success(new DataResponseDTO<>(200, "Đã cập nhật trạng thái thích bình luận"));
    }

    @GetMapping("/features/{featureId}/comments")
    @Operation(summary = "Get all comments for a feature")
    public ResponseEntity<?> getCommentsByFeatureId(
            @PathVariable Long featureId,
            @RequestParam(name = "user_id", required = false) UUID userId,
            @RequestParam(name = "page", defaultValue = "1") int page) {
        CommentResDto comments = communityService.getCommentsByFeatureId(featureId, userId, page);
        return success(new DataResponseDTO<>(200, "Lấy danh sách bình luận thành công", comments));
    }
}
