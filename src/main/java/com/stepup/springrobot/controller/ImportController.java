package com.stepup.springrobot.controller;

import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.service.data.ImportAddonService;
import com.stepup.springrobot.service.data.ImportGameService;
import com.stepup.springrobot.service.data.ImportService;
import com.stepup.springrobot.service.data.ImportStudyDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin("*")
@RequestMapping("robot/api/v1/import")
public class ImportController extends BaseController {
    @Autowired
    private ImportService importService;

    @Autowired
    private ImportGameService importGameService;

    @Autowired
    private ImportStudyDataService importStudyDataService;

    @Autowired
    private ImportAddonService importAddonService;

    @GetMapping("/lesson")
    public ResponseEntity<?> importLessonData() throws Exception {
        importService.initCommunicateLessonData();
        importService.initCommunicateLessonDetailData();
        return success();
    }
    @GetMapping("/onboarding")
    public ResponseEntity<?> importOnboardingSheet() throws Exception {
        importService.importOnboardingData();
        return success(new DataResponseDTO<>(CodeDefine.OK, "Import dữ liệu sheet onboarding đang được xử lý!"));
    }

    @GetMapping("/entrance_test")
    public ResponseEntity<?> importEntranceTestSheet() throws Exception {
        importService.importEntranceTestData();
        return success(new DataResponseDTO<>(CodeDefine.OK, "Import dữ liệu sheet entrance test đang được xử lý!"));
    }

    @GetMapping("/echo_tower")
    public ResponseEntity<?> importEchoTowerSheet() throws Exception {
        importGameService.initEchoTowerData();
        return success(new DataResponseDTO<>(CodeDefine.OK, "Import dữ liệu sheet echo_tower đang được xử lý!"));
    }

    @GetMapping("/word_race")
    public ResponseEntity<?> importWordRaceSheet() throws Exception {
        importGameService.initGameWordRaceData();
        return success(new DataResponseDTO<>(CodeDefine.OK, "Import dữ liệu sheet word_race đang được xử lý!"));
    }

    @GetMapping("/alpha_blast")
    public ResponseEntity<?> importAlphaBlastSheet() throws Exception {
        importGameService.initGameAlphaBlastData();
        return success(new DataResponseDTO<>(CodeDefine.OK, "Import dữ liệu sheet alpha_blast đang được xử lý!"));
    }

    @GetMapping("/firmware_version")
    public ResponseEntity<?> importFirmwareVersionSheet() throws Exception {
        importService.importRobotFirmwareData();
        return success(new DataResponseDTO<>(CodeDefine.OK, "Import dữ liệu sheet firmware_version đang được xử lý!"));
    }

    @GetMapping("/study_unit")
    public ResponseEntity<?> importStudyUnitSheet() throws Exception {
        importStudyDataService.initStudyUnitData();
        return success(new DataResponseDTO<>(CodeDefine.OK, "Import dữ liệu sheet study_unit đang được xử lý!"));
    }

    @GetMapping("/song")
    public ResponseEntity<?> importMusicSheet() throws Exception {
        importAddonService.initMusicData();
        return success(new DataResponseDTO<>(CodeDefine.OK, "Import dữ liệu sheet music đang được xử lý!"));
    }

    @GetMapping("/story")
    public ResponseEntity<?> importStorySheet() throws Exception {
        importAddonService.initStoryData();
        return success(new DataResponseDTO<>(CodeDefine.OK, "Import dữ liệu sheet story đang được xử lý!"));
    }

    @GetMapping("/servo_action")
    public ResponseEntity<?> importServoActionSheet() throws Exception {
        importAddonService.initServoData();
        return success(new DataResponseDTO<>(CodeDefine.OK, "Import dữ liệu sheet Servo đang được xử lý!"));
    }

    @GetMapping("/listening_emotion")
    public ResponseEntity<?> importListeningEmotionSheet() throws Exception {
        importAddonService.initListeningEmotionData();
        return success(new DataResponseDTO<>(CodeDefine.OK, "Import dữ liệu sheet listening_emotion đang được xử lý!"));
    }

    @GetMapping("/lesson_mvp")
    public ResponseEntity<?> importLessonMvpSheet() throws Exception {
        importService.initMvpLessonData();
        return success(new DataResponseDTO<>(CodeDefine.OK, "Import dữ liệu sheet lesson_mvp đang được xử lý!"));
    }
}
