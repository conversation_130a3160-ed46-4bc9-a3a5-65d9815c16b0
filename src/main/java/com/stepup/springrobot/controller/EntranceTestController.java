package com.stepup.springrobot.controller;

import com.stepup.springrobot.dto.entrance_test.SaveEntranceDetailReqDTO;
import com.stepup.springrobot.service.EntranceTestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.IOException;

@RestController
@CrossOrigin("*")
@RequestMapping("/robot/api/v1/entrance_test")
public class EntranceTestController extends BaseController {
    @Autowired
    private EntranceTestService entranceTestService;

    @GetMapping()
    public ResponseEntity<?> getTestData(HttpServletRequest request, @RequestParam("device_id") String deviceId) throws IOException {
        return success(entranceTestService.getEntranceTestData(request, deviceId, false));
    }

    @PostMapping()
    public ResponseEntity<?> saveTestData(HttpServletRequest request, @Valid @RequestBody SaveEntranceDetailReqDTO reqDTO) throws IOException {
        return success(entranceTestService.saveEntranceTestDetailResult(request, reqDTO, false));
    }
}
