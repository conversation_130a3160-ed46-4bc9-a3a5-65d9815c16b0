package com.stepup.springrobot.controller;

import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.dto.DataResponseDTO;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.HashMap;
import java.util.Map;

public class BaseController {
    protected BaseController() {
    }

    protected ResponseEntity<?> success() {
        return new ResponseEntity<>(new DataResponseDTO<>(CodeDefine.OK, "Thành công!"), HttpStatus.OK);
    }

    protected <T> ResponseEntity<DataResponseDTO<T>> success(DataResponseDTO<T> dataResponseDTO) {
        return new ResponseEntity<>(dataResponseDTO, HttpStatus.OK);
    }

    protected <T> ResponseEntity<DataResponseDTO<T>> success(DataResponseDTO<T> dataResponseDTO, HttpStatus httpStatus) {
        return new ResponseEntity<>(dataResponseDTO, httpStatus);
    }

    protected ResponseEntity<Map<String, String>> success(String strBase64) {
        Map<String, String> map = new HashMap<>();
        map.put("data", strBase64);
        return new ResponseEntity<>(map, HttpStatus.OK);
    }

    protected <T> ResponseEntity<DataResponseDTO<T>> serverError(DataResponseDTO<T> dataResponseDTO) {
        return new ResponseEntity<>(dataResponseDTO, HttpStatus.INTERNAL_SERVER_ERROR);
    }
}
