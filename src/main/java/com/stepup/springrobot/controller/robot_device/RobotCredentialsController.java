package com.stepup.springrobot.controller.robot_device;

import com.stepup.springrobot.controller.BaseController;
import com.stepup.springrobot.dto.robot.RobotCredentialsRequestDTO;
import com.stepup.springrobot.service.robot.RobotCredentialsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping("robot/api/v1/device/credentials")
public class RobotCredentialsController extends BaseController {

    private final RobotCredentialsService robotCredentialsService;


    @Autowired
    public RobotCredentialsController(RobotCredentialsService robotCredentialsService) {
        this.robotCredentialsService = robotCredentialsService;
    }

    /**
     * Endpoint to provide credentials for a robot by serial number,
     * but only after verifying the challenge response
     * 
     * @param request The credentials request containing serial number, secret key, and challenge response
     * @return The robot credentials response
     */
    @PostMapping
    public ResponseEntity<?> getCredentials(
            @Valid @RequestBody RobotCredentialsRequestDTO request) {
        // If the challenge is verified, process the credentials request
      return success(robotCredentialsService.processCredentialsRequest(request));
    }
} 