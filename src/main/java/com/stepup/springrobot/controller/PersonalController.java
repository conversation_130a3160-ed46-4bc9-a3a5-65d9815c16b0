package com.stepup.springrobot.controller;

import com.stepup.springrobot.dto.personal.SwitchProfileReqDTO;
import com.stepup.springrobot.service.PersonalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.IOException;

@RestController
@CrossOrigin("*")
@RequestMapping("robot/api/v1/personal")
public class PersonalController extends BaseController {
    @Autowired
    private PersonalService personalService;

    @GetMapping("/info")
    public ResponseEntity<?> getLearnLessonData(HttpServletRequest request,
                                                @RequestParam(value = "device_id") String deviceId,
                                                @RequestParam(value = "app_v") String appV ,
                                                @RequestParam(value = "platform") String platform) throws IOException {
        return success(personalService.getPersonalInfo(request, deviceId, appV, platform, false));
    }

    @GetMapping("/profiles")
    public ResponseEntity<?> getUserProfiles(HttpServletRequest request) throws IOException {
        return success(personalService.getUserProfiles(request));
    }

    @PutMapping("/profile")
    public ResponseEntity<?> getUserProfiles(HttpServletRequest request, @Valid @RequestBody SwitchProfileReqDTO reqDTO) throws IOException {
        return success(personalService.updateCurrentProfile(request, reqDTO));
    }


    @GetMapping("/statistics")
    public ResponseEntity<?> getUserStatistics(HttpServletRequest request) throws IOException {
        return success(personalService.getStatisticData(request));
    }

    @GetMapping("/robots")
    public ResponseEntity<?> getListRobot(HttpServletRequest request) throws IOException {
        return success(personalService.getListRobot(request));
    }

    @GetMapping("/settings")
    public ResponseEntity<?> getPersonalSettingConfig(HttpServletRequest request) throws IOException {
        return success(personalService.getPersonalSettingConfig(request));
    }

    /**
     * Xóa tài khoản (Chỉ áp dụng tài khoản test)
     */
    @DeleteMapping("/account")
    public ResponseEntity<?> deleteAccount(HttpServletRequest request) throws IOException {
        return success(personalService.deleteTestAccount(request, null, false));
    }
}
