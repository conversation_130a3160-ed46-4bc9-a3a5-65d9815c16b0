package com.stepup.springrobot.controller;

import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.auth.RefreshTokenReqDTO;
import com.stepup.springrobot.security.RefreshTokenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Slf4j
@RestController
@CrossOrigin("*")
@RequestMapping("robot-user/api/v1/auth")
public class AuthController extends BaseController {

    @Autowired
    private RefreshTokenService refreshTokenService;

    @PostMapping("/refresh-token")
    public ResponseEntity<?> refreshToken(@Valid @RequestBody RefreshTokenReqDTO refreshToken) {
        return success(new DataResponseDTO<>(CodeDefine.OK, "Thành công", refreshTokenService.handleRefreshToken(refreshToken.getToken(), refreshToken.getDeviceId())));
    }
}
