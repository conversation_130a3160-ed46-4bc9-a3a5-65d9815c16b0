package com.stepup.springrobot.controller;

import com.stepup.springrobot.dto.chat.AIConversationDetailMessageResDTO;
import com.stepup.springrobot.service.AIRobotConversationService;
import com.stepup.springrobot.service.UtilsService;
import com.stepup.springrobot.websocket.service.LocalFileStreamingEmulationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;

@RestController
@CrossOrigin("*")
@RequestMapping("robot/api/v1/utils")
public class UtilsController extends BaseController {
    @Autowired
    private LocalFileStreamingEmulationService localFileStreamingEmulationService;

    @Autowired
    private AIRobotConversationService aiRobotConversationService;

    @PostMapping("/stream_file")
    public ResponseEntity<?> streamFile(@RequestParam(value = "audio_file") MultipartFile file) {
        localFileStreamingEmulationService.streamFile(file);
        return success();
    }

    @PostMapping("/sentence_animation")
    public ResponseEntity<?> getAnimationForSentence(@RequestBody AIConversationDetailMessageResDTO message, @RequestParam(value = "is_convert_wav_file", defaultValue = "false") Boolean isConvertWavFile) {
        return success(aiRobotConversationService.getAnimationsData(message, isConvertWavFile));
    }

    @PostMapping("/convert_wav")
    public ResponseEntity<?> convertToWaw(@RequestParam(value = "audio_file") MultipartFile multipartFile) {
        File file = UtilsService.convertMultiPartFileToFile(multipartFile, 1L);
        String wavFilePath = UtilsService.getWavFilePath(file.getAbsolutePath());
        return success();
    }
}
