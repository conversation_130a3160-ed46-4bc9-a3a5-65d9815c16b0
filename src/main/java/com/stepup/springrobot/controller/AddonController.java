package com.stepup.springrobot.controller;

import com.stepup.springrobot.dto.addon.SongPlayReqDTO;
import com.stepup.springrobot.dto.addon.StoryPlayReqDTO;
import com.stepup.springrobot.service.AddonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.IOException;

@RestController
@CrossOrigin("*")
@RequestMapping("/robot/api/v1/addon")
public class AddonController extends BaseController {

    @Autowired
    private AddonService addonService;


    @GetMapping("/song")
    public ResponseEntity<?> getSongs(HttpServletRequest request, @RequestParam(value = "page", defaultValue = "1") Integer page) throws IOException {
        return success(addonService.getSongs(request, page));
    }

    @PutMapping("/song/play")
    public ResponseEntity<?> playSong(HttpServletRequest request, @Valid @RequestBody SongPlayReqDTO reqDTO) throws IOException {
        return success(addonService.playMusic(request, reqDTO));
    }

    @GetMapping("/story")
    public ResponseEntity<?> getStories(HttpServletRequest request, @RequestParam(value = "page", defaultValue = "1") Integer page) throws IOException {
        return success(addonService.getStories(request, page));
    }

    @PutMapping("/story/play")
    public ResponseEntity<?> playStory(HttpServletRequest request, @Valid @RequestBody StoryPlayReqDTO reqDTO) throws IOException {
        return success(addonService.playStory(request, reqDTO));
    }
}
