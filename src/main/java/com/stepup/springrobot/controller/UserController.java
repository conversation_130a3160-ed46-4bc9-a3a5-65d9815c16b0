package com.stepup.springrobot.controller;

import com.stepup.springrobot.dto.auth.*;
import com.stepup.springrobot.exception.business.user.InvalidPhoneNumberException;
import com.stepup.springrobot.service.auth.OTPService;
import com.stepup.springrobot.service.auth.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.IOException;

@RestController
@CrossOrigin("*")
@RequestMapping("robot-user/api/v1/user")
public class UserController extends BaseController {
    @Autowired
    private OTPService otpService;

    @Autowired
    private UserService userService;

    /**
     * Tạo mới user
     */
    @PostMapping()
    public ResponseEntity<?> createUser(@Valid @RequestBody UserCreateReqDTO userCreateRequestDTO, HttpServletRequest request) {
        return success(userService.createUserFromApp(userCreateRequestDTO, request));
    }


    /**
     * Đăng xuất
     */
    @PostMapping("/logout")
    public ResponseEntity<?> logout(@Valid @RequestBody LogoutReqDTO requestDTO, HttpServletRequest request) throws IOException {
        return success(userService.logOut(request, requestDTO));
    }


    /**
     * Trước khi đăng ký theo dạng gửi OTP firebase hoặc OTP nhà mạng
     * * thì cần kiểm tra xem user đã tồn tại chưa
     */
    @GetMapping("/pre-sign-up")
    public ResponseEntity<?> getUserForPreSignUp(HttpServletRequest request, @RequestParam("phone") String phone) throws InvalidPhoneNumberException {
        return success(userService.getUserForPreSignUp(request, phone));
    }

    /**
     * Gửi SMS theo hệ thống tổng đài hoặc sms brandname đến user
     */
    @PutMapping("/send-otp-sms")
    public ResponseEntity<?> sendOtpSMSToUser(HttpServletRequest request, @Valid @RequestBody UserSendSMSReqDTO requestDTO) {
        return success(userService.sendOtpSMSToUser(request, requestDTO));
    }

    /**
     * Xác minh OTP theo hệ thống tổng đài
     */
    @PutMapping("/verify-otp-sms")
    public ResponseEntity<?> verifyOTPSMS(HttpServletRequest request, @Valid @RequestBody UserVerifySMSReqDTO requestDTO) throws InvalidPhoneNumberException {
        return success(userService.verifyOTPSMS(request, requestDTO));
    }

    /**
     * Tìm kiếm user trước khi thực hiện quên mật khẩu
     */
    @GetMapping("/search")
    public ResponseEntity<?> getUserForgotPwdInfo(HttpServletRequest request, @RequestParam("phone") String phone) throws InvalidPhoneNumberException {
        return success(userService.getUserForgotPwdInfo(request, phone));
    }

    /**
     * Update password qua cả otp firebase và sms
     */
    @PutMapping("/update-password-with-otp")
    public ResponseEntity<?> updatePasswordWithOtp(HttpServletRequest request, @Valid @RequestBody UserRecoveryPWByOTPReqDTO requestDTO) {
        return success(userService.updateUserPasswordByOTP(request, requestDTO));
    }
}
