package com.stepup.springrobot.controller.web;

import com.stepup.springrobot.controller.BaseController;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

@Controller
@CrossOrigin("*")
@RequestMapping("/api/v1/robot_conversation")
public class AIRobotConversationController extends BaseController {
    @GetMapping("/stream")
    private ModelAndView streamAudioBinary()  {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("stream_audio");
        return modelAndView;
    }

    @GetMapping("/v2/stream")
    private ModelAndView streamAudioV2Binary()  {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("stream_audio_v2");
        return modelAndView;
    }

    @GetMapping("/v4/stream")
    private ModelAndView streamAudioV2SecureBinary()  {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("stream_audio_v2_security");
        return modelAndView;
    }

    @GetMapping("/v3/stream")
    private ModelAndView streamAudioV3Binary()  {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("stream_audio_v3");
        return modelAndView;
    }

    @GetMapping("/stream_google")
    private ModelAndView streamAudioBinaryGoogle()  {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("stream_audio_google");
        return modelAndView;
    }

    @GetMapping("/stream_web")
    private ModelAndView streamAudioBinaryWeb()  {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("stream_audio_web");
        return modelAndView;
    }
}
