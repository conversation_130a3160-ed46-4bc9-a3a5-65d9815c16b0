package com.stepup.springrobot.controller.web;

import com.stepup.springrobot.controller.BaseController;
import com.stepup.springrobot.model.chat.RobotUserConversation;
import com.stepup.springrobot.service.AdminService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

@Controller
@CrossOrigin("*")
@RequestMapping("web/api/v1")
public class WebController extends BaseController {
    @Autowired
    private AdminService adminService;

    @GetMapping("/conversations")
    public ModelAndView listConversations(@RequestParam(defaultValue = "0") int page) {
        Page<RobotUserConversation> conversations = adminService
            .findAll(PageRequest.of(page, 6));

        ModelAndView mav = new ModelAndView("conversations/list");
        mav.addObject("conversations", conversations);
        return mav;
    }

    @GetMapping("/account/delete")
    private ModelAndView deleteAccount() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("delete_account");
        return modelAndView;
    }
}