package com.stepup.springrobot.controller;

import com.stepup.springrobot.dto.setting.ChangeSettingReqDTO;
import com.stepup.springrobot.model.mqtt.MqttMessageType;
import com.stepup.springrobot.service.communication.SettingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.IOException;

@RestController
@CrossOrigin("*")
@RequestMapping("robot/api/v1/setting")
public class SettingController extends BaseController {
    @Autowired
    private SettingService settingService;

    @GetMapping()
    public ResponseEntity<?> getSettingData(HttpServletRequest request, @RequestParam("type") MqttMessageType type) throws IOException {
        return success(settingService.getSettingData(request, type));
    }

    @PutMapping()
    public ResponseEntity<?> updateSettingData(HttpServletRequest request, @Valid @RequestBody ChangeSettingReqDTO reqDTO) throws IOException {
        return success(settingService.updateSettingData(request, reqDTO));
    }
}
