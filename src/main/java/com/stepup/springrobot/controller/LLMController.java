package com.stepup.springrobot.controller;

import com.stepup.springrobot.dto.llm.LlmImageReqDTO;
import com.stepup.springrobot.dto.llm.LlmMoodReqDTO;
import com.stepup.springrobot.dto.llm_conversation.UpdateUserProfileReqDTO;
import com.stepup.springrobot.service.LLMService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@RestController
@CrossOrigin("*")
@RequestMapping("robot/api/v1/llm")
public class LLMController extends BaseController {
    @Autowired
    private LLMService llmService;

    @GetMapping("/user_profile")
    public ResponseEntity<?> getUserProfile(HttpServletRequest request, @RequestParam(value = "conversation_id") String conversationId,
                                            @RequestParam(value = "token") String token) {
        return success(llmService.getUserProfile(request, conversationId, token));
    }

    @GetMapping("/user_profile_description")
    public ResponseEntity<?> getUserProfileDescription(HttpServletRequest request, @RequestParam(value = "token") String token) {
        return success(llmService.getUserProfileDescription(request, token));
    }

    @PutMapping("/user_profile")
    public ResponseEntity<?> getUserProfile(HttpServletRequest request, @Valid @RequestBody UpdateUserProfileReqDTO reqDTO) {
        return success(llmService.updateUserProfile(request, reqDTO));
    }

    @GetMapping("/images")
    public ResponseEntity<?> getLLMImageByBotId(HttpServletRequest request, @RequestParam(value = "token") String token, @RequestParam(value = "bot_id") Long botId) {
        return success(llmService.getLLMImageByBotId(request, token, botId));
    }

    @PutMapping("/images")
    public ResponseEntity<?> saveLLMImagesByBotId(HttpServletRequest request, @Valid @RequestBody LlmImageReqDTO reqDTO) {
        return success(llmService.saveLLMImagesByBotId(request, reqDTO));
    }

    @GetMapping("/moods")
    public ResponseEntity<?> getLLMMood(HttpServletRequest request, @RequestParam(value = "token") String token) {
        return success(llmService.getLLMMood(request, token));
    }

    @PutMapping("/moods")
    public ResponseEntity<?> saveLLMMood(HttpServletRequest request,  @Valid @RequestBody LlmMoodReqDTO reqDTO) {
        return success(llmService.saveLLMMood(request, reqDTO));
    }
}
