package com.stepup.springrobot.controller;

import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.ladipage.LadipageWebhookRequest;
import com.stepup.springrobot.service.ladipage.LadipageWebhookService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Slf4j
@RestController
@RequestMapping("/api/v1/webhook/ladipage")
@Tag(name = "Ladipage Webhook API", description = "APIs for handling Ladipage webhooks")
@RequiredArgsConstructor
public class LadipageWebhookController {

    private final LadipageWebhookService ladipageWebhookService;

    @PostMapping("/orders")
    @Operation(summary = "Handle order creation webhook from Ladipage")
    public ResponseEntity<DataResponseDTO<Void>> handleOrderWebhook(@Valid @RequestBody LadipageWebhookRequest request) {
        return ResponseEntity.ok(ladipageWebhookService.handleOrderWebhook(request));
    }
} 