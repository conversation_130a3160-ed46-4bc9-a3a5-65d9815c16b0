package com.stepup.springrobot.controller;

import com.stepup.springrobot.dto.chat.ReportDataConversationDTO;
import com.stepup.springrobot.model.chat.RobotUserConversationRecordHistory;
import com.stepup.springrobot.service.AIRobotConversationService;
import com.stepup.springrobot.service.PersonalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

@RestController
@CrossOrigin("*")
@RequestMapping("robot/api/v1/robot-open/personal")
public class PersonalOpenController extends BaseController {
    @Autowired
    private PersonalService personalService;

    @Autowired
    private AIRobotConversationService aiRobotConversationService;

    @GetMapping("/info")
    public ResponseEntity<?> getLearnLessonData(HttpServletRequest request, @RequestParam(value = "device_id") String deviceId,
                                                @RequestParam(value = "app_v") String appV,
                                                @RequestParam(value = "platform") String platform) throws IOException {
        return success(personalService.getPersonalInfo(request, deviceId, appV, platform, true));
    }

    @PostMapping("/report")
    public ResponseEntity<?> reportData(@RequestBody ReportDataConversationDTO dataReport) {
        return success(aiRobotConversationService.saveDataReportConversation(dataReport));
    }

    @GetMapping("/report")
    public Page<RobotUserConversationRecordHistory> getReportData(@RequestParam(defaultValue = "0") int page) {
        return aiRobotConversationService.findAllByDataReportNotNull(PageRequest.of(page, 5));
    }

    /**
     * Xóa tài khoản (Chỉ áp dụng tài khoản test)
     */
    @DeleteMapping("/account")
    public ResponseEntity<?> deleteAccount(HttpServletRequest request, @RequestParam(value = "phone") String phone) throws IOException {
        return success(personalService.deleteTestAccount(request, phone, true));
    }
}
