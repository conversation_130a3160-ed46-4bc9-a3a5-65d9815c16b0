/* Robot Settings Styles */
.settings-card {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  margin-bottom: 20px;
}

.settings-card .card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 15px 20px;
}

.settings-card .card-title {
  margin-bottom: 0;
  font-weight: 600;
  color: #495057;
}

.settings-card .card-body {
  padding: 20px;
}

.form-label {
  font-weight: 500;
  color: #495057;
}

.btn-primary {
  background-color: #0d6efd;
  border-color: #0d6efd;
  padding: 10px 20px;
  font-weight: 500;
}

.btn-primary:hover {
  background-color: #0b5ed7;
  border-color: #0a58ca;
}

#settingResult {
  padding: 15px;
  border-radius: 5px;
}

.alert-success {
  background-color: #d1e7dd;
  border-color: #badbcc;
  color: #0f5132;
}

.alert-danger {
  background-color: #f8d7da;
  border-color: #f5c2c7;
  color: #842029;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .settings-card {
    margin-bottom: 15px;
  }

  .settings-card .card-header {
    padding: 12px 15px;
  }

  .settings-card .card-body {
    padding: 15px;
  }
}
