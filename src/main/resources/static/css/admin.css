/* Admin Layout */
.admin-wrapper {
  display: flex;
  min-height: 100vh;
}

/* Sidebar Styles */
.sidebar {
  width: 250px;
  background: #2c3e50;
  color: white;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header h4 {
  margin: 0;
  font-size: 1.2rem;
}

.sidebar-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.menu-item {
  color: #ecf0f1;
  text-decoration: none;
  padding: 15px 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s;
}

.menu-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.menu-item.active {
  background: #3498db;
  color: white;
}

/* Main Content */
.main-content {
  flex-grow: 1;
  background: #f8f9fa;
}

/* Top Navbar */
.top-navbar {
  background: white;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.admin-profile .dropdown-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Stats Cards */
.stats-card {
  background: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  gap: 15px;
}

.stats-icon {
  width: 50px;
  height: 50px;
  background: #e3f2fd;
  color: #0d6efd;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.stats-info h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.stats-info p {
  margin: 0;
  color: #6c757d;
}

.content-wrapper {
  padding: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: -250px;
    height: 100%;
    z-index: 1000;
  }

  .sidebar.active {
    left: 0;
  }
}

.metric-card {
  transition: all 0.3s ease;
  border: 1px solid #dee2e6;
  height: 100%;
}

.metric-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.metric-card .card-title {
  color: #333;
  margin-bottom: 1rem;
}

.metric-card .card-title i {
  margin-right: 0.5rem;
  font-size: 1.2rem;
}

.metric-card .card-text {
  color: #6c757d;
  flex-grow: 1;
}

.metric-card .btn {
  width: 100%;
}
