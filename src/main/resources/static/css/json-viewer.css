.json-viewer {
  font-family: monospace;
  font-size: 13px;
  line-height: 1.5;
  padding: 15px;
  background-color: #ffffff;
  border-radius: 4px;
  height: calc(100vh - 60px); /* Subtract header height */
  overflow-y: auto;
  color: #333;
}

/* Tree view styling */
.json-viewer ul {
  list-style-type: none;
  padding-left: 16px;
  margin: 0;
  position: relative;
}

.json-viewer li {
  position: relative;
  padding-left: 1em;
  line-height: 18px;
}

/* Vertical lines */
.json-viewer li::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 1px;
  background-color: #ccc;
  height: 100%;
}

/* Horizontal lines */
.json-viewer li::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0.7em;
  width: 0.7em;
  height: 1px;
  background-color: #ccc;
}

/* Remove vertical line from last child */
.json-viewer li:last-child::before {
  height: 0.7em;
}

/* Collapsed/expanded icons */
.json-viewer .collapsed::before {
  content: "+";
  display: inline-block;
  width: 10px;
  height: 10px;
  line-height: 10px;
  text-align: center;
  border: 1px solid #999;
  margin-right: 5px;
  font-size: 10px;
  color: #333;
  background-color: #f5f5f5;
}

.json-viewer .expanded::before {
  content: "-";
  display: inline-block;
  width: 10px;
  height: 10px;
  line-height: 10px;
  text-align: center;
  border: 1px solid #999;
  margin-right: 5px;
  font-size: 10px;
  color: #333;
  background-color: #f5f5f5;
}

/* Hide children of collapsed nodes */
.json-viewer .collapsed + ul {
  display: none;
}

/* Object and array brackets */
.json-viewer .object-bracket,
.json-viewer .array-bracket {
  color: #000;
  font-weight: normal;
}

/* Property names */
.json-viewer .property {
  color: #000;
  font-weight: normal;
}

/* Values */
.json-viewer .string {
  color: #006400;
}

.json-viewer .number {
  color: #0000ff;
}

.json-viewer .boolean {
  color: #b22222;
}

.json-viewer .null {
  color: #808080;
}

/* Row highlighting */
.json-viewer li:nth-child(even) > ul {
  background-color: #f8f8ff;
}

/* Blue highlight for selected rows */
.json-viewer .highlight-row {
  background-color: #e6f3ff;
}

/* Square brackets for arrays, curly for objects */
.json-viewer .array-icon::before {
  content: "[]";
  color: #000;
  margin-right: 5px;
}

.json-viewer .object-icon::before {
  content: "{}";
  color: #000;
  margin-right: 5px;
}

/* Controls */
.json-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1;
}

.json-control-button {
  background: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 3px;
  padding: 2px 8px;
  font-size: 12px;
  cursor: pointer;
  margin-left: 5px;
}

.json-control-button:hover {
  background: #e0e0e0;
}

/* Custom scrollbar */
.json-viewer::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.json-viewer::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.json-viewer::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 4px;
}

.json-viewer::-webkit-scrollbar-thumb:hover {
  background: #999;
}

/* Alternating row background */
.json-viewer li:nth-child(odd) {
  background-color: #ffffff;
}

.json-viewer li:nth-child(even) {
  background-color: #f8f8ff;
}

/* Blue highlight for rows */
.json-viewer li.highlight-row {
  background-color: #e6f3ff;
}

/* Modal specific styles */
.modal-fullscreen .modal-body {
  height: calc(100vh - 56px); /* Subtract header height */
  overflow: hidden;
}

.modal-fullscreen .json-viewer {
  height: 100%;
  border-radius: 0;
}

.json-node {
  margin-left: 20px;
  position: relative;
}

.json-preview {
  color: #666;
  font-style: italic;
  margin-left: 5px;
  font-size: 12px;
}

.json-key {
  color: #881391;
  font-weight: normal;
}

.json-string {
  color: #1a1aa6;
}

.json-number {
  color: #1c00cf;
}

.json-boolean {
  color: #0000ff;
}

.json-null {
  color: #808080;
}

.json-content {
  margin-left: 20px;
  border-left: 1px solid #e0e0e0;
  padding-left: 10px;
}

.json-item {
  margin: 2px 0;
  position: relative;
}

.json-item::before {
  content: "";
  position: absolute;
  left: -10px;
  top: 10px;
  width: 10px;
  height: 1px;
  background-color: #e0e0e0;
}

/* Additional styles for better visual hierarchy */
.json-object {
  position: relative;
}

.json-array {
  position: relative;
}

.json-item:last-child::before {
  border-bottom-left-radius: 4px;
}

/* Style for the root object */
.json-viewer > .json-node {
  margin-left: 0;
}

/* Style for empty objects and arrays */
.json-empty {
  color: #999;
  font-style: italic;
}
