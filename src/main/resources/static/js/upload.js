// Load SD paths when page loads
document.addEventListener("DOMContentLoaded", function () {
  loadSDPaths();
  updateFileInputState();
});

// Function to update file input state based on path selection
function updateFileInputState() {
  const pathSelect = document.getElementById("sdPath");
  const fileInput = document.getElementById("file");
  const uploadButton = document.getElementById("uploadButton");

  if (!pathSelect.value) {
    fileInput.disabled = true;
    uploadButton.disabled = true;
    fileInput.title = "Please select a path first";
    uploadButton.title = "Please select a path first";
  } else {
    fileInput.disabled = false;
    uploadButton.disabled = false;
    fileInput.title = "";
    uploadButton.title = "";
  }
}

function loadSDPaths() {
  fetch("/robot/api/v1/admin/sd_path")
    .then((response) => response.json())
    .then((data) => {
      const pathSelect = document.getElementById("sdPath");
      pathSelect.innerHTML = ""; // Clear existing options

      // Add default option
      const defaultOption = document.createElement("option");
      defaultOption.value = "";
      defaultOption.textContent = "Select a path...";
      pathSelect.appendChild(defaultOption);

      // Add paths from API
      data.data.forEach((pathData) => {
        const option = document.createElement("option");
        option.value = pathData.file_name;
        option.textContent = pathData.description;
        option.dataset.path = pathData.path;
        pathSelect.appendChild(option);
      });

      // Add change event listener to path select
      pathSelect.addEventListener("change", updateFileInputState);
    })
    .catch((error) => {
      console.error("Error loading paths:", error);
      alert("Failed to load paths. Please refresh the page.");
    });
}

// Add variable to store the last uploaded URL
let lastUploadedUrl = "";

document.getElementById("uploadForm").addEventListener("submit", function (e) {
  e.preventDefault();

  const selectedPath = document.getElementById("sdPath").value;
  if (!selectedPath) {
    alert("Please select a path first before uploading a file.");
    return;
  }

  const fileInput = document.getElementById("file");
  if (!fileInput.files.length) {
    alert("Please select a file to upload.");
    return;
  }

  const formData = new FormData(this);
  const file = fileInput.files[0];
  const fileType = file.type;
  const fileName = file.name.toLowerCase();

  formData.append("file_name", selectedPath);

  fetch("/robot/api/v1/admin/upload", {
    method: "POST",
    body: formData,
  })
    .then((response) => response.json())
    .then((data) => {
      const uploadResult = document.getElementById("uploadResult");
      const fileUrl = document.getElementById("fileUrl");
      const imagePreview = document.getElementById("imagePreview");
      const audioPreviewContainer = document.getElementById(
        "audioPreviewContainer"
      );
      const audioPreview = document.getElementById("audioPreview");

      // Store the uploaded URL
      lastUploadedUrl = data.data;

      fileUrl.href = lastUploadedUrl;
      fileUrl.textContent = lastUploadedUrl;
      uploadResult.style.display = "block";

      // Reset previews
      imagePreview.style.display = "none";
      audioPreviewContainer.style.display = "none";

      // Show preview based on file type
      if (fileType.startsWith("image/") || fileName.endsWith(".gif")) {
        imagePreview.src = lastUploadedUrl;
        imagePreview.style.display = "block";
      } else if (fileType.startsWith("audio/")) {
        audioPreview.src = lastUploadedUrl;
        audioPreviewContainer.style.display = "block";
      }
    })
    .catch((error) => {
      console.error("Error:", error);
      alert("Upload failed. Please try again.");
    });
});

// Add event listener for Update SD button
document
  .getElementById("updateSdButton")
  .addEventListener("click", function () {
    const robotId = document.getElementById("robotId").value.trim();
    const selectedPath = document.getElementById("sdPath").value;

    if (!robotId) {
      alert("Please enter a Robot ID");
      return;
    }

    if (!selectedPath) {
      alert("Please select a path first");
      return;
    }

    if (!lastUploadedUrl) {
      alert("Please upload a file first");
      return;
    }

    // Get the selected option to access path data
    const selectedOption = document.getElementById("sdPath").selectedOptions[0];

    const updateData = {
      robot_id: robotId,
      file_name: selectedPath,
      path: selectedOption.dataset.path,
      url: lastUploadedUrl, // Include the uploaded URL
    };

    fetch("/robot/api/v1/admin/update_sd", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(updateData),
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          alert("SD update successful!");
        } else {
          alert("SD update failed: " + (data.message || "Unknown error"));
        }
      })
      .catch((error) => {
        console.error("Error updating SD:", error);
        alert("Failed to update SD. Please try again.");
      });
  });
