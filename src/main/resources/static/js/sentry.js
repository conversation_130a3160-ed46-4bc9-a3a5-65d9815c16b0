let nextCursor = null;
let isLoading = false;

document.addEventListener("DOMContentLoaded", function () {
  loadMetrics();

  // Add scroll event listener
  window.addEventListener("scroll", function () {
    if (
      window.innerHeight + window.scrollY >=
      document.body.offsetHeight - 100
    ) {
      // User is near bottom of page
      if (nextCursor && !isLoading) {
        loadMetrics(nextCursor);
      }
    }
  });

  const toggleButton = document.querySelector(".toggle-llm-column");
  const llmColumn = document.querySelector(".llm-conversation-column");
  const tableBody = document.getElementById("metricsTableBody");
  let isColumnVisible = false;

  // Add CSS for hiding/showing the column
  const style = document.createElement("style");
  style.textContent = `
      .llm-conversation-column,
      tr td:nth-child(9) {
          display: none;
      }
      .llm-conversation-column.visible,
      tr td:nth-child(9).visible {
          display: table-cell;
      }
      .toggle-llm-column {
          transition: all 0.3s ease;
      }
      .toggle-llm-column i {
          margin-right: 5px;
      }
  `;
  document.head.appendChild(style);

  // Set initial state
  llmColumn.classList.remove("visible");
  const icon = toggleButton.querySelector("i");
  icon.className = "bi bi-eye-slash";
  toggleButton.title = "Show LLM Conversation ID";

  toggleButton.addEventListener("click", function () {
    isColumnVisible = !isColumnVisible;

    // Toggle the column visibility
    llmColumn.classList.toggle("visible");

    // Toggle visibility of all cells in the 9th column
    const rows = tableBody.getElementsByTagName("tr");
    for (let row of rows) {
      const cell = row.cells[8]; // 8 is the index of the 9th column (0-based)
      if (cell) {
        cell.classList.toggle("visible");
      }
    }

    // Update the button icon and text
    const icon = toggleButton.querySelector("i");
    icon.className = isColumnVisible ? "bi bi-eye" : "bi bi-eye-slash";
    toggleButton.innerHTML = `${icon.outerHTML} LLM Conversation ID`;

    // Update the button title
    toggleButton.title = isColumnVisible
      ? "Hide LLM Conversation ID"
      : "Show LLM Conversation ID";
  });
});

function loadMetrics(cursor = null) {
  if (isLoading) return;
  isLoading = true;

  console.log("Loading metrics...");
  const url = cursor
    ? `/robot/api/v1/admin/metrics?cursor=${cursor}`
    : "/robot/api/v1/admin/metrics";

  fetch(url)
    .then((response) => {
      console.log("Response status:", response.status);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.json();
    })
    .then((data) => {
      console.log("Received data structure:", data);
      if (data.status === 200) {
        console.log("Metrics array:", data.data.metrics);
        if (!data.data.metrics) {
          console.error("No metrics array found in response");
          return;
        }
        // Update next cursor
        nextCursor = data.data.nextCursor;
        // Append metrics to table
        displayMetrics(data.data.metrics, !cursor);
      } else {
        console.error("Error loading metrics:", data.message);
      }
    })
    .catch((error) => {
      console.error("Error fetching metrics:", error);
    })
    .finally(() => {
      isLoading = false;
    });
}

function displayMetrics(metrics, clearTable = false) {
  console.log("Starting displayMetrics with:", metrics);
  const tbody = document.getElementById("metricsTableBody");
  const isColumnVisible = document
    .querySelector(".llm-conversation-column")
    .classList.contains("visible");

  if (!tbody) {
    console.error("Could not find metricsTableBody element");
    return;
  }

  // Clear table only if this is the first load
  if (clearTable) {
    tbody.innerHTML = "";
  }

  if (!metrics || !Array.isArray(metrics)) {
    console.error("Invalid metrics data:", metrics);
    return;
  }

  metrics.forEach((metric) => {
    const row = document.createElement("tr");
    row.innerHTML = `
            <td>${metric.conversationId || "N/A"}</td>
            <td class="${getTimeClass(metric.resTime)}">${
      metric.resTime || ""
    }ms</td>
            <td class="${getTimeClass(metric.stalling)}">${
      metric.stalling ? metric.stalling + "ms" : ""
    }</td>
              <td class="${getTimeClass(metric.uploadAudio)}">${
      metric.uploadAudio ? metric.uploadAudio + "ms" : ""
    }</td>
            <td class="${getTimeClass(metric.botLlmTime)}">${
      metric.botLlmTime ? metric.botLlmTime + "ms" : ""
    }</td>
            <td class="${getTimeClass(metric.ttsTime)}">${
      metric.ttsTime ? metric.ttsTime + "ms" : ""
    }</td>
            <td class="${getTimeClass(metric.animationsTime)}">${
      metric.animationsTime ? metric.animationsTime + "ms" : ""
    }</td>
            <td>${formatTimestamp(metric.timestamp)}</td>
              <td class="${isColumnVisible ? "visible" : ""}">${
      metric.llmConversationId || "N/A"
    }</td>
        `;
    tbody.appendChild(row);
  });
}

function getTimeClass(time) {
  if (!time) return "";
  if (time > 5000) return "text-danger"; // Over 5 seconds
  if (time > 2000) return "text-warning"; // Over 2 seconds
  return "text-success"; // Under 2 seconds
}

function formatTimestamp(timestamp) {
  if (!timestamp) return null;
  try {
    const date = new Date(timestamp);
    return date.toLocaleString("vi-VN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false,
    });
  } catch (e) {
    console.error("Error formatting timestamp:", e);
    return timestamp;
  }
}
