class JsonViewer {
  constructor(element) {
    this.element = element;
    this.json = null;
    this.addControls();
  }

  addControls() {
    const controls = document.createElement("div");
    controls.className = "json-controls";

    const expandAll = document.createElement("button");
    expandAll.className = "json-control-button";
    expandAll.textContent = "Expand All";
    expandAll.onclick = () => this.expandAll();

    const collapseAll = document.createElement("button");
    collapseAll.className = "json-control-button";
    collapseAll.textContent = "Collapse All";
    collapseAll.onclick = () => this.collapseAll();

    controls.appendChild(expandAll);
    controls.appendChild(collapseAll);
    this.element.appendChild(controls);
  }

  expandAll() {
    const toggles = this.element.querySelectorAll(".expanded, .collapsed");
    toggles.forEach((toggle) => {
      toggle.classList.remove("collapsed");
      toggle.classList.add("expanded");
    });
  }

  collapseAll() {
    const toggles = this.element.querySelectorAll(".expanded, .collapsed");
    toggles.forEach((toggle) => {
      toggle.classList.remove("expanded");
      toggle.classList.add("collapsed");
    });
  }

  setJson(json) {
    this.json = json;
    this.render();
  }

  render() {
    // Clear existing content except controls
    const controls = this.element.querySelector(".json-controls");
    this.element.innerHTML = "";
    if (controls) {
      this.element.appendChild(controls);
    }

    if (typeof this.json === "string") {
      try {
        this.json = JSON.parse(this.json);
      } catch (e) {
        this.element.textContent = this.json;
        return;
      }
    }

    const rootUl = document.createElement("ul");
    rootUl.className = "json-tree";

    // Create root node
    const rootLi = document.createElement("li");
    const rootSpan = document.createElement("span");
    rootSpan.className = "collapsed";
    rootSpan.textContent = " JSON";
    rootSpan.onclick = function () {
      this.classList.toggle("expanded");
      this.classList.toggle("collapsed");
    };
    rootLi.appendChild(rootSpan);

    // Create child tree
    const childUl = this.createTreeView(this.json);
    rootLi.appendChild(childUl);
    rootUl.appendChild(rootLi);

    this.element.appendChild(rootUl);

    // Add click handlers for collapsible elements
    const collapsibles = this.element.querySelectorAll(".expanded, .collapsed");
    collapsibles.forEach((item) => {
      if (!item.onclick) {
        item.onclick = function (e) {
          e.stopPropagation();
          this.classList.toggle("expanded");
          this.classList.toggle("collapsed");
        };
      }
    });
  }

  createTreeView(data, path = "") {
    const ul = document.createElement("ul");

    if (Array.isArray(data)) {
      // Handle array
      for (let i = 0; i < data.length; i++) {
        const li = document.createElement("li");
        const itemPath = path ? `${path}[${i}]` : `[${i}]`;

        if (this.isComplexValue(data[i])) {
          const span = document.createElement("span");
          span.className = "collapsed";

          // Add array icon
          const iconSpan = document.createElement("span");
          iconSpan.className = Array.isArray(data[i])
            ? "array-icon"
            : "object-icon";
          span.appendChild(iconSpan);

          span.appendChild(document.createTextNode(` ${i}`));
          li.appendChild(span);

          // Create child tree
          const childUl = this.createTreeView(data[i], itemPath);
          li.appendChild(childUl);
        } else {
          // Simple value
          const valueSpan = document.createElement("span");
          valueSpan.innerHTML = this.formatValue(data[i]);
          li.innerHTML = `<span class="property">${i}</span>: `;
          li.appendChild(valueSpan);
        }

        ul.appendChild(li);
      }
    } else if (this.isObject(data)) {
      // Handle object
      const keys = Object.keys(data);
      for (let i = 0; i < keys.length; i++) {
        const key = keys[i];
        const li = document.createElement("li");
        const itemPath = path ? `${path}.${key}` : key;

        if (this.isComplexValue(data[key])) {
          const span = document.createElement("span");
          span.className = "collapsed";

          // Add object/array icon
          const iconSpan = document.createElement("span");
          iconSpan.className = Array.isArray(data[key])
            ? "array-icon"
            : "object-icon";
          span.appendChild(iconSpan);

          span.appendChild(document.createTextNode(` ${key}`));
          li.appendChild(span);

          // Create child tree
          const childUl = this.createTreeView(data[key], itemPath);
          li.appendChild(childUl);
        } else {
          // Simple value
          const valueSpan = document.createElement("span");
          valueSpan.innerHTML = this.formatValue(data[key]);
          li.innerHTML = `<span class="property">${key}</span>: `;
          li.appendChild(valueSpan);
        }

        ul.appendChild(li);
      }
    }

    return ul;
  }

  isComplexValue(value) {
    return this.isObject(value) && Object.keys(value).length > 0;
  }

  formatValue(value) {
    if (typeof value === "string") {
      return `<span class="string">"${this.escapeHTML(value)}"</span>`;
    } else if (typeof value === "number") {
      return `<span class="number">${value}</span>`;
    } else if (typeof value === "boolean") {
      return `<span class="boolean">${value}</span>`;
    } else if (value === null) {
      return `<span class="null">null</span>`;
    } else if (this.isObject(value)) {
      // Empty object or array
      if (Array.isArray(value)) {
        return `<span class="array-bracket">[]</span>`;
      } else {
        return `<span class="object-bracket">{}</span>`;
      }
    } else {
      return String(value);
    }
  }

  isObject(value) {
    return value !== null && typeof value === "object";
  }

  escapeHTML(str) {
    return str
      .replace(/&/g, "&amp;")
      .replace(/</g, "&lt;")
      .replace(/>/g, "&gt;")
      .replace(/"/g, "&quot;")
      .replace(/'/g, "&#039;");
  }
}
