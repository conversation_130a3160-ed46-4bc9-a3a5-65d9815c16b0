document.addEventListener("DOMContentLoaded", function () {
  // Get DOM elements
  const settingTypeSelect = document.getElementById("settingType");
  const volumeSettingSection = document.getElementById("volumeSettingSection");
  const brightnessSettingSection = document.getElementById(
    "brightnessSettingSection"
  );
  const phoneSettingSection = document.getElementById("phoneSettingSection");
  const volumeValueInput = document.getElementById("volumeValue");
  const brightnessValueInput = document.getElementById("brightnessValue");
  const phoneValueInput = document.getElementById("phoneValue");
  const robotSettingsForm = document.getElementById("robotSettingsForm");
  const settingResult = document.getElementById("settingResult");
  const robotIdInput = document.getElementById("robotId");

  // Show/hide setting sections based on selected setting type
  settingTypeSelect.addEventListener("change", function () {
    const selectedValue = this.value;

    // Hide all setting sections first
    volumeSettingSection.style.display = "none";
    brightnessSettingSection.style.display = "none";
    phoneSettingSection.style.display = "none";

    // Remove required attribute from all inputs
    volumeValueInput.removeAttribute("required");
    brightnessValueInput.removeAttribute("required");
    phoneValueInput.removeAttribute("required");

    // Show the appropriate section based on selection
    if (selectedValue === "VOLUME") {
      volumeSettingSection.style.display = "block";
      volumeValueInput.setAttribute("required", "required");
    } else if (selectedValue === "SCREEN_BRIGHTNESS") {
      brightnessSettingSection.style.display = "block";
      brightnessValueInput.setAttribute("required", "required");
    } else if (selectedValue === "MAP_TO_PHONE") {
      phoneSettingSection.style.display = "block";
      phoneValueInput.setAttribute("required", "required");
    }
  });

  // Handle form submission
  robotSettingsForm.addEventListener("submit", function (event) {
    event.preventDefault();

    // Get form values
    const robotId = robotIdInput.value.trim();
    const settingType = settingTypeSelect.value;

    // Validate robot ID
    if (!robotId) {
      showResult("Please enter a Robot ID", "danger");
      return;
    }

    // Get the appropriate value based on setting type
    let value;
    let endpoint = "/robot/api/v1/device/trigger_test";

    if (settingType === "VOLUME") {
      value = parseInt(volumeValueInput.value);
      if (isNaN(value) || value < 1 || value > 21) {
        showResult("Volume must be a number between 1 and 21", "danger");
        return;
      }
    } else if (settingType === "SCREEN_BRIGHTNESS") {
      value = parseInt(brightnessValueInput.value);
      if (isNaN(value) || value < 1 || value > 100) {
        showResult(
          "Screen brightness must be a number between 1 and 100",
          "danger"
        );
        return;
      }
    } else if (settingType === "MAP_TO_PHONE") {
      value = phoneValueInput.value.trim();
      if (!value) {
        showResult("Please enter a phone number", "danger");
        return;
      }
      endpoint = "/robot/api/v1/admin/map_robot_to_phone";
    } else {
      showResult("Please select a setting type", "danger");
      return;
    }

    // Prepare request data
    const requestData = {
      trigger_type: settingType,
      robot_id: robotId,
      value: value,
    };

    // Send API request
    fetch(endpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestData),
    })
      .then((response) => {
        if (!response.ok) {
          return response.json().then((data) => {
            throw new Error(data.message || "Network response was not ok");
          });
        }
        return response.json();
      })
      .then((data) => {
        showResult(data.message || "Settings applied successfully!", "success");
        console.log("API Response:", data);
      })
      .catch((error) => {
        showResult("Error: " + error.message, "danger");
        console.error("Error:", error);
      });
  });

  // Helper function to show result message
  function showResult(message, type) {
    settingResult.textContent = message;
    settingResult.className = "alert alert-" + type;
    settingResult.style.display = "block";

    // Auto-hide after 5 seconds
    setTimeout(() => {
      settingResult.style.display = "none";
    }, 5000);
  }
});
