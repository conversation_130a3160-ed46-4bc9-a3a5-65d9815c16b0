// Load SD paths when page loads
document.addEventListener("DOMContentLoaded", function () {
  updateFileInputState();
});

// Function to update file input state based on path selection
function updateFileInputState() {

  const fileInput = document.getElementById("file");
  const uploadButton = document.getElementById("uploadButton");


    fileInput.disabled = false;
    uploadButton.disabled = false;
    fileInput.title = "";
    uploadButton.title = "";

}

// Add variable to store the last uploaded URL
let lastUploadedUrl = "";

document.getElementById("uploadForm").addEventListener("submit", function (e) {
  e.preventDefault();



  const fileInput = document.getElementById("file");
  if (!fileInput.files.length) {
    alert("Please select a file to upload.");
    return;
  }

  const formData = new FormData(this);
  const file = fileInput.files[0];
  const fileType = file.type;

  fetch("/robot/api/v1/admin/upload", {
    method: "POST",
    body: formData,
  })
    .then((response) => response.json())
    .then((data) => {
      const uploadResult = document.getElementById("uploadResult");
      const fileUrl = document.getElementById("fileUrl");
      const imagePreview = document.getElementById("imagePreview");
      const audioPreviewContainer = document.getElementById(
        "audioPreviewContainer"
      );
      const audioPreview = document.getElementById("audioPreview");

      // Store the uploaded URL
      lastUploadedUrl = data.data;

      fileUrl.href = lastUploadedUrl;
      fileUrl.textContent = lastUploadedUrl;
      uploadResult.style.display = "block";

      // Reset previews
      imagePreview.style.display = "none";
      audioPreviewContainer.style.display = "none";

    })
    .catch((error) => {
      console.error("Error:", error);
      alert("Upload failed. Please try again.");
    });
});

// Add event listener for Update SD button
document
  .getElementById("updateSdButton")
  .addEventListener("click", function () {
    const robotId = document.getElementById("robotId").value.trim();

    if (!robotId) {
      alert("Please enter a Robot ID");
      return;
    }

    if (!lastUploadedUrl) {
      alert("Please upload a file first");
      return;
    }

    const updateData = {
      robot_id: robotId,
      url: lastUploadedUrl, // Include the uploaded URL
    };

    fetch("/robot/api/v1/admin/face_demo", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(updateData),
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.status === 200) {
          alert("Show face demo successful!");
        } else {
          alert("Show face demo fail: " + (data.message || "Unknown error"));
        }
      })
      .catch((error) => {
        console.error("Error show face demo:", error);
        alert("Failed to show face demo. Please try again.");
      });
  });
