<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
  <head>
    <title>Chat History</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css"
    />
    <link rel="stylesheet" href="/css/json-viewer.css" />
    <style>
      .chat-container {
        max-width: 800px;
        margin: 0 auto;
      }

      .message {
        margin-bottom: 20px;
        display: flex;
        flex-direction: column;
      }

      .message-content {
        max-width: 70%;
        padding: 12px 16px;
        border-radius: 12px;
        margin: 4px 0;
        position: relative;
        padding-bottom: 40px;
      }

      .media-container {
        margin-top: 8px;
        max-width: 100%;
      }

      .media-container img,
      .media-container video {
        max-width: 100%;
        height: auto;
        border-radius: 8px;
      }

      .message-time {
        font-size: 0.8em;
        color: #666;
        margin: 0 8px;
      }

      .user-message {
        align-items: flex-end;
      }

      .bot-message {
        align-items: flex-start;
      }

      .user-message .message-content {
        background: #007bff;
        color: white;
        border-bottom-right-radius: 4px;
      }

      .bot-message .message-content {
        background: #e9ecef;
        color: #212529;
        border-bottom-left-radius: 4px;
      }

      .header {
        background: #007bff;
        color: white;
        padding: 20px 0;
        margin-bottom: 30px;
      }

      .chat-header {
        position: sticky;
        top: 0;
        background: white;
        padding: 15px 0;
        border-bottom: 1px solid #dee2e6;
        z-index: 1000;
      }

      .audio-player {
        margin-top: 8px;
        width: 250px;
        margin-bottom: 8px;
      }

      .mode-switch {
        margin-left: 10px;
        display: inline-flex;
        align-items: center;
      }

      .realtime-indicator {
        color: #28a745;
        display: none;
        margin-left: 8px;
      }

      .realtime-indicator.active {
        display: inline;
      }

      .report-icon {
        cursor: pointer;
        color: #dc3545;
        margin-left: 8px;
        opacity: 0.6;
      }

      .report-icon:hover {
        opacity: 1;
      }

      /* Styles for like/dislike buttons */
      .message-actions {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-left: 8px;
        position: absolute;
        bottom: 8px;
        right: 8px;
        background: inherit;
        padding: 4px 8px;
        border-radius: 4px;
      }

      .action-button {
        background: none;
        border: none;
        padding: 4px;
        display: flex;
        align-items: center;
        gap: 4px;
        color: #666;
        font-size: 1em;
        cursor: pointer;
      }

      .action-button i {
        font-size: 1em;
      }

      .action-count {
        font-size: 0.9em;
      }

      .action-button:hover {
        opacity: 0.8;
      }

      /* Styles cho modal report */
      .report-modal .modal-dialog {
        max-width: 400px;
        margin: 1.75rem auto;
      }

      .report-modal .modal-content {
        border: none;
        border-radius: 16px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
      }

      .report-modal .modal-header {
        padding: 20px 24px;
      }

      .report-modal .modal-body {
        padding: 24px;
      }

      .report-modal .form-check {
        padding: 12px 20px;
        margin: 0 -12px 8px -12px;
        border-radius: 10px;
      }

      .report-modal .form-check-label {
        font-size: 1rem;
        padding-left: 12px;
      }

      .report-modal .form-check-input {
        width: 20px;
        height: 20px;
        margin-top: 2px;
      }

      .report-modal #otherReasonInput {
        padding: 0 8px;
      }

      .report-modal #otherReasonText {
        padding: 10px 16px;
        font-size: 1rem;
      }

      .report-modal .btn-submit {
        padding: 10px 28px;
        font-size: 1rem;
      }
    </style>
  </head>
  <body>
    <div class="header">
      <div class="container">
        <div class="d-flex justify-content-between align-items-center">
          <h2>Chat History</h2>
          <a href="/web/admin/conversations" class="btn btn-outline-light"
            >Back to List</a
          >
        </div>
      </div>
    </div>

    <div class="container">
      <div class="chat-container">
        <div class="chat-header mb-4">
          <div class="d-flex flex-column">
            <div class="d-flex justify-content-between align-items-center mb-2">
              <h5>
                Conversation with User
                <span th:text="${conversation.userId}"></span>
              </h5>
              <div class="mode-switch" style="display: none">
                <input
                  type="checkbox"
                  id="realtimeToggle"
                  class="form-check-input"
                />
                <label class="form-check-label ms-2">Real-time Mode</label>
                <span class="realtime-indicator">●&nbsp;Live</span>
              </div>
            </div>

            <div class="d-flex flex-wrap gap-3 mb-2">
              <div class="btn-group me-3">
                <button
                  th:if="${conversation.video != null && conversation.video != ''}"
                  class="btn btn-outline-primary"
                  onclick="showVideoModal()"
                >
                  <i class="bi bi-camera-video"></i> View Video
                </button>
                <button
                  th:if="${conversation.log != null && conversation.log != ''}"
                  class="btn btn-outline-primary"
                  onclick="showLogModal()"
                >
                  <i class="bi bi-journal-text"></i> View Log
                </button>
                <button
                  th:if="${conversation.serverLog != null && conversation.serverLog != ''}"
                  class="btn btn-outline-primary"
                  onclick="showServerLogModal()"
                >
                  <i class="bi bi-server"></i> Server Log
                </button>
                <button
                  class="btn btn-outline-primary"
                  onclick="showProfileModal()"
                  th:data-robot-id="${conversation.robotId}"
                >
                  <i class="bi bi-person-circle"></i> Profile Info
                </button>
              </div>

              <div class="btn-group">
                <button
                  id="generateReportBtn"
                  class="btn btn-outline-info"
                  onclick="generateReport()"
                >
                  <i class="bi bi-file-earmark-bar-graph"></i> Generate Report
                </button>
                <button
                  id="downloadExcelBtn"
                  class="btn btn-outline-success"
                  onclick="downloadUserMessagesExcel()"
                >
                  <i class="bi bi-file-earmark-excel"></i> Download User
                  Messages
                </button>
              </div>
            </div>

            <small
              class="text-muted"
              th:text="${conversation.botType + ' - Bot ' + conversation.botId + ' - STT ' + conversation.asrType + ' - Phone ' + conversation.phone + ' - id ' + conversation.id}"
              th:attr="data-phone=${conversation.phone}"
            >
            </small>
            <div id="totalFeedback" class="mt-2">
              <span class="badge bg-success me-2">
                <i class="bi bi-hand-thumbs-up"></i>
                <span th:text="${likes}" id="totalLikes">0</span>
              </span>
              <span class="badge bg-danger">
                <i class="bi bi-hand-thumbs-down"></i>
                <span th:text="${dislikes}" id="totalDislikes">0</span>
              </span>
            </div>
          </div>
        </div>

        <div class="chat-messages" id="chat-messages">
          <div
            th:each="msg : ${messages}"
            th:class="${msg.character.name() == 'USER'} ? 'message user-message' : 'message bot-message'"
            th:data-message-id="${msg.id}"
          >
            <div class="message-content">
              <div class="d-flex justify-content-between align-items-start">
                <p class="mb-0" th:text="${msg.content}"></p>
                <i
                  class="bi bi-exclamation-triangle-fill report-icon"
                  th:if="${msg.dataReport != null}"
                  th:data-message-id="${msg.id}"
                  th:data-message-content="${msg.content}"
                  th:data-report="${msg.dataReport}"
                  onclick="showDataReportModal(this.dataset.report)"
                ></i>
                <i
                  class="bi bi-exclamation-triangle-fill report-icon"
                  th:if="${msg.dataReport == null}"
                  onclick="showReportModal(this)"
                  th:data-message-id="${msg.id}"
                  th:data-message-content="${msg.content}"
                ></i>
              </div>
              <div th:if="${msg.audio}" class="audio-player">
                <audio controls class="w-100">
                  <source th:src="${msg.audio}" type="audio/mpeg" />
                </audio>
              </div>
              <div th:if="${msg.image}" class="media-container">
                <img th:src="${msg.image}" alt="Message Image" />
              </div>
              <div th:if="${msg.video}" class="media-container">
                <video controls>
                  <source th:src="${msg.video}" type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
              </div>
              <div
                class="message-actions"
                th:if="${msg.character.name() != 'USER'}"
              >
                <div class="action-button">
                  <i class="bi bi-hand-thumbs-up"></i>
                  <span
                    class="action-count"
                    th:text="${msg.likes != null ? msg.likes : 0}"
                    >0</span
                  >
                </div>
                <div class="action-button">
                  <i class="bi bi-hand-thumbs-down"></i>
                  <span
                    class="action-count"
                    th:text="${msg.dislikes != null ? msg.dislikes : 0}"
                    >0</span
                  >
                </div>
              </div>
            </div>
            <div
              class="message-time"
              th:text="${#dates.format(msg.createdAt, 'HH:mm:ss dd-MM-yyyy')}"
            ></div>
          </div>
        </div>

        <div id="loading" class="text-center d-none">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>

        <!-- Overlay loading -->
        <div
          id="loadingOverlay"
          style="
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 9999;
          "
        >
          <div
            style="
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
            "
          >
            <div
              class="spinner-border text-light"
              style="width: 3rem; height: 3rem"
              role="status"
            >
              <span class="sr-only">Loading...</span>
            </div>
            <p class="text-light mt-2">Generating report, please wait...</p>
          </div>
        </div>

        <script th:inline="javascript">
          let currentPage = 1;
          let loading = false;
          let hasMore = true;
          let realtimeMode = false;
          let realtimeInterval;
          let lastMessageId = null;
          const conversationId = /*[[${conversation.id}]]*/ "";
          console.log("conversationId", conversationId);

          // Fallback: If conversationId is empty, try to extract it from the URL
          function getConversationIdFromUrl() {
            if (conversationId && conversationId !== "") return conversationId;

            const pathParts = window.location.pathname.split("/");
            // URL pattern is expected to be /web/admin/conversations/{id}
            for (let i = 0; i < pathParts.length; i++) {
              if (
                pathParts[i] === "conversations" &&
                i + 1 < pathParts.length
              ) {
                const potentialId = pathParts[i + 1];
                // Make sure it's a number
                if (!isNaN(potentialId) && potentialId !== "") {
                  console.log(
                    "Extracted conversationId from URL:",
                    potentialId
                  );
                  return potentialId;
                }
              }
            }
            console.error("Could not determine conversation ID");
            return null;
          }

          document.addEventListener("DOMContentLoaded", function () {
            const messages = document.querySelectorAll(".message");
            if (messages.length > 0) {
              const lastMessage = messages[messages.length - 1];
              lastMessageId = lastMessage.dataset.messageId;
            }
          });

          function createMessageElement(msg) {
            const messageDiv = document.createElement("div");
            messageDiv.className =
              msg.character === "USER"
                ? "message user-message"
                : "message bot-message";
            messageDiv.dataset.messageId = msg.id;

            const date = new Date(msg.createdAt);
            const formattedDate =
              date.toLocaleTimeString("en-GB") +
              " " +
              date.getDate().toString().padStart(2, "0") +
              "-" +
              (date.getMonth() + 1).toString().padStart(2, "0") +
              "-" +
              date.getFullYear();

            const mediaContent = msg.image
              ? `
                <div class="media-container">
                    <img src="${msg.image}" alt="Message Image">
                </div>
            `
              : msg.video
              ? `
                <div class="media-container">
                    <video controls>
                        <source src="${msg.video}" type="video/mp4">
                        Your browser does not support the video tag.
                    </video>
                </div>
            `
              : "";

            const content = `
                <div class="message-content">
                    <div class="d-flex justify-content-between align-items-start">
                        <p class="mb-0">${msg.content}</p>
                        <i class="bi bi-exclamation-triangle-fill report-icon"
                           onclick="showReportModal(this)"
                           data-message-id="${msg.id}"
                           data-message-content="${msg.content}"></i>
                    </div>
                    ${
                      msg.audio
                        ? `
                        <div class="audio-player">
                            <audio controls class="w-100">
                                <source src="${msg.audio}" type="audio/mpeg">
                            </audio>
                        </div>
                    `
                        : ""
                    }
                    ${mediaContent}
                </div>
                <div class="message-time">${formattedDate}</div>
            `;

            messageDiv.innerHTML = content;
            return messageDiv;
          }

          function getMaxMessageId() {
            const messages = document.querySelectorAll(".message");
            let maxId = 0;
            messages.forEach((message) => {
              const messageId = parseInt(message.dataset.messageId);
              if (messageId > maxId) {
                maxId = messageId;
              }
            });
            return maxId || null;
          }

          function fetchNewMessages() {
            const effectiveConversationId = getConversationIdFromUrl();
            if (!effectiveConversationId) {
              console.error(
                "Cannot fetch messages: No conversation ID available"
              );
              return;
            }

            const url = new URL(
              `/web/admin/conversations/${effectiveConversationId}/messages`,
              window.location.origin
            );
            url.searchParams.append("realtime", "true");

            // Always get the current maximum ID
            const currentMaxId = getMaxMessageId();
            if (currentMaxId) {
              url.searchParams.append("afterId", currentMaxId);
            }

            fetch(url)
              .then((response) => {
                if (!response.ok) {
                  throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
              })
              .then((data) => {
                const chatMessages = document.getElementById("chat-messages");
                let newMessagesAdded = false;

                if (data.content && data.content.length > 0) {
                  // Get all existing message IDs
                  const existingMessageIds = new Set();
                  document.querySelectorAll(".message").forEach((msg) => {
                    existingMessageIds.add(msg.dataset.messageId);
                  });

                  // Append only new messages that don't already exist
                  data.content.forEach((msg) => {
                    if (!existingMessageIds.has(msg.id.toString())) {
                      const messageElement = createMessageElement(msg);
                      chatMessages.appendChild(messageElement);
                      newMessagesAdded = true;
                    }
                  });

                  // Only scroll if new messages were actually added
                  if (newMessagesAdded) {
                    // Always scroll to bottom in real-time mode
                    if (realtimeMode) {
                      window.scrollTo({
                        top: document.body.scrollHeight,
                        behavior: "smooth",
                      });
                    } else {
                      // Auto-scroll to bottom only if near bottom when not in real-time mode
                      const isNearBottom =
                        window.innerHeight + window.scrollY >=
                        document.body.offsetHeight - 1000;
                      if (isNearBottom) {
                        window.scrollTo({
                          top: document.body.scrollHeight,
                          behavior: "smooth",
                        });
                      }
                    }
                  }
                }
              })
              .catch((error) => {
                console.error("Error fetching new messages:", error);
                // If there's an error, don't stop the real-time updates
                // The next interval will try again
              });
          }

          function toggleRealtimeMode(enabled) {
            const indicator = document.querySelector(".realtime-indicator");
            if (enabled) {
              realtimeMode = true;
              indicator.classList.add("active");
              realtimeInterval = setInterval(fetchNewMessages, 5000);
            } else {
              realtimeMode = false;
              indicator.classList.remove("active");
              clearInterval(realtimeInterval);
            }
          }

          // Add event listener for the toggle button
          document
            .getElementById("realtimeToggle")
            .addEventListener("change", (e) => {
              toggleRealtimeMode(e.target.checked);
            });
        </script>

        <script>
          // Handle audio playback
          document.addEventListener(
            "play",
            function (e) {
              // Get all audio elements
              const audios = document.getElementsByTagName("audio");

              // Pause all audio elements except the one that just started playing
              for (let audio of audios) {
                if (audio !== e.target) {
                  audio.pause();
                  audio.currentTime = 0;
                }
              }
            },
            true
          ); // Use capture phase to ensure this runs before other event listeners
        </script>

        <script th:inline="javascript">
          document.addEventListener("DOMContentLoaded", function () {
            const conversationCreatedAt = new Date(
              /*[[${conversation.createdAt}]]*/ ""
            );
            const thirtyMinutesAgo = new Date(Date.now() - 15 * 60 * 1000);

            // Show real-time toggle only if conversation is less than 30 minutes old
            if (conversationCreatedAt > thirtyMinutesAgo) {
              document.querySelector(".mode-switch").style.display =
                "inline-flex";
            }
          });
        </script>
      </div>
    </div>

    <!-- Add Modal for Log -->
    <div class="modal fade" id="logModal" tabindex="-1">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Conversation Log</h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
            ></button>
          </div>
          <div class="modal-body">
            <pre id="logContent" style="white-space: pre-wrap"></pre>
          </div>
        </div>
      </div>
    </div>

    <!-- Add this before the closing body tag -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script th:inline="javascript">
      const conversationLog = /*[[${conversation.log}]]*/ "";
      const logModal = new bootstrap.Modal(document.getElementById("logModal"));

      function showLogModal() {
        try {
          const logContent = document.getElementById("logContent");
          const parsedLog = JSON.parse(conversationLog);
          logContent.textContent = JSON.stringify(parsedLog, null, 2);
          logModal.show();
        } catch (e) {
          console.error("Error parsing log:", e);
          // If JSON parsing fails, show raw log
          document.getElementById("logContent").textContent = conversationLog;
          logModal.show();
        }
      }
    </script>

    <!-- Thêm modal report vào cuối file, trước closing body tag -->
    <div class="modal fade report-modal" id="reportModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Report conversation</h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
            ></button>
          </div>
          <div class="modal-body">
            <form id="reportForm">
              <input type="hidden" id="reportMessageId" />
              <div class="form-check mb-3">
                <input
                  class="form-check-input"
                  type="radio"
                  name="reportReason"
                  id="unclear_audio"
                  value="Speech-to-text sai"
                />
                <label class="form-check-label" for="unclear_audio">
                  Speech-to-text sai
                </label>
              </div>
              <div class="form-check mb-3">
                <input
                  class="form-check-input"
                  type="radio"
                  name="reportReason"
                  id="no_play"
                  value="User nói nhưng không nghe được"
                />
                <label class="form-check-label" for="no_play">
                  User nói nhưng không nghe được
                </label>
              </div>
              <div class="form-check mb-3">
                <input
                  class="form-check-input"
                  type="radio"
                  name="reportReason"
                  id="wrong_text"
                  value="S2T nhầm ngôn ngữ (Anh ra Việt, Việt ra Anh)"
                />
                <label class="form-check-label" for="wrong_text">
                  S2T nhầm ngôn ngữ (Anh ra Việt, Việt ra Anh)
                </label>
              </div>
              <div class="form-check mb-3">
                <input
                  class="form-check-input"
                  type="radio"
                  name="reportReason"
                  id="other"
                  value="other"
                />
                <label class="form-check-label" for="other"> Khác </label>
              </div>
              <div id="otherReasonInput" class="mt-4 d-none">
                <input
                  type="text"
                  class="form-control"
                  id="otherReasonText"
                  placeholder="Nhập lý do..."
                />
              </div>
              <div class="text-end mt-4">
                <button
                  type="button"
                  class="btn btn-submit"
                  onclick="submitReport()"
                >
                  SEND
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>

    <!-- Cập nhật JavaScript cho chức năng report -->
    <script>
      const reportModal = new bootstrap.Modal(
        document.getElementById("reportModal")
      );

      // Thêm event listener để hiển thị/ẩn text input khi chọn "Khác"
      document
        .querySelectorAll('input[name="reportReason"]')
        .forEach((radio) => {
          radio.addEventListener("change", function () {
            const otherReasonInput =
              document.getElementById("otherReasonInput");
            if (this.value === "other") {
              otherReasonInput.classList.remove("d-none");
            } else {
              otherReasonInput.classList.add("d-none");
            }
          });
        });

      function showReportModal(element) {
        const messageId = element.dataset.messageId;
        document.getElementById("reportMessageId").value = messageId;
        document.getElementById("reportForm").reset();
        document.getElementById("otherReasonInput").classList.add("d-none");
        reportModal.show();
      }

      function submitReport() {
        const messageId = document.getElementById("reportMessageId").value;
        const selectedReason = document.querySelector(
          'input[name="reportReason"]:checked'
        );

        if (!selectedReason) {
          alert("Vui lòng chọn một lý do");
          return;
        }

        let reason = selectedReason.value;
        if (reason === "other") {
          const otherReason = document
            .getElementById("otherReasonText")
            .value.trim();
          if (!otherReason) {
            alert("Vui lòng nhập lý do");
            return;
          }
          reason = otherReason;
        }

        fetch(`/robot/api/v1/robot-open/personal/report`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            messageId: messageId,
            message: reason,
          }),
        })
          .then((response) => {
            if (response.ok) {
              alert("Đã gửi report thành công");
              reportModal.hide();
              location.reload();
            } else {
              alert("Gửi report thất bại");
              reportModal.hide();
              location.reload();
            }
          })
          .catch((error) => {
            console.error("Error submitting report:", error);
            alert("Đã xảy ra lỗi khi gửi báo cáo");
          });
      }

      function showDataReportModal(dataReport) {
        console.log("Data Report:", dataReport);
        document.getElementById("dataReportContent").textContent =
          dataReport || "No data available";
        const dataReportModal = new bootstrap.Modal(
          document.getElementById("dataReportModal")
        );
        dataReportModal.show();
      }
    </script>

    <!-- Modal for Data Report -->
    <div class="modal fade" id="dataReportModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Data Report</h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
            ></button>
          </div>
          <div class="modal-body">
            <pre id="dataReportContent" style="white-space: pre-wrap"></pre>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal for Video -->
    <div class="modal fade" id="videoModal" tabindex="-1">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Conversation Video</h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
            ></button>
          </div>
          <div class="modal-body">
            <div class="ratio ratio-16x9">
              <video id="videoPlayer" controls>
                <source id="videoSource" src="" type="video/mp4" />
                Your browser does not support the video tag.
              </video>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script th:inline="javascript">
      const conversationVideo = /*[[${conversation.video}]]*/ "";
      const videoModal = new bootstrap.Modal(
        document.getElementById("videoModal")
      );

      function showVideoModal() {
        const videoSource = document.getElementById("videoSource");
        videoSource.src = conversationVideo;

        const videoPlayer = document.getElementById("videoPlayer");
        videoPlayer.load();

        videoModal.show();
      }
    </script>

    <script>
      // Function to mask the first 5 digits of a phone number
      function maskPhoneNumber(phone) {
        if (!phone) return "";
        // Handle phone numbers that start with a plus sign
        return phone
          .toString()
          .replace(/^(\+?\d{0,5})(.*)/, function (match, p1, p2) {
            // If the phone starts with +, keep the + and mask the next 5 characters
            if (p1.startsWith("+")) {
              return "+" + "*".repeat(Math.min(5, p1.length - 1)) + p2;
            }
            // Otherwise mask the first 5 characters
            return "*".repeat(Math.min(5, p1.length)) + p2;
          });
      }

      // Immediately invoked function to mask phone numbers
      (function () {
        // Wait for DOM to be fully loaded
        if (document.readyState === "loading") {
          document.addEventListener("DOMContentLoaded", maskPhoneInHeader);
        } else {
          maskPhoneInHeader();
        }

        function maskPhoneInHeader() {
          // Mask phone number in the conversation header
          const phoneElement = document.querySelector("[data-phone]");
          if (phoneElement) {
            const phone = phoneElement.getAttribute("data-phone");
            const text = phoneElement.textContent;
            phoneElement.textContent = text.replace(
              "Phone " + phone,
              "Phone " + maskPhoneNumber(phone)
            );
          }
        }
      })();
    </script>

    <!-- Add Modal for Profile Info -->
    <div class="modal fade" id="profileModal" tabindex="-1">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Robot Profile Information</h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
            ></button>
          </div>
          <div class="modal-body">
            <div class="table-responsive">
              <table class="table table-striped">
                <thead>
                  <tr>
                    <th>Key</th>
                    <th>Value</th>
                  </tr>
                </thead>
                <tbody id="profileInfoBody"></tbody>
              </table>
            </div>
            <div id="profileLoading" class="text-center d-none">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script th:inline="javascript">
      const profileModal = new bootstrap.Modal(
        document.getElementById("profileModal")
      );

      function showProfileModal() {
        const robotId =
          document.querySelector("[data-robot-id]").dataset.robotId;
        const profileInfoBody = document.getElementById("profileInfoBody");
        const profileLoading = document.getElementById("profileLoading");

        // Clear previous content and show loading
        profileInfoBody.innerHTML = "";
        profileLoading.classList.remove("d-none");

        // Fetch profile information
        fetch(`/robot/api/v1/admin/profile-variables/${robotId}`)
          .then((response) => {
            if (!response.ok) {
              throw new Error("Failed to fetch profile information");
            }
            return response.json();
          })
          .then((data) => {
            console.log(data.data);
            const map = Object.entries(data.data);
            console.log(map);
            profileLoading.classList.add("d-none");
            if (map && map.length > 0) {
              map.forEach((item) => {
                const row = document.createElement("tr");
                row.innerHTML = `
                  <td>${item[0]}</td>
                  <td>${item[1]}</td>
                `;
                profileInfoBody.appendChild(row);
              });
            } else {
              profileInfoBody.innerHTML =
                '<tr><td colspan="2" class="text-center">No profile information available</td></tr>';
            }
          })
          .catch((error) => {
            profileLoading.classList.add("d-none");
            profileInfoBody.innerHTML =
              '<tr><td colspan="2" class="text-center text-danger">Error loading profile information</td></tr>';
            console.error("Error fetching profile information:", error);
          });

        profileModal.show();
      }
    </script>

    <!-- JavaScript to handle loading and navigation -->
    <script th:inline="javascript">
      function generateReport() {
        // Show loading overlay
        document.getElementById("loadingOverlay").style.display = "block";

        // Disable the button
        document.getElementById("generateReportBtn").disabled = true;

        // Get the conversation ID
        const conversationId = /*[[${conversation.id}]]*/ 0;

        // Navigate to the report page
        window.location.href = `/web/admin/conversations/${conversationId}/report/view`;

        // No need to hide overlay since we're navigating away
      }
    </script>

    <script th:inline="javascript">
      function downloadUserMessagesExcel() {
        // Show loading overlay
        document.getElementById("loadingOverlay").style.display = "block";

        // Get the conversation ID
        const conversationId = /*[[${conversation.id}]]*/ 0;

        // Create a form to submit the request
        const form = document.createElement("form");
        form.method = "POST";
        form.action = `/web/admin/conversations/${conversationId}/download-user-messages`;

        // Add CSRF token if needed (uncomment and modify if your application uses CSRF)
        // const csrfToken = document.querySelector('meta[name="_csrf"]').getAttribute('content');
        // const csrfHeader = document.querySelector('meta[name="_csrf_header"]').getAttribute('content');
        // const csrfInput = document.createElement('input');
        // csrfInput.type = 'hidden';
        // csrfInput.name = csrfHeader;
        // csrfInput.value = csrfToken;
        // form.appendChild(csrfInput);

        // Append form to body and submit
        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);

        // Hide loading overlay after a short delay
        setTimeout(() => {
          document.getElementById("loadingOverlay").style.display = "none";
        }, 1000);
      }
    </script>

    <!-- Add Modal for Server Log -->
    <div class="modal fade" id="serverLogModal" tabindex="-1">
      <div class="modal-dialog modal-fullscreen">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Server Log</h5>
            <div class="ms-auto me-2">
              <button
                type="button"
                class="btn btn-outline-primary btn-sm"
                onclick="copyJsonContent()"
              >
                <i class="bi bi-clipboard"></i> Copy JSON
              </button>
            </div>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
            ></button>
          </div>
          <div class="modal-body p-0">
            <div id="serverLogContent" class="json-viewer"></div>
          </div>
        </div>
      </div>
    </div>

    <script src="/js/json-viewer.js"></script>
    <script th:inline="javascript">
      const conversationServerLog = /*[[${conversation.serverLog}]]*/ "";
      const serverLogModal = new bootstrap.Modal(
        document.getElementById("serverLogModal")
      );
      const jsonViewer = new JsonViewer(
        document.getElementById("serverLogContent")
      );

      function showServerLogModal() {
        try {
          jsonViewer.setJson(conversationServerLog);
          serverLogModal.show();
        } catch (e) {
          console.error("Error parsing server log:", e);
          document.getElementById("serverLogContent").textContent =
            conversationServerLog;
          serverLogModal.show();
        }
      }

      function copyJsonContent() {
        try {
          const jsonContent =
            typeof conversationServerLog === "string"
              ? conversationServerLog
              : JSON.stringify(conversationServerLog, null, 2);

          navigator.clipboard
            .writeText(jsonContent)
            .then(() => {
              // Show success feedback
              const copyBtn = document.querySelector(".btn-outline-primary");
              const originalText = copyBtn.innerHTML;
              copyBtn.innerHTML = '<i class="bi bi-check2"></i> Copied!';
              copyBtn.classList.remove("btn-outline-primary");
              copyBtn.classList.add("btn-success");

              setTimeout(() => {
                copyBtn.innerHTML = originalText;
                copyBtn.classList.remove("btn-success");
                copyBtn.classList.add("btn-outline-primary");
              }, 2000);
            })
            .catch((err) => {
              console.error("Failed to copy: ", err);
              alert("Failed to copy JSON content");
            });
        } catch (e) {
          console.error("Error copying JSON:", e);
          alert("Error copying JSON content");
        }
      }
    </script>
  </body>
</html>
