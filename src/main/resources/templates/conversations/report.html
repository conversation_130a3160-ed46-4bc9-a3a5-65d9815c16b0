<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Conversation Report</title>
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .card-header {
            font-weight: bold;
            background-color: #f8f9fa;
        }
        .table th {
            font-weight: bold;
            background-color: #f0f0f0;
        }
        .pass-true {
            background-color: #d4edda;
        }
        .pass-false {
            background-color: #f8d7da;
        }
        .section-header {
            background-color: #e9ecef;
            font-weight: bold;
        }
        .back-button {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row back-button">
            <div class="col-12">
                <a th:href="@{/web/admin/conversations}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Conversations
                </a>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h2>Conversation Report</h2>
                        <h5 th:text="'Conversation ID: ' + ${conversationId}"></h5>
                    </div>
                </div>
            </div>
        </div>

        <!-- Error message if data is not available -->
        <div class="alert alert-danger" th:if="${reportData == null}">
            <p>Failed to load report data</p>
        </div>

        <!-- Debug information -->
        <div class="alert alert-info" th:if="${reportData != null && (reportData.keys == null || reportData.data == null)}">
            <h4>Debug Information:</h4>
            <p>Report data is available but structure is not as expected.</p>
            <p>Report data content: <span th:text="${reportData}"></span></p>
        </div>

        <!-- Consolidated Report Table -->
        <div th:if="${reportData != null && reportData.keys != null && reportData.data != null}">
            <div class="card">
                <div class="card-header">
                    <h3>Conversation Report Summary</h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Section</th>
                                    <th th:each="key : ${reportData.keys}" th:text="${key}"></th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr th:each="section, sectionStat : ${reportData.data}">
                                    <td class="section-header" th:text="'Section ' + ${sectionStat.count}"></td>
                                    <td th:each="item : ${section.content}" 
                                        th:class="${item.isPass != null ? (item.isPass ? 'pass-true' : 'pass-false') : ''}"
                                        th:text="${item.text}">
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>
</html> 