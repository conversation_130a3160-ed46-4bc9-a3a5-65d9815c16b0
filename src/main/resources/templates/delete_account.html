<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Delete Test Account</title>
    <style>
      .container {
        max-width: 400px;
        margin: 50px auto;
        padding: 20px;
        border: 1px solid #ccc;
        border-radius: 5px;
      }
      .form-group {
        margin-bottom: 15px;
      }
      input {
        width: 100%;
        padding: 8px;
        margin-top: 5px;
      }
      button {
        background-color: #dc3545;
        color: white;
        padding: 10px 15px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
      }
      button:hover {
        background-color: #c82333;
      }
      .result {
        margin-top: 20px;
        padding: 10px;
        border-radius: 4px;
      }
      .success {
        background-color: #d4edda;
        color: #155724;
      }
      .error {
        background-color: #f8d7da;
        color: #721c24;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h2>Delete Test Account</h2>
      <div class="form-group">
        <label for="phone">Phone Number:</label>
        <input type="text" id="phone" name="phone" required />
      </div>
      <button onclick="deleteAccount()">Delete Account</button>
      <div id="result" class="result" style="display: none"></div>
    </div>

    <script>
      async function deleteAccount() {
        const phone = document.getElementById("phone").value;
        const resultDiv = document.getElementById("result");

        if (!phone) {
          showResult("Please enter a phone number", false);
          return;
        }

        try {
          const response = await fetch(
            `/robot/api/v1/robot-open/personal/account?phone=${encodeURIComponent(
              phone
            )}`,
            {
              method: "DELETE",
            }
          );

          const data = await response.json();

          if (response.ok) {
            showResult("Account deleted successfully", true);
          } else {
            showResult(
              `Error: ${data.message || "Failed to delete account"}`,
              false
            );
          }
        } catch (error) {
          showResult(`Error: ${error.message}`, false);
        }
      }

      function showResult(message, isSuccess) {
        const resultDiv = document.getElementById("result");
        resultDiv.textContent = message;
        resultDiv.style.display = "block";
        resultDiv.className = `result ${isSuccess ? "success" : "error"}`;
      }
    </script>
  </body>
</html>
