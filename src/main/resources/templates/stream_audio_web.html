<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Speech to Text Conversation</title>
    <style>
        #status {
            font-weight: bold;
            color: green;
        }
    </style>
</head>
<body>
<h1>Speech to Text Conversation</h1>
<button id="startConversationButton">💬 Start Conversation</button>
<p id="status">Status: Idle</p>
<p id="transcript">Transcript will appear here...</p>
<p id="response">Response will appear here...</p>
<audio id="audioPlayback" controls style="display:none;"></audio>

<script>
    const startConversationButton = document.getElementById('startConversationButton');
    const status = document.getElementById('status');
    const transcript = document.getElementById('transcript');
    const response = document.getElementById('response');

    let socket;
    let audioContext;
    let source;
    let processor;
    let mediaStream;
    const SAMPLE_RATE = 16000; // 16,000 Hz sample rate
    const BYTES_PER_SAMPLE = 4; // 32-bit float = 4 bytes per sample
    const SAMPLES_PER_BUFFER = 1024; // 512 bytes / 4 bytes per sample = 128 samples
    const BYTES_PER_BUFFER = SAMPLES_PER_BUFFER * BYTES_PER_SAMPLE; // 512 bytes
    let isRecording = false;

    startConversationButton.addEventListener('click', () => {
        if (startConversationButton.textContent === "💬 Start Conversation") {
            startConversation();
            startConversationButton.textContent = "⏹ Stop Conversation";
        } else {
            stopConversation();
            startConversationButton.textContent = "💬 Start Conversation";
        }
    });

    function getWebSocketUrl() {
        const protocol = window.location.protocol === 'https:' ? 'wss://' : 'ws://';
        const host = window.location.host; // This will give you the host (domain + port if present)
        return `${protocol}${host}/ws/web/conversation?is_convert=true`; // Adjust the path if necessary
    }

    function startConversation() {
        if (!socket || socket.readyState !== WebSocket.OPEN) {
            let socketUrl = getWebSocketUrl();
            console.log("WebSocket URL:", socketUrl);
            socket = new WebSocket(socketUrl);

            console.log("WebSocket connection opened");

            socket.binaryType = 'arraybuffer'; // Expect binary data

            socket.onopen = function() {
                console.log("WebSocket connected");
                startRecording(); // Start recording when WebSocket is open
            };

            socket.onmessage = async function(event) {
                const responseMessage = JSON.parse(event.data);
                if (responseMessage.type === 'ASR' && responseMessage.data.transcript) {
                    transcript.innerText = responseMessage.data.transcript;
                    if (responseMessage.data.is_stop) {
                        console.log("Received is_stop = true. Pausing recording...");
                        console.log(responseMessage);
                        pauseRecording();
                        status.textContent = "Status: Waiting for response";
                        status.style.color = 'red'; // Set the text color to red
                    }
                } else if (responseMessage.type === 'CHAT_RESPONSE') {
                    console.log("Bot response received.", JSON.stringify(responseMessage));
                    // Assuming responseMessage.data is an array of messages, each containing text and audio.
                    await renderMessagesSequentially(responseMessage.data);

                    startRecording(); // Restart recording after bot responds
                } else if (responseMessage.type === "CHAT_STALLING") {
                    console.log(responseMessage);
                }
            };

            socket.onclose = function(event) {
                console.log("WebSocket closed with code:", event.code, "and reason:", event.reason);
            };

            socket.onerror = function(error) {
                console.error("WebSocket error:", error);
            };
        }
    }

    // Function to render messages and play audio sequentially
    async function renderMessagesSequentially(messages) {
        for (const message of messages) {
            // Render text
            response.innerText = message.text;

            // Play audio if it exists
            if (message.audio) {
                await playAudio(message.audio);
            }
        }
    }

    // Function to play audio and return a Promise that resolves when playback finishes
    function playAudio(audioUrl) {
        return new Promise((resolve) => {
            const audioPlayback = document.getElementById('audioPlayback');
            audioPlayback.src = audioUrl;
            audioPlayback.style.display = 'none'; // Ensure the audio element is hidden
            audioPlayback.play();

            // Resolve the promise once the audio finishes playing
            audioPlayback.onended = function() {
                resolve();
            };
        });
    }

    function startRecording() {
        if (isRecording) return;

        navigator.mediaDevices.getUserMedia({ audio: true })
            .then(stream => {
                mediaStream = stream;
                audioContext = new AudioContext({ sampleRate: SAMPLE_RATE });
                source = audioContext.createMediaStreamSource(stream);
                processor = audioContext.createScriptProcessor(SAMPLES_PER_BUFFER, 1, 1);

                processor.onaudioprocess = function(e) {
                    const inputData = e.inputBuffer.getChannelData(0);
                    if (socket && socket.readyState === WebSocket.OPEN) {
                        socket.send(inputData.buffer);
                        console.log("Sent audio data of size:", inputData.buffer.byteLength);
                    }
                };

                source.connect(processor);
                processor.connect(audioContext.destination);

                isRecording = true;
                status.textContent = "Status: Recording..."; // Update status
                status.style.color = 'greed'; // Set the text color to green
                console.log("Recording started");
            })
            .catch(error => {
                console.error("Error accessing media devices.", error);
            });
    }

    function pauseRecording() {
        // Stop recording but keep the conversation alive
        if (processor) {
            processor.disconnect();
        }
        if (source) {
            source.disconnect();
        }
        if (audioContext) {
            audioContext.suspend(); // Pause the audio context
        }

        isRecording = false;
        status.textContent = "Status: Waiting for response"; // Update status
        status.style.color = 'red'; // Set the text color to red
        console.log("Recording paused");
    }

    function stopConversation() {
        // Stop the audio context, processor, and media stream tracks
        if (processor) {
            processor.disconnect();
        }
        if (source) {
            source.disconnect();
        }
        if (audioContext) {
            audioContext.close();
        }
        if (mediaStream) {
            mediaStream.getTracks().forEach(track => track.stop());
        }

        // If there is an active WebSocket connection, close it
        if (socket && socket.readyState === WebSocket.OPEN) {
            socket.close();
            console.log("WebSocket connection closed.");
        }

        isRecording = false;
        status.textContent = "Status: Idle"; // Update status
    }

</script>
</body>
</html>
