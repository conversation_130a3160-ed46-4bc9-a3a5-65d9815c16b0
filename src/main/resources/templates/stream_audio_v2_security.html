<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Speech to Text Conversation</title>
    <style>
      #status {
        font-weight: bold;
        color: green;
      }
      #mediaContainer {
        margin: 10px 0;
        max-width: 100%;
      }
      #mediaContainer img,
      #mediaContainer video {
        max-width: 100%;
        height: auto;
      }
      .login-container {
        margin: 20px 0;
        padding: 20px;
        border: 1px solid #ccc;
        border-radius: 5px;
      }
      .login-container input {
        margin: 5px 0;
        padding: 5px;
        width: 200px;
      }
      .login-container button {
        margin: 5px 0;
        padding: 5px 10px;
      }
      .hidden {
        display: none;
      }
    </style>
  </head>
  <body>
    <div id="loginSection" class="login-container">
      <h2>Robot Login</h2>
      <div>
        <input type="text" id="deviceId" placeholder="Device ID" />
      </div>
      <div>
        <input type="password" id="password" placeholder="Password" />
      </div>
      <button id="loginButton">Login</button>
      <p id="loginStatus"></p>
    </div>

    <div id="conversationSection" class="hidden">
      <h1>Speech to Text Conversation</h1>
      <button id="startConversationButton">💬 Start Conversation</button>
      <button id="skipButton" disabled>⏭ SKIP</button>
      <p id="status">Status: Idle</p>
      <p id="transcript">Transcript will appear here...</p>
      <p id="response">Response will appear here...</p>
      <div id="mediaContainer"></div>
      <audio id="audioPlayback" controls style="display: none"></audio>
    </div>

    <script>
      const loginSection = document.getElementById("loginSection");
      const conversationSection = document.getElementById(
        "conversationSection"
      );
      const loginButton = document.getElementById("loginButton");
      const loginStatus = document.getElementById("loginStatus");
      const deviceIdInput = document.getElementById("deviceId");
      const passwordInput = document.getElementById("password");
      const startConversationButton = document.getElementById(
        "startConversationButton"
      );
      const skipButton = document.getElementById("skipButton");
      const status = document.getElementById("status");
      const transcript = document.getElementById("transcript");
      const response = document.getElementById("response");

      let socket;
      let audioContext;
      let source;
      let processor;
      let mediaStream;
      const SAMPLE_RATE = 16000;
      const BYTES_PER_SAMPLE = 2;
      const SAMPLES_PER_BUFFER = 4096;
      const BYTES_PER_BUFFER = SAMPLES_PER_BUFFER * BYTES_PER_SAMPLE;
      let isRecording = false;
      let currentAudioPromise = Promise.resolve();
      let skipButtonPressed = false;

      loginButton.addEventListener("click", async () => {
        const deviceId = deviceIdInput.value;
        const password = passwordInput.value;

        if (!deviceId || !password) {
          loginStatus.textContent = "Please enter both device ID and password";
          loginStatus.style.color = "red";
          return;
        }

        try {
          const response = await fetch("/robot/api/v2/device/login", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              deviceId: deviceId,
              password: password,
            }),
          });

          const data = await response.json();

          if (response.ok) {
            // Store the token
            localStorage.setItem("robot_token", data.token);
            loginStatus.textContent = "Login successful!";
            loginStatus.style.color = "green";

            // Show conversation section and hide login section
            loginSection.classList.add("hidden");
            conversationSection.classList.remove("hidden");
          } else {
            loginStatus.textContent = data.error || "Login failed";
            loginStatus.style.color = "red";
          }
        } catch (error) {
          console.error("Login error:", error);
          loginStatus.textContent = "Login failed. Please try again.";
          loginStatus.style.color = "red";
        }
      });

      // Check if already logged in
      if (localStorage.getItem("robot_token")) {
        loginSection.classList.add("hidden");
        conversationSection.classList.remove("hidden");
      }

      startConversationButton.addEventListener("click", () => {
        if (startConversationButton.textContent === "💬 Start Conversation") {
          startConversation();
          startConversationButton.textContent = "⏹ Stop Conversation";
        } else {
          stopConversation();
          startConversationButton.textContent = "💬 Start Conversation";
        }
      });

      skipButton.addEventListener("click", () => {
        if (
          socket &&
          socket.readyState === WebSocket.OPEN &&
          !skipButtonPressed
        ) {
          socket.send(JSON.stringify({ type: "SKIP" }));
          console.log("Send skip signal");
          skipButtonPressed = true;
          skipButton.disabled = true;
        }
      });

      function getWebSocketUrl() {
        const protocol =
          window.location.protocol === "https:" ? "wss://" : "ws://";
        const host = window.location.host;
        return `${protocol}${host}/ws/v2/free_talk?robot_id=quan_web`;
      }

      async function getWebSocketHandshakeToken() {
        const token = localStorage.getItem("robot_token");
        if (!token) {
          throw new Error("No authentication token found");
        }

        try {
          const response = await fetch("/robot/api/v2/device/ws-handshake", {
            method: "POST",
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          });

          if (!response.ok) {
            throw new Error("Handshake failed");
          }

          const data = await response.json();
          return data.wsToken; // Server returns a short-lived token for WebSocket
        } catch (error) {
          console.error("Handshake error:", error);
          throw error;
        }
      }

      async function startConversation() {
        if (!socket || socket.readyState !== WebSocket.OPEN) {
          try {
            let socketUrl = getWebSocketUrl();
            console.log("WebSocket URL:", socketUrl);

            // Get a secure WebSocket token through handshake
            const wsToken = await getWebSocketHandshakeToken();

            // Create WebSocket with the secure token
            socket = new WebSocket(socketUrl);

            // Send the token as the first message after connection
            socket.onopen = function () {
              console.log("WebSocket connected");
              socket.send(
                JSON.stringify({
                  type: "AUTH",
                  token: wsToken,
                })
              );
            //  startRecording(); // Start recording after authentication
            };

            socket.binaryType = "arraybuffer"; // Expect binary data

            socket.onmessage = async function (event) {
              const responseMessage = JSON.parse(event.data);
              if (responseMessage.type === "AUTH_SUCCESS") {
                console.log("WebSocket authentication successful");
              } else if (responseMessage.type === "AUTH_FAILED") {
                console.error("WebSocket authentication failed");
                socket.close();
              } else if (
                responseMessage.type === "ASR" &&
                responseMessage.data.transcript
              ) {
                transcript.innerText = responseMessage.data.transcript;
                if (responseMessage.data.is_stop) {
                  console.log("Received is_stop = true. Pausing recording...");
                  console.log(responseMessage);
                  pauseRecording();
                  status.textContent = "Status: Waiting for response";
                  status.style.color = "red";
                }
              } else if (
                responseMessage.type === "CHAT_RESPONSE" ||
                responseMessage.type === "CHAT_STALLING"
              ) {
                console.log(responseMessage);
                await renderMessagesSequentially(responseMessage.data.messages);

                if (
                  responseMessage.type === "CHAT_RESPONSE" &&
                  !responseMessage.data.has_next_message
                ) {
                  startRecording();
                }
              }
            };

            socket.onclose = function (event) {
              console.log(
                "WebSocket closed with code:",
                event.code,
                "and reason:",
                event.reason
              );
            };

            socket.onerror = function (error) {
              console.error("WebSocket error:", error);
            };
          } catch (error) {
            console.error("Error starting conversation:", error);
          }
        }
      }

      async function renderMessagesSequentially(messages) {
        const mediaContainer = document.getElementById("mediaContainer");

        for (const message of messages) {
          if (message.text) {
            response.innerText = message.text;
          }

          if (message.media) {
            mediaContainer.innerHTML = "";
            if (message.media.type === "IMAGE") {
              const img = document.createElement("img");
              img.src = message.media.url;
              img.alt = "Response Image";
              mediaContainer.appendChild(img);
            } else if (message.media.type === "VIDEO") {
              const video = document.createElement("video");
              video.src = message.media.url;
              video.controls = true;
              video.style.maxWidth = "100%";
              mediaContainer.appendChild(video);
            }
          } else {
            mediaContainer.innerHTML = "";
          }

          if (message.audio) {
            await playAudio(message.audio);
          }
        }
      }

      function playAudio(audioUrl) {
        currentAudioPromise = currentAudioPromise.then(() => {
          return new Promise((resolve) => {
            const audioPlayback = document.getElementById("audioPlayback");
            audioPlayback.src = audioUrl;
            audioPlayback.style.display = "none";

            audioPlayback.onended = null;

            audioPlayback.play();
            audioPlayback.onended = function () {
              resolve();
            };

            audioPlayback.onerror = function () {
              console.error("Audio playback error");
              resolve();
            };
          });
        });

        return currentAudioPromise;
      }

      function startRecording() {
        if (isRecording) return;

        navigator.mediaDevices
          .getUserMedia({ audio: true })
          .then((stream) => {
            mediaStream = stream;
            audioContext = new AudioContext({ sampleRate: SAMPLE_RATE });
            source = audioContext.createMediaStreamSource(stream);
            processor = audioContext.createScriptProcessor(
              SAMPLES_PER_BUFFER,
              1,
              1
            );

            processor.onaudioprocess = function (e) {
              const inputData = e.inputBuffer.getChannelData(0);
              const int16Data = new Int16Array(inputData.length);
              for (let i = 0; i < inputData.length; i++) {
                const s = Math.max(-1, Math.min(1, inputData[i]));
                int16Data[i] = s < 0 ? s * 0x8000 : s * 0x7fff;
              }
              if (socket && socket.readyState === WebSocket.OPEN) {
                socket.send(int16Data.buffer);
                console.log(
                  "Sent audio data of size:",
                  int16Data.buffer.byteLength
                );
              }
            };

            source.connect(processor);
            processor.connect(audioContext.destination);

            isRecording = true;
            skipButtonPressed = false;
            skipButton.disabled = false;
            status.textContent = "Status: Recording...";
            status.style.color = "green";
            console.log("Recording started");
          })
          .catch((error) => {
            console.error("Error accessing media devices.", error);
          });
      }

      function pauseRecording() {
        if (processor) {
          processor.disconnect();
        }
        if (source) {
          source.disconnect();
        }
        if (audioContext) {
          audioContext.suspend();
        }

        isRecording = false;
        skipButton.disabled = true;
        status.textContent = "Status: Waiting for response";
        status.style.color = "red";
        console.log("Recording paused");
      }

      function stopConversation() {
        if (processor) {
          processor.disconnect();
        }
        if (source) {
          source.disconnect();
        }
        if (audioContext) {
          audioContext.close();
        }
        if (mediaStream) {
          mediaStream.getTracks().forEach((track) => track.stop());
        }

        if (socket && socket.readyState === WebSocket.OPEN) {
          socket.close();
          console.log("WebSocket connection closed.");
        }

        isRecording = false;
        status.textContent = "Status: Idle";
      }
    </script>
  </body>
</html>
