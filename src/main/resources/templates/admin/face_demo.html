<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>File Upload - Admin</title>
    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Bootstrap Icons -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css"
      rel="stylesheet"
    />
    <!-- Custom CSS -->
    <link th:href="@{/css/face_demo.css}" rel="stylesheet" />
  </head>
  <body>
    <div class="container mt-5">
      <!-- Upload and Preview Row -->
      <div class="row justify-content-center">
        <!-- Upload Form Column -->
        <div class="col-md-5">
          <div class="card">
            <div class="card-header">
              <h4 class="mb-0">File Upload</h4>
            </div>
            <div class="card-body">
              <form
                id="uploadForm"
                enctype="multipart/form-data"
              >
                <div class="mb-3">
                  <label for="file" class="form-label"
                    >Select File </label
                  >
                  <input
                    type="file"
                    class="form-control"
                    id="file"
                    name="file"
                    accept="*"
                    required
                  />
                  <div class="form-text">
                    Supported formats: *
                  </div>
                </div>
                <button type="submit" class="btn btn-primary" id="uploadButton">
                  <i class="bi bi-upload"></i> Upload
                </button>
              </form>
            </div>
          </div>
        </div>

        <!-- Preview Column -->
        <div class="col-md-5">
          <div id="uploadResult" style="display: none">
            <div class="card">
              <div class="card-header">
                <div class="d-flex align-items-center">
                  <i class="bi bi-check-circle-fill text-success me-2"></i>
                  <h4 class="mb-0">Preview</h4>
                </div>
              </div>
              <div class="card-body">
                <div class="mb-3">
                  <strong>URL:</strong>
                  <a
                    id="fileUrl"
                    href="#"
                    target="_blank"
                    class="text-break"
                  ></a>
                </div>
                <!-- Preview section -->
                <div id="previewSection">
                  <!-- Image/GIF preview -->
                  <img
                    id="imagePreview"
                    class="preview-image"
                    style="display: none"
                    alt="Uploaded file"
                  />
                  <!-- Audio preview -->
                  <div
                    id="audioPreviewContainer"
                    class="audio-container"
                    style="display: none"
                  >
                    <div class="d-flex align-items-center mb-2">
                      <i class="bi bi-music-note-beamed me-2"></i>
                      <span class="text-muted">Audio Preview</span>
                    </div>
                    <audio id="audioPreview" class="audio-player" controls>
                      Your browser does not support the audio element.
                    </audio>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>


    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script th:src="@{/js/face_demo.js}"></script>
  </body>
</html>
