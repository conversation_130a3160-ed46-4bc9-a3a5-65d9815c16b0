<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>File Upload - Admin</title>
    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Bootstrap Icons -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css"
      rel="stylesheet"
    />
    <!-- Custom CSS -->
    <link th:href="@{/css/upload.css}" rel="stylesheet" />
  </head>
  <body>
    <div class="container mt-5">
      <!-- Path Selection Row -->
      <div class="row justify-content-center mb-4">
        <div class="col-md-10">
          <div class="card path-card">
            <div class="card-header">
              <h4 class="mb-0">SD Path</h4>
            </div>
            <div class="card-body">
              <div class="form-group">
                <label for="sdPath" class="form-label"
                  >Choose destination path:</label
                >
                <select class="form-select" id="sdPath" required>
                  <option value="">Loading paths...</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Upload and Preview Row -->
      <div class="row justify-content-center">
        <!-- Upload Form Column -->
        <div class="col-md-5">
          <div class="card">
            <div class="card-header">
              <h4 class="mb-0">File Upload</h4>
            </div>
            <div class="card-body">
              <form
                id="uploadForm"
                method="POST"
                enctype="multipart/form-data"
                th:action="@{/web/admin/upload}"
              >
                <div class="mb-3">
                  <label for="file" class="form-label"
                    >Select File (Images, Audio, or GIF)</label
                  >
                  <input
                    type="file"
                    class="form-control"
                    id="file"
                    name="file"
                    accept="image/*,audio/*,.gif"
                    required
                  />
                  <div class="form-text">
                    Supported formats: JPG, PNG, GIF, MP3, WAV, etc.
                  </div>
                </div>
                <button type="submit" class="btn btn-primary" id="uploadButton">
                  <i class="bi bi-upload"></i> Upload
                </button>
              </form>
            </div>
          </div>
        </div>

        <!-- Preview Column -->
        <div class="col-md-5">
          <div id="uploadResult" style="display: none">
            <div class="card">
              <div class="card-header">
                <div class="d-flex align-items-center">
                  <i class="bi bi-check-circle-fill text-success me-2"></i>
                  <h4 class="mb-0">Preview</h4>
                </div>
              </div>
              <div class="card-body">
                <div class="mb-3">
                  <strong>URL:</strong>
                  <a
                    id="fileUrl"
                    href="#"
                    target="_blank"
                    class="text-break"
                  ></a>
                </div>
                <!-- Preview section -->
                <div id="previewSection">
                  <!-- Image/GIF preview -->
                  <img
                    id="imagePreview"
                    class="preview-image"
                    style="display: none"
                    alt="Uploaded file"
                  />
                  <!-- Audio preview -->
                  <div
                    id="audioPreviewContainer"
                    class="audio-container"
                    style="display: none"
                  >
                    <div class="d-flex align-items-center mb-2">
                      <i class="bi bi-music-note-beamed me-2"></i>
                      <span class="text-muted">Audio Preview</span>
                    </div>
                    <audio id="audioPreview" class="audio-player" controls>
                      Your browser does not support the audio element.
                    </audio>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Robot ID and Update SD Section -->
      <div class="row justify-content-center mt-4">
        <div class="col-md-10">
          <div class="card">
            <div class="card-header">
              <h4 class="mb-0">Update SD</h4>
            </div>
            <div class="card-body">
              <div class="form-group mb-3">
                <label for="robotId" class="form-label">Robot ID</label>
                <input
                  type="text"
                  class="form-control"
                  id="robotId"
                  placeholder="Enter Robot ID"
                />
              </div>
              <button type="button" class="btn btn-primary" id="updateSdButton">
                <i class="bi bi-arrow-clockwise"></i> Update SD
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script th:src="@{/js/upload.js}"></script>
  </body>
</html>
