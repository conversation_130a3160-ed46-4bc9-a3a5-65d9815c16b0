<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Speech to Text Conversation (API Mode)</title>
    <style>
      #status {
        font-weight: bold;
        color: green;
      }
      #mediaContainer {
        margin: 10px 0;
        max-width: 100%;
      }
      #mediaContainer img,
      #mediaContainer video {
        max-width: 100%;
        height: auto;
      }
      #micButton {
        font-size: 24px;
        padding: 10px 20px;
        border-radius: 50%;
        width: 60px;
        height: 60px;
        background-color: #f0f0f0;
        border: 1px solid #ccc;
        cursor: pointer;
        transition: background-color 0.3s;
      }
      #micButton.recording {
        background-color: #ff6b6b;
      }
      .controls {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
        align-items: center;
      }
    </style>
  </head>
  <body>
    <h1>Speech to Text Conversation (API Mode)</h1>
    <div class="controls">
      <button id="startConversationButton">💬 Start Conversation</button>
      <button id="micButton" disabled>🎤</button>
      <button id="skipButton" disabled>⏭ SKIP</button>
    </div>
    <p id="status">Status: Idle</p>
    <p id="transcript">Transcript will appear here...</p>
    <p id="response">Response will appear here...</p>
    <div id="mediaContainer"></div>
    <audio id="audioPlayback" controls style="display: none"></audio>

    <script>
      const startConversationButton = document.getElementById(
        "startConversationButton"
      );
      const micButton = document.getElementById("micButton");
      const skipButton = document.getElementById("skipButton");
      const status = document.getElementById("status");
      const transcript = document.getElementById("transcript");
      const response = document.getElementById("response");

      let socket;
      let mediaRecorder;
      let audioChunks = [];
      let mediaStream;
      let isRecording = false;
      let currentAudioPromise = Promise.resolve(); // Track current audio playback
      let skipButtonPressed = false; // Track if skip button has been pressed during current recording

      startConversationButton.addEventListener("click", () => {
        if (startConversationButton.textContent === "💬 Start Conversation") {
          startConversation();
          startConversationButton.textContent = "⏹ Stop Conversation";
        } else {
          stopConversation();
          startConversationButton.textContent = "💬 Start Conversation";
        }
      });

      micButton.addEventListener("click", () => {
        if (!isRecording) {
          startRecording();
        } else {
          stopRecording();
        }
      });

      skipButton.addEventListener("click", () => {
        if (
          socket &&
          socket.readyState === WebSocket.OPEN &&
          !skipButtonPressed
        ) {
          socket.send(JSON.stringify({ type: "SKIP" }));
          console.log("Send skip signal");
          skipButtonPressed = true;
          skipButton.disabled = true;
        }
      });

      function getWebSocketUrl() {
        const protocol =
          window.location.protocol === "https:" ? "wss://" : "ws://";
        const host = window.location.host; // This will give you the host (domain + port if present)
        return `${protocol}${host}/ws/free_talk?robot_id=quan_web`; // Adjust the path if necessary
      }

      function startConversation() {
        if (!socket || socket.readyState !== WebSocket.OPEN) {
          let socketUrl = getWebSocketUrl();
          console.log("WebSocket URL:", socketUrl);
          socket = new WebSocket(socketUrl);

          console.log("WebSocket connection opened");

          socket.binaryType = "arraybuffer"; // Expect binary data

          socket.onopen = function () {
            console.log("WebSocket connected");
            micButton.disabled = false; // Enable mic button when WebSocket is connected
          };

          socket.onmessage = async function (event) {
            const responseMessage = JSON.parse(event.data);
            if (
              responseMessage.type === "ASR" &&
              responseMessage.data.transcript
            ) {
              transcript.innerText = responseMessage.data.transcript;
              if (responseMessage.data.is_stop) {
                console.log("Received is_stop = true. Waiting for response...");
                console.log(responseMessage);
                status.textContent = "Status: Waiting for response";
                status.style.color = "red";
              }
            } else if (
              responseMessage.type === "CHAT_RESPONSE" ||
              responseMessage.type === "CHAT_STALLING"
            ) {
              console.log(responseMessage);
              // Handle both CHAT_RESPONSE and CHAT_STALLING the same way for playback
              await renderMessagesSequentially(responseMessage.data.messages);

              // Enable mic button after response is fully played
              if (
                responseMessage.type === "CHAT_RESPONSE" &&
                !responseMessage.data.has_next_message
              ) {
                micButton.disabled = false;
                status.textContent = "Status: Ready to record";
                status.style.color = "green";
              }
            }
          };

          socket.onclose = function (event) {
            console.log(
              "WebSocket closed with code:",
              event.code,
              "and reason:",
              event.reason
            );
            micButton.disabled = true;
          };

          socket.onerror = function (error) {
            console.error("WebSocket error:", error);
            micButton.disabled = true;
          };
        }
      }

      // Function to render messages and play audio sequentially
      async function renderMessagesSequentially(messages) {
        const mediaContainer = document.getElementById("mediaContainer");

        for (const message of messages) {
          if (message.text) {
            response.innerText = message.text;
          }

          // Handle media content if present
          if (message.media) {
            mediaContainer.innerHTML = ""; // Clear previous media
            if (message.media.type === "IMAGE") {
              const img = document.createElement("img");
              img.src = message.media.url;
              img.alt = "Response Image";
              mediaContainer.appendChild(img);
            } else if (message.media.type === "VIDEO") {
              const video = document.createElement("video");
              video.src = message.media.url;
              video.controls = true;
              video.style.maxWidth = "100%";
              mediaContainer.appendChild(video);
            }
          } else {
            mediaContainer.innerHTML = ""; // Clear media if none present
          }

          if (message.audio) {
            await playAudio(message.audio);
          }
        }
      }

      // Function to play audio and return a Promise that resolves when playback finishes
      function playAudio(audioUrl) {
        // Wait for any current audio to finish before starting new audio
        currentAudioPromise = currentAudioPromise.then(() => {
          return new Promise((resolve) => {
            const audioPlayback = document.getElementById("audioPlayback");
            audioPlayback.src = audioUrl;
            audioPlayback.style.display = "none";

            // Ensure any existing onended handlers are removed
            audioPlayback.onended = null;

            audioPlayback.play();
            audioPlayback.onended = function () {
              resolve();
            };

            // Also handle cases where audio fails to play
            audioPlayback.onerror = function () {
              console.error("Audio playback error");
              resolve();
            };
          });
        });

        return currentAudioPromise;
      }

      function startRecording() {
        if (isRecording) return;

        audioChunks = []; // Clear previous recording chunks

        navigator.mediaDevices
          .getUserMedia({ audio: true })
          .then((stream) => {
            mediaStream = stream;

            // Create MediaRecorder with WAV format
            // Note: MediaRecorder doesn't directly support WAV, so we'll record in a supported format
            // and convert if needed
            let options;
            try {
              // Try to use audio/wav if supported
              if (MediaRecorder.isTypeSupported("audio/wav")) {
                options = { mimeType: "audio/wav" };
              } else if (MediaRecorder.isTypeSupported("audio/webm")) {
                // Fallback to webm which is widely supported
                options = { mimeType: "audio/webm" };
              }
              mediaRecorder = new MediaRecorder(stream, options);
            } catch (e) {
              console.error(
                "MediaRecorder not supported with these options",
                e
              );
              // Use default options
              mediaRecorder = new MediaRecorder(stream);
            }

            // Set up event handlers for the MediaRecorder
            mediaRecorder.ondataavailable = (event) => {
              if (event.data.size > 0) {
                audioChunks.push(event.data);
              }
            };

            // Start recording
            mediaRecorder.start();
            isRecording = true;
            micButton.classList.add("recording");
            micButton.textContent = "⏹️";
            skipButtonPressed = false; // Reset skip button state
            skipButton.disabled = false; // Enable skip button
            status.textContent = "Status: Recording..."; // Update status
            status.style.color = "green"; // Set the text color to green
            console.log("Recording started");
          })
          .catch((error) => {
            console.error("Error accessing media devices.", error);
          });
      }

      function stopRecording() {
        if (!isRecording || !mediaRecorder) return;

        // Stop the MediaRecorder
        mediaRecorder.stop();

        // Update UI
        isRecording = false;
        micButton.classList.remove("recording");
        micButton.textContent = "🎤";
        micButton.disabled = true; // Disable mic button while processing
        skipButton.disabled = true; // Disable skip button
        status.textContent = "Status: Processing audio..."; // Update status
        status.style.color = "blue"; // Set the text color to blue
        console.log("Recording stopped, processing audio...");

        // When MediaRecorder stops, process the recorded audio
        mediaRecorder.onstop = () => {
          // Create a blob from the audio chunks
          // Use the same MIME type that was used for recording
          const audioBlob = new Blob(audioChunks, {
            type: mediaRecorder.mimeType,
          });

          // Send the audio to the ASR API
          sendAudioToASR(audioBlob);

          // Stop all tracks in the media stream
          if (mediaStream) {
            mediaStream.getTracks().forEach((track) => track.stop());
          }
        };
      }

      // Convert audio blob to WAV format
      async function convertToWav(audioBlob) {
        return new Promise((resolve, reject) => {
          // Create an audio context
          const audioContext = new (window.AudioContext ||
            window.webkitAudioContext)();

          // Create a source from the blob
          const fileReader = new FileReader();

          fileReader.onload = function (event) {
            const arrayBuffer = event.target.result;

            // Decode the audio data
            audioContext.decodeAudioData(
              arrayBuffer,
              function (audioBuffer) {
                // Convert to WAV
                const wavBlob = audioBufferToWav(audioBuffer);
                resolve(new Blob([wavBlob], { type: "audio/wav" }));
              },
              function (error) {
                console.error("Error decoding audio data", error);
                // If conversion fails, return the original blob
                resolve(audioBlob);
              }
            );
          };

          fileReader.onerror = function (error) {
            console.error("Error reading file", error);
            // If reading fails, return the original blob
            resolve(audioBlob);
          };

          fileReader.readAsArrayBuffer(audioBlob);
        });
      }

      // Function to convert AudioBuffer to WAV format
      function audioBufferToWav(audioBuffer) {
        const numOfChannels = audioBuffer.numberOfChannels;
        const length = audioBuffer.length * numOfChannels * 2; // 2 bytes per sample (16-bit)
        const sampleRate = audioBuffer.sampleRate;
        const buffer = new ArrayBuffer(44 + length); // 44 bytes for WAV header
        const view = new DataView(buffer);

        // Write WAV header
        // "RIFF" chunk descriptor
        writeString(view, 0, "RIFF");
        view.setUint32(4, 36 + length, true); // File size - 8
        writeString(view, 8, "WAVE");

        // "fmt " sub-chunk
        writeString(view, 12, "fmt ");
        view.setUint32(16, 16, true); // Sub-chunk size
        view.setUint16(20, 1, true); // Audio format (1 = PCM)
        view.setUint16(22, numOfChannels, true); // Number of channels
        view.setUint32(24, sampleRate, true); // Sample rate
        view.setUint32(28, sampleRate * numOfChannels * 2, true); // Byte rate
        view.setUint16(32, numOfChannels * 2, true); // Block align
        view.setUint16(34, 16, true); // Bits per sample

        // "data" sub-chunk
        writeString(view, 36, "data");
        view.setUint32(40, length, true); // Sub-chunk size

        // Write audio data
        const channels = [];
        for (let i = 0; i < numOfChannels; i++) {
          channels.push(audioBuffer.getChannelData(i));
        }

        let offset = 44;
        for (let i = 0; i < audioBuffer.length; i++) {
          for (let channel = 0; channel < numOfChannels; channel++) {
            // Convert float to 16-bit signed integer
            const sample = Math.max(-1, Math.min(1, channels[channel][i]));
            const int16 = sample < 0 ? sample * 0x8000 : sample * 0x7fff;
            view.setInt16(offset, int16, true);
            offset += 2;
          }
        }

        return buffer;
      }

      // Helper function to write strings to DataView
      function writeString(view, offset, string) {
        for (let i = 0; i < string.length; i++) {
          view.setUint8(offset + i, string.charCodeAt(i));
        }
      }

      async function sendAudioToASR(audioBlob) {
        try {
          // Show conversion status
          status.textContent = "Status: Converting audio format...";

          // Convert to WAV if not already WAV
          let processedBlob = audioBlob;
          if (!audioBlob.type.includes("wav")) {
            try {
              processedBlob = await convertToWav(audioBlob);
            } catch (conversionError) {
              console.error("Error converting audio to WAV:", conversionError);
              // Continue with original format if conversion fails
              console.log("Using original format for upload");
            }
          }

          // Create a FormData object to send the audio file
          const formData = new FormData();
          // Determine the file extension based on the actual blob type
          let fileExtension = "wav";
          if (processedBlob.type.includes("webm")) {
            fileExtension = "webm";
          } else if (
            processedBlob.type.includes("mp4") ||
            processedBlob.type.includes("mp4a")
          ) {
            fileExtension = "mp4";
          } else if (processedBlob.type.includes("ogg")) {
            fileExtension = "ogg";
          }

          formData.append(
            "audio-file",
            processedBlob,
            `recording.${fileExtension}`
          );

          // Update status
          status.textContent = "Status: Sending audio to server...";

          // Send the audio to the ASR API
          return fetch("/robot-speech/api/v1/robot-open/check/asr", {
            method: "POST",
            body: formData,
          })
            .then((response) => {
              if (!response.ok) {
                throw new Error("Network response was not ok");
              }
              return response.json();
            })
            .then((data) => {
              console.log("ASR API response:", data);

              // Check if the response contains the transcription and audio URL
              if (data && data.data && data.data.text) {
                const transcription = data.data.text;
                const audioUrl = data.data.audio_url;

                // Update the transcript display
                transcript.innerText = transcription;

                // Send the transcription and audio URL to the WebSocket as a text message
                if (socket && socket.readyState === WebSocket.OPEN) {
                  const message = {
                    type: "ASR",
                    text: transcription,
                    audio_url: audioUrl,
                  };
                  socket.send(JSON.stringify(message));
                  console.log(
                    "Sent transcription via WebSocket:",
                    transcription
                  );
                  console.log("With audio URL:", audioUrl);

                  status.textContent = "Status: Waiting for response";
                  status.style.color = "red";
                  micButton.disabled = true; // Disable mic button while waiting for response
                } else {
                  console.error("WebSocket not connected");
                  status.textContent =
                    "Status: Error - WebSocket not connected";
                  status.style.color = "red";
                  micButton.disabled = false;
                }
              } else {
                console.error("No transcription in response:", data);
                status.textContent =
                  "Status: Error - No transcription received";
                status.style.color = "red";
                micButton.disabled = false;
              }
            })
            .catch((error) => {
              console.error("Error sending audio to ASR API:", error);
              status.textContent = "Status: Error processing audio";
              status.style.color = "red";
              micButton.disabled = false;
            });
        } catch (error) {
          console.error("Error in sendAudioToASR:", error);
          status.textContent = "Status: Error processing audio";
          status.style.color = "red";
          micButton.disabled = false;
        }
      }

      function stopConversation() {
        // Stop recording if active
        if (
          isRecording &&
          mediaRecorder &&
          mediaRecorder.state === "recording"
        ) {
          mediaRecorder.stop();
        }

        // Stop media stream tracks
        if (mediaStream) {
          mediaStream.getTracks().forEach((track) => track.stop());
        }

        // If there is an active WebSocket connection, close it
        if (socket && socket.readyState === WebSocket.OPEN) {
          socket.close();
          console.log("WebSocket connection closed.");
        }

        // Reset UI
        isRecording = false;
        micButton.classList.remove("recording");
        micButton.textContent = "🎤";
        micButton.disabled = true;
        skipButton.disabled = true;
        status.textContent = "Status: Idle";
        status.style.color = "green";
      }
    </script>
  </body>
</html>
