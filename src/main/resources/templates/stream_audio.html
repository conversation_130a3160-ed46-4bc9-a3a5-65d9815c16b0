<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Speech to Text Conversation</title>
    <style>
      #status {
        font-weight: bold;
        color: green;
      }
    </style>
  </head>
  <body>
    <h1>Speech to Text Conversation</h1>
    <button id="startConversationButton">💬 Start Conversation</button>
    <p id="status">Status: Idle</p>
    <p id="transcript">Transcript will appear here...</p>
    <p id="response">Response will appear here...</p>
    <audio id="audioPlayback" controls style="display: none"></audio>

    <script>
      const startConversationButton = document.getElementById(
        "startConversationButton"
      );
      const status = document.getElementById("status");
      const transcript = document.getElementById("transcript");
      const response = document.getElementById("response");

      let socket;
      let audioContext;
      let source;
      let processor;
      let mediaStream;
      const SAMPLE_RATE = 16000; // 16,000 Hz sample rate
      const BYTES_PER_SAMPLE = 2; // 16-bit integer = 2 bytes per sample
      const SAMPLES_PER_BUFFER = 4096;
      const BYTES_PER_BUFFER = SAMPLES_PER_BUFFER * BYTES_PER_SAMPLE;
      let isRecording = false;
      let currentAudioPromise = Promise.resolve(); // Track current audio playback

      startConversationButton.addEventListener("click", () => {
        if (startConversationButton.textContent === "💬 Start Conversation") {
          startConversation();
          startConversationButton.textContent = "⏹ Stop Conversation";
        } else {
          stopConversation();
          startConversationButton.textContent = "💬 Start Conversation";
        }
      });

      function getWebSocketUrl() {
        const protocol =
          window.location.protocol === "https:" ? "wss://" : "ws://";
        const host = window.location.host; // This will give you the host (domain + port if present)
        return `${protocol}${host}/ws/conversation?robot_id=quan_web`; // Adjust the path if necessary
      }

      function startConversation() {
        if (!socket || socket.readyState !== WebSocket.OPEN) {
          let socketUrl = getWebSocketUrl();
          console.log("WebSocket URL:", socketUrl);
          socket = new WebSocket(socketUrl);

          console.log("WebSocket connection opened");

          socket.binaryType = "arraybuffer"; // Expect binary data

          socket.onopen = function () {
            console.log("WebSocket connected");
            startRecording(); // Start recording when WebSocket is open
          };

          socket.onmessage = async function (event) {
            const responseMessage = JSON.parse(event.data);
            if (
              responseMessage.type === "ASR" &&
              responseMessage.data.transcript
            ) {
              transcript.innerText = responseMessage.data.transcript;
              if (responseMessage.data.is_stop) {
                console.log("Received is_stop = true. Pausing recording...");
                console.log(responseMessage);
                pauseRecording();
                status.textContent = "Status: Waiting for response";
                status.style.color = "red";
              }
            } else if (
              responseMessage.type === "CHAT_RESPONSE" ||
              responseMessage.type === "CHAT_STALLING"
            ) {
              console.log(
                `${responseMessage.type} received.`,
                JSON.stringify(responseMessage)
              );
              // Handle both CHAT_RESPONSE and CHAT_STALLING the same way for playback
              await renderMessagesSequentially(responseMessage.data);

              // Only restart recording after CHAT_RESPONSE messages are fully played
              if (responseMessage.type === "CHAT_RESPONSE") {
                startRecording();
              }
            }
          };

          socket.onclose = function (event) {
            console.log(
              "WebSocket closed with code:",
              event.code,
              "and reason:",
              event.reason
            );
          };

          socket.onerror = function (error) {
            console.error("WebSocket error:", error);
          };
        }
      }

      // Function to render messages and play audio sequentially
      async function renderMessagesSequentially(messages) {
        for (const message of messages) {
          if (message.text) {
            response.innerText = message.text;
          }

          if (message.audio) {
            await playAudio(message.audio);
          }
        }
      }

      // Function to play audio and return a Promise that resolves when playback finishes
      function playAudio(audioUrl) {
        // Wait for any current audio to finish before starting new audio
        currentAudioPromise = currentAudioPromise.then(() => {
          return new Promise((resolve) => {
            const audioPlayback = document.getElementById("audioPlayback");
            audioPlayback.src = audioUrl;
            audioPlayback.style.display = "none";

            // Ensure any existing onended handlers are removed
            audioPlayback.onended = null;

            audioPlayback.play();
            audioPlayback.onended = function () {
              resolve();
            };

            // Also handle cases where audio fails to play
            audioPlayback.onerror = function () {
              console.error("Audio playback error");
              resolve();
            };
          });
        });

        return currentAudioPromise;
      }

      function startRecording() {
        if (isRecording) return;

        navigator.mediaDevices
          .getUserMedia({ audio: true })
          .then((stream) => {
            mediaStream = stream;
            audioContext = new AudioContext({ sampleRate: SAMPLE_RATE });
            source = audioContext.createMediaStreamSource(stream);
            processor = audioContext.createScriptProcessor(
              SAMPLES_PER_BUFFER,
              1,
              1
            );

            processor.onaudioprocess = function (e) {
              const inputData = e.inputBuffer.getChannelData(0);
              // Convert Float32Array to Int16Array
              const int16Data = new Int16Array(inputData.length);
              for (let i = 0; i < inputData.length; i++) {
                // Convert float [-1.0, 1.0] to int16 [-32768, 32767]
                const s = Math.max(-1, Math.min(1, inputData[i]));
                int16Data[i] = s < 0 ? s * 0x8000 : s * 0x7fff;
              }
              if (socket && socket.readyState === WebSocket.OPEN) {
                socket.send(int16Data.buffer);
                console.log(
                  "Sent audio data of size:",
                  int16Data.buffer.byteLength
                );
              }
            };

            source.connect(processor);
            processor.connect(audioContext.destination);

            isRecording = true;
            status.textContent = "Status: Recording..."; // Update status
            status.style.color = "greed"; // Set the text color to green
            console.log("Recording started");
          })
          .catch((error) => {
            console.error("Error accessing media devices.", error);
          });
      }

      function pauseRecording() {
        // Stop recording but keep the conversation alive
        if (processor) {
          processor.disconnect();
        }
        if (source) {
          source.disconnect();
        }
        if (audioContext) {
          audioContext.suspend(); // Pause the audio context
        }

        isRecording = false;
        status.textContent = "Status: Waiting for response"; // Update status
        status.style.color = "red"; // Set the text color to red
        console.log("Recording paused");
      }

      function stopConversation() {
        // Stop the audio context, processor, and media stream tracks
        if (processor) {
          processor.disconnect();
        }
        if (source) {
          source.disconnect();
        }
        if (audioContext) {
          audioContext.close();
        }
        if (mediaStream) {
          mediaStream.getTracks().forEach((track) => track.stop());
        }

        // If there is an active WebSocket connection, close it
        if (socket && socket.readyState === WebSocket.OPEN) {
          socket.close();
          console.log("WebSocket connection closed.");
        }

        isRecording = false;
        status.textContent = "Status: Idle"; // Update status
      }
    </script>
  </body>
</html>
