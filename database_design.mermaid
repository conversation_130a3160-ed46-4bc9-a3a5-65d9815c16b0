erDiagram
    %% User Management
    Users ||--o{ Profiles : manages
    Users {
        uuid id PK
        string email
        string password_hash
        string full_name
        string phone
        boolean is_active
        datetime created_at
        datetime updated_at
    }

    Profiles ||--o{ ProfileProducts : has
    Profiles ||--o{ ProfileHiddenLessons : customizes
    Profiles ||--o{ ProfileLevelHistory : tracks_changes
    Profiles {
        uuid id PK
        uuid user_id FK
        uuid level_id FK "Current level"
        string name
        date birth_date
        string gender
        boolean is_current_profile
        datetime created_at 
        datetime updated_at
    }

    %% Product Management
    Products ||--o{ ProfileProducts : assigned_to
    Products {
        uuid id PK
        string name
        string type "robot/app/other"
        string description
        boolean is_active
        decimal price
        json specifications
        datetime created_at
        datetime updated_at
    }

    ProfileProducts {
        uuid id PK
        uuid profile_id FK
        uuid product_id FK
        string serial_number
        datetime activation_date
        datetime expiration_date
        boolean is_active
        datetime created_at
        datetime updated_at
    }

    %% Level History
    ProfileLevelHistory {
        uuid id PK
        uuid profile_id FK
        uuid level_id FK
        string change_reason "initial/reassessment/user_request"
        datetime changed_at
        datetime created_at
        datetime updated_at
    }

    %% Content Structure
    Levels ||--o{ Units : contains
    Levels {
        uuid id PK
        string code "enum(A0,A1,A2,B1,B2,C1,C2)"
        string name
        string description
        datetime created_at
        datetime updated_at
    }

    Units ||--o{ Lessons : contains
    Units {
        uuid id PK
        uuid level_id FK
        string name
        string subtitle
        string description
        int order_index
        boolean is_active
        datetime created_at
        datetime updated_at
    }

    Lessons ||--o{ LessonActivities : contains
    Lessons ||--o{ ProfileHiddenLessons : hidden_by
    Lessons {
        uuid id PK
        uuid unit_id FK
        string name
        string description
        string thumbnail_url
        int duration_minutes
        int order_index
        boolean is_active
        datetime created_at
        datetime updated_at
    }

    %% Profile's Hidden/Customized Lessons
    ProfileHiddenLessons {
        uuid id PK
        uuid profile_id FK
        uuid lesson_id FK
        int custom_order "NULL if using default order"
        datetime hidden_at "NULL if visible"
        datetime created_at
        datetime updated_at
    }

    %% Learning Activities
    Skills {
        uuid id PK
        string name "listening/speaking/reading/writing"
        string description
        string icon_url
        datetime created_at
        datetime updated_at
    }

    Activities ||--o{ LessonActivities : used_in
    Activities {
        uuid id PK
        uuid skill_id FK
        string name
        string description
        string type "question/exercise/game/etc"
        json content
        json requirements
        int points
        boolean is_active
        datetime created_at
        datetime updated_at
    }

    LessonActivities {
        uuid id PK
        uuid lesson_id FK
        uuid activity_id FK
        int order_index
        boolean is_required
        datetime created_at
        datetime updated_at
    }

    %% Progress Tracking
    DailyProgress {
        uuid id PK
        uuid profile_id FK
        date progress_date
        int study_minutes
        int completed_activities
        datetime created_at
        datetime updated_at
    }

    VocabularyProgress {
        uuid id PK
        uuid profile_id FK
        string word
        string phonetic
        string meaning
        int mastery_level "0-100"
        datetime last_reviewed
        datetime created_at
        datetime updated_at
    }

    GrammarProgress {
        uuid id PK
        uuid profile_id FK
        string structure
        string example
        int mastery_level "0-100"
        datetime last_reviewed
        datetime created_at
        datetime updated_at
    }

    PronunciationProgress {
        uuid id PK
        uuid profile_id FK
        string phoneme
        int accuracy_score "0-100"
        datetime last_practiced
        json detailed_scores
        datetime created_at
        datetime updated_at
    }

    WeeklyStats {
        uuid id PK
        uuid profile_id FK
        date week_start
        date week_end
        int total_study_minutes
        int vocabulary_progress
        int grammar_progress
        float pronunciation_accuracy
        json daily_breakdown
        datetime created_at
        datetime updated_at
    }

    ProfileSkillLevels {
        uuid id PK
        uuid profile_id FK
        uuid skill_id FK
        int score "0-100"
        datetime last_updated_at
        datetime created_at
        datetime updated_at
    }

    LessonProgress {
        uuid id PK
        uuid profile_id FK
        uuid lesson_id FK
        int completion_percentage
        string status "not_started/in_progress/completed"
        datetime started_at
        datetime completed_at
        datetime created_at
        datetime updated_at
    }

    ActivityProgress {
        uuid id PK
        uuid profile_id FK
        uuid lesson_activity_id FK
        int score
        string status "not_started/in_progress/completed"
        json answer_details
        datetime started_at
        datetime completed_at
        datetime created_at
        datetime updated_at
    }
